<VirtualHost *:80>
    ServerName parabot.fun
    ServerAlias www.parabot.fun

    DocumentRoot "/var/www/html"

    <Directory "/var/www/html">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    Redirect "/" "https://parabot.fun/" 
</VirtualHost>

<VirtualHost *:443>
    ServerName parabot.fun
    
    # SSL Sertifika Ayarları
    SSLEngine on
    SSLCertificateFile /etc/letsencrypt/live/parabot.fun/fullchain.pem
    SSLCertificateKeyFile /etc/letsencrypt/live/parabot.fun/privkey.pem
    
    # SSL Proxy Motorunu aç
    SSLProxyEngine On
    
    # Statik dosyalar
    DocumentRoot /var/www/html
    
    # HTTP isteklerini Node.js'e yönlendir (3001 portu)
    ProxyPreserveHost On
    ProxyRequests Off
    
    # HTTP proxy ayarı
    ProxyPass /api https://localhost:3001/api
    ProxyPassReverse /api https://localhost:3001/api
    
    ProxyPass /health https://localhost:3001/health
    ProxyPassReverse /health https://localhost:3001/health
    
    # WebSocket proxy ayarı (wss:// için önemli!)
    ProxyPass /ws-fapi wss://localhost:3001/ws-fapi
    ProxyPassReverse /ws-fapi wss://localhost:3001/ws-fapi
    
    # CORS Başlıkları (Node.js'de ayarlasanız bile ek güvenlik için)
    Header always set Access-Control-Allow-Origin "https://parabot.fun"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-MBX-APIKEY, X-Requested-With"
    Header always set Access-Control-Allow-Credentials "true"
    
    # OPTIONS isteklerine 200 yanıtı
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
    
    # Loglar
    ErrorLog ${APACHE_LOG_DIR}/parabot-error.log
    CustomLog ${APACHE_LOG_DIR}/parabot-access.log combined
</VirtualHost>
