export class ConfigManager {
    constructor(config = null) {
        this.config = config || {
            apiKey: '',
            secretKey: '',
            testMode: true
        };
    }

    getApiKey() {
        return process.env.REACT_APP_BINANCE_API_KEY || this.config.apiKey;
    }

    getSecretKey() {
        return process.env.REACT_APP_BINANCE_SECRET_KEY || this.config.secretKey;
    }

    isTestMode() {
        if (process.env.REACT_APP_BINANCE_TEST_MODE !== undefined) {
            return process.env.REACT_APP_BINANCE_TEST_MODE === 'true';
        }
        return this.config.testMode !== false;
    }
}




export class TradingRulesManager {
    constructor(rules = null) {
        this.rules = rules || [];
    }

    getRuleForSymbol(symbol) {
        const baseSymbol = symbol.replace(/USDT$|USDC$|BUSD$/, '');
        let rule = this.rules.find(r => r.symbol === baseSymbol);

        if (!rule) {
            rule = this.rules.find(r => {
                if (r.contract_size && typeof r.contract_size === 'string') {
                    return r.contract_size.includes(baseSymbol);
                }
                return false;
            });
        }

        return rule;
    }

    getMinOrderQuantity(symbol) {
        const rule = this.getRuleForSymbol(symbol);
        if (!rule || !rule.contract_size) return null;

        const match = rule.contract_size.match(/([0-9.]+)/);
        return match ? parseFloat(match[1]) : null;
    }

    getTickSize(symbol) {
        const rule = this.getRuleForSymbol(symbol);
        if (!rule || !rule.tick_size) return null;

        return parseFloat(rule.tick_size);
    }

    getMinOrderValue(symbol) {
        const rule = this.getRuleForSymbol(symbol);
        if (!rule || !rule.min_order_value) return 5;

        const match = rule.min_order_value.match(/([0-9.]+)/);
        return match ? parseFloat(match[1]) : 5;
    }

    getMaxLeverage(symbol) {
        const rule = this.getRuleForSymbol(symbol);
        if (!rule || !rule.leverage) return 20;

        return parseInt(rule.leverage);
    }

    validateOrder(symbol, quantity, price) {
        const minQty = this.getMinOrderQuantity(symbol);
        const minValue = this.getMinOrderValue(symbol);

        if (!minQty) {
            console.warn(`⚠️ ${symbol} için minimum işlem miktarı bulunamadı, doğrulama yapılamıyor`);
            return true;
        }

        if (quantity < minQty) {
            console.error(`❌ İşlem miktarı çok düşük! Minimum: ${minQty} ${symbol}`);
            return false;
        }

        if (price && price * quantity < minValue) {
            console.error(`❌ İşlem değeri çok düşük! Minimum: ${minValue} USDT`);
            return false;
        }

        return true;
    }
}


import CryptoJS from 'crypto-js';

export class BinanceFuturesWebSocket {
    constructor(apiKey, secretKey, rules = null) {
        this.apiKey = apiKey;
        this.secretKey = secretKey;
        this.ws = null;
        this.baseUrl = 'wss://ws-fapi.binance.com/ws-fapi/v1';
        this.messageId = 1;
        this.tradingRules = new TradingRulesManager(rules);
        this.listeners = {
            open: [],
            message: [],
            error: [],
            close: []
        };
        console.log('BinanceFuturesWebSocket sınıfı başlatıldı');
    }

    connect() {
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(this.baseUrl);

                this.ws.onopen = () => {
                    console.log('🚀 Binance Futures WebSocket bağlantısı kuruldu!');
                    this.listeners.open.forEach(callback => callback());
                    resolve();
                };

                this.ws.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    this.handleMessage(message);
                    this.listeners.message.forEach(callback => callback(message));
                };

                this.ws.onerror = (error) => {
                    console.error('❌ WebSocket Hatası:', error);
                    this.listeners.error.forEach(callback => callback(error));
                    reject(error);
                };

                this.ws.onclose = () => {
                    console.log('🔌 WebSocket bağlantısı kapandı');
                    this.listeners.close.forEach(callback => callback());
                };
            } catch (error) {
                console.error('WebSocket oluşturma hatası:', error);
                reject(error);
            }
        });
    }

    on(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event].push(callback);
        }
    }

    off(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
        }
    }

    handleMessage(message) {
        console.log('\n📨 Gelen Mesaj:', message);

        if (message.status === 200) {
            console.log('✅ İşlem başarılı!');
        } else if (message.status !== 200 && message.status) {
            console.log('⚠️ İşlem hatası:', message.error || 'Bilinmeyen hata');
        }
    }

    createSignature(params) {
        const queryString = Object.keys(params)
            .sort()
            .map(key => `${key}=${params[key]}`)
            .join('&');

        return CryptoJS.HmacSHA256(queryString, this.secretKey).toString(CryptoJS.enc.Hex);
    }

    sendMessage(method, params = {}) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            console.error('❌ WebSocket bağlantısı aktif değil!');
            return false;
        }

        params.apiKey = this.apiKey;
        params.timestamp = Date.now();
        params.signature = this.createSignature(params);

        const message = {
            id: this.generateId(),
            method,
            params
        };

        console.log('\n📤 Gönderilen Mesaj:', message);
        this.ws.send(JSON.stringify(message));
        return true;
    }

    generateId() {
        return `msg_${this.messageId++}_${Date.now()}`;
    }

    // Diğer metodlar (placeLimitOrder, placeMarketOrder vb.) aynı şekilde kalabilir
    // Sadece console.log'ları ve event dinleyicileri React uyumlu hale getirildi
    placeLimitOrder(symbol, side, quantity, price, positionSide = 'BOTH') {
        console.log(`\n🔷 LIMIT EMİR VERİLİYOR: ${side} ${quantity} ${symbol} @ ${price} (${positionSide})`);

        if (!this.tradingRules.validateOrder(symbol, quantity, price)) {
            console.error('❌ Emir trading kurallarına uymuyor, işlem iptal edildi!');
            return false;
        }

        const params = {
            symbol,
            side,
            type: 'LIMIT',
            timeInForce: 'GTC',
            quantity,
            price,
            positionSide
        };

        return this.sendMessage('order.place', params);
    }

    placeMarketOrder(symbol, side, quantity, positionSide = 'BOTH') {
        console.log(`\n🔷 MARKET EMİR VERİLİYOR: ${side} ${quantity} ${symbol} (${positionSide})`);

        if (!this.tradingRules.validateOrder(symbol, quantity)) {
            console.error('❌ Emir trading kurallarına uymuyor, işlem iptal edildi!');
            return false;
        }

        const params = {
            symbol,
            side,
            type: 'MARKET',
            quantity,
            positionSide
        };

        return this.sendMessage('order.place', params);
    }

    // Diğer order fonksiyonları da benzer şekilde uyarlanabilir
    // ...

    disconnect() {
        if (this.ws) {
            this.ws.close();
            console.log('🔌 WebSocket bağlantısı kapatıldı');
        }
    }
}

import { useState, useEffect, useCallback } from 'react';
import { BinanceFuturesWebSocket, ConfigManager } from './BinanceFuturesWebSocket';

export function useBinanceFutures(config, rules) {
    const [client, setClient] = useState(null);
    const [isConnected, setIsConnected] = useState(false);
    const [messages, setMessages] = useState([]);
    const [error, setError] = useState(null);

    const configManager = new ConfigManager(config);
    const apiKey = configManager.getApiKey();
    const secretKey = configManager.getSecretKey();

    const connect = useCallback(async () => {
        if (!apiKey || !secretKey) {
            setError('API anahtarları bulunamadı!');
            return;
        }

        try {
            const newClient = new BinanceFuturesWebSocket(apiKey, secretKey, rules);

            newClient.on('message', (message) => {
                setMessages(prev => [...prev, message]);
            });

            newClient.on('error', (err) => {
                setError(err);
            });

            newClient.on('open', () => {
                setIsConnected(true);
            });

            newClient.on('close', () => {
                setIsConnected(false);
            });

            await newClient.connect();
            setClient(newClient);
        } catch (err) {
            setError(err);
        }
    }, [apiKey, secretKey, rules]);

    const disconnect = useCallback(() => {
        if (client) {
            client.disconnect();
            setClient(null);
            setIsConnected(false);
        }
    }, [client]);

    useEffect(() => {
        return () => {
            disconnect();
        };
    }, [disconnect]);

    return {
        client,
        isConnected,
        messages,
        error,
        connect,
        disconnect
    };
}


import React, { useState } from 'react';
import { useBinanceFutures } from './useBinanceFutures';

export function BinanceFuturesPanel({ config, rules }) {
    const [symbol, setSymbol] = useState('BTCUSDT');
    const [quantity, setQuantity] = useState('0.001');
    const [price, setPrice] = useState('');
    const [side, setSide] = useState('BUY');

    const {
        client,
        isConnected,
        messages,
        error,
        connect,
        disconnect
    } = useBinanceFutures(config, rules);

    const handlePlaceOrder = () => {
        if (!client || !isConnected) return;

        if (price) {
            client.placeLimitOrder(symbol, side, parseFloat(quantity), parseFloat(price));
        } else {
            client.placeMarketOrder(symbol, side, parseFloat(quantity));
        }
    };

    return (
        <div className="binance-futures-panel">
            <h2>Binance Futures WebSocket</h2>

            <div className="connection-status">
                Status: {isConnected ? 'Connected' : 'Disconnected'}
                {!isConnected ? (
                    <button onClick={connect}>Connect</button>
                ) : (
                    <button onClick={disconnect}>Disconnect</button>
                )}
            </div>

            {error && <div className="error">{error.message}</div>}

            <div className="order-form">
                <select value={symbol} onChange={(e) => setSymbol(e.target.value)}>
                    <option value="BTCUSDT">BTC/USDT</option>
                    <option value="ETHUSDT">ETH/USDT</option>
                    <option value="SOLUSDT">SOL/USDT</option>
                </select>

                <select value={side} onChange={(e) => setSide(e.target.value)}>
                    <option value="BUY">BUY</option>
                    <option value="SELL">SELL</option>
                </select>

                <input
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(e.target.value)}
                    placeholder="Quantity"
                />

                <input
                    type="number"
                    value={price}
                    onChange={(e) => setPrice(e.target.value)}
                    placeholder="Price (leave empty for market)"
                />

                <button onClick={handlePlaceOrder} disabled={!isConnected}>
                    Place Order
                </button>
            </div>

            <div className="messages">
                <h3>Messages:</h3>
                <ul>
                    {messages.map((msg, i) => (
                        <li key={i}>{JSON.stringify(msg)}</li>
                    ))}
                </ul>
            </div>
        </div>
    );
}

