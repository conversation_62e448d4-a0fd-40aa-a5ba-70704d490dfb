import WebSocket from 'ws';
import crypto from 'crypto';


class BinanceFuturesWebSocket {
    constructor(apiKey, secretKey) {
        this.apiKey = apiKey;
        this.secretKey = secretKey;
        this.ws = null;
        this.baseUrl = 'wss://ws-fapi.binance.com/ws-fapi/v1';
        this.messageId = 1;
    }

    // WebSocket bağlantısını başlat
    connect() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket(this.baseUrl);

            this.ws.on('open', () => {
                console.log('🚀 Binance Futures WebSocket bağlantısı kuruldu!');
                resolve();
            });

            this.ws.on('message', (data) => {
                const message = JSON.parse(data.toString());
                this.handleMessage(message);
            });

            this.ws.on('error', (error) => {
                console.error('❌ WebSocket Hatası:', error);
                reject(error);
            });

            this.ws.on('close', () => {
                console.log('🔌 WebSocket bağlantısı kapandı');
            });
        });
    }

    // Mesajları işle
    handleMessage(message) {
        console.log('\n📨 Gelen Mesaj:');
        console.log(JSON.stringify(message, null, 2));

        if (message.status === 200) {
            console.log('✅ İşlem başarılı!');
        } else if (message.status !== 200 && message.status) {
            console.log('⚠️ İşlem hatası:', message.error || 'Bilinmeyen hata');
        }
    }

    // İmza oluştur
    createSignature(params) {
        const queryString = Object.keys(params)
            .sort()
            .map(key => `${key}=${params[key]}`)
            .join('&');

        return crypto
            .createHmac('sha256', this.secretKey)
            .update(queryString)
            .digest('hex');
    }

    // Mesaj gönder
    sendMessage(method, params = {}) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            console.error('❌ WebSocket bağlantısı aktif değil!');
            return;
        }

        // API key ve timestamp ekle
        params.apiKey = this.apiKey;
        params.timestamp = Date.now();

        // İmza oluştur ve ekle
        params.signature = this.createSignature(params);

        const message = {
            id: this.generateId(),
            method: method,
            params: params
        };

        console.log('\n📤 Gönderilen Mesaj:');
        console.log(JSON.stringify(message, null, 2));

        this.ws.send(JSON.stringify(message));
    }

    // Benzersiz ID oluştur
    generateId() {
        return `msg_${this.messageId++}_${Date.now()}`;
    }

    // 1. Yeni Emir Ver (LIMIT)
    placeLimitOrder(symbol, side, quantity, price, positionSide = 'BOTH') {
        console.log(`\n🔷 LIMIT EMİR VERİLİYOR: ${side} ${quantity} ${symbol} @ ${price}`);

        const params = {
            symbol: symbol,
            side: side,
            type: 'LIMIT',
            timeInForce: 'GTC',
            quantity: quantity,
            price: price,
            positionSide: positionSide
        };

        this.sendMessage('order.place', params);
    }

    // 2. Yeni Emir Ver (MARKET)
    placeMarketOrder(symbol, side, quantity, positionSide = 'BOTH') {
        console.log(`\n🔷 MARKET EMİR VERİLİYOR: ${side} ${quantity} ${symbol}`);

        const params = {
            symbol: symbol,
            side: side,
            type: 'MARKET',
            quantity: quantity,
            positionSide: positionSide
        };

        this.sendMessage('order.place', params);
    }

    // 3. Stop Loss Emri Ver
    placeStopLossOrder(symbol, side, quantity, stopPrice, positionSide = 'BOTH') {
        console.log(`\n🔷 STOP LOSS EMİR VERİLİYOR: ${side} ${quantity} ${symbol} @ ${stopPrice}`);

        const params = {
            symbol: symbol,
            side: side,
            type: 'STOP_MARKET',
            quantity: quantity,
            stopPrice: stopPrice,
            positionSide: positionSide
        };

        this.sendMessage('order.place', params);
    }

    // 4. Take Profit Emri Ver
    placeTakeProfitOrder(symbol, side, quantity, stopPrice, positionSide = 'BOTH') {
        console.log(`\n🔷 TAKE PROFIT EMİR VERİLİYOR: ${side} ${quantity} ${symbol} @ ${stopPrice}`);

        const params = {
            symbol: symbol,
            side: side,
            type: 'TAKE_PROFIT_MARKET',
            quantity: quantity,
            stopPrice: stopPrice,
            positionSide: positionSide
        };

        this.sendMessage('order.place', params);
    }

    // 5. Trailing Stop Emri Ver
    placeTrailingStopOrder(symbol, side, quantity, callbackRate, activationPrice = null, positionSide = 'BOTH') {
        console.log(`\n🔷 TRAILING STOP EMİR VERİLİYOR: ${side} ${quantity} ${symbol}`);

        const params = {
            symbol: symbol,
            side: side,
            type: 'TRAILING_STOP_MARKET',
            quantity: quantity,
            callbackRate: callbackRate,
            positionSide: positionSide
        };

        if (activationPrice) {
            params.activationPrice = activationPrice;
        }

        this.sendMessage('order.place', params);
    }

    // 6. Emir Düzenle
    modifyOrder(symbol, orderId, quantity, price, side, positionSide = 'BOTH') {
        console.log(`\n🔷 EMİR DÜZENLENİYOR: Order ID ${orderId}`);

        const params = {
            symbol: symbol,
            orderId: orderId,
            quantity: quantity,
            price: price,
            side: side,
            positionSide: positionSide
        };

        this.sendMessage('order.modify', params);
    }

    // 7. Emir İptal Et
    cancelOrder(symbol, orderId) {
        console.log(`\n🔷 EMİR İPTAL EDİLİYOR: Order ID ${orderId}`);

        const params = {
            symbol: symbol,
            orderId: orderId
        };

        this.sendMessage('order.cancel', params);
    }

    // 8. Emir Durumu Sorgula
    queryOrderStatus(symbol, orderId) {
        console.log(`\n🔷 EMİR DURUMU SORGULANIYIOR: Order ID ${orderId}`);

        const params = {
            symbol: symbol,
            orderId: orderId
        };

        this.sendMessage('order.status', params);
    }

    // 9. Pozisyon Bilgisi Al (V1)
    getPositionInfo(symbol = null) {
        console.log(`\n🔷 POZİSYON BİLGİSİ ALINIYOR${symbol ? ': ' + symbol : ''}`);

        const params = {};
        if (symbol) {
            params.symbol = symbol;
        }

        this.sendMessage('account.position', params);
    }

    // 10. Pozisyon Bilgisi Al (V2)
    getPositionInfoV2(symbol = null) {
        console.log(`\n🔷 POZİSYON BİLGİSİ V2 ALINIYOR${symbol ? ': ' + symbol : ''}`);

        const params = {};
        if (symbol) {
            params.symbol = symbol;
        }

        this.sendMessage('v2/account.position', params);
    }

    // Bağlantıyı kapat
    disconnect() {
        if (this.ws) {
            this.ws.close();
            console.log('🔌 WebSocket bağlantısı kapatıldı');
        }
    }
}

// Kullanım örneği ve test fonksiyonları
async function main() {
    // API bilgilerinizi buraya girin
    const API_KEY = 'tcRufjk6HSy37EEP0LjTvqXNCkDt81H2QSOwdUhqVq5kn3EhJMdYza0RijWjE1Ge';
    const SECRET_KEY = 'RHTzJXVyWjfEWZw0BYRTErEq4pZVfjKwaHBUzg0Up4WBGP2WGvvJveDBN6pEXJuS';

    const client = new BinanceFuturesWebSocket(API_KEY, SECRET_KEY);

    try {
        // WebSocket bağlantısını kur
        await client.connect();

        // Test menüsü
        console.log('\n🎯 BINANCE FUTURES WEBSOCKET TEST MENÜSÜ');
        console.log('==========================================');
        console.log('Aşağıdaki fonksiyonları test edebilirsiniz:');
        console.log('');

        // 5 saniye bekle, sonra örnek işlemler yap
        setTimeout(() => {
            console.log('\n🧪 TEST İŞLEMLERİ BAŞLATILIJYOR...');

            // Örnek 1: Pozisyon bilgisi al
            client.getPositionInfo('BTCUSDT');

            // 2 saniye sonra V2 pozisyon bilgisi al
            setTimeout(() => {
                client.getPositionInfoV2('BTCUSDT');
            }, 2000);

            // 4 saniye sonra limit emir ver (test - gerçek işlem yapmayacak çünkü test API keyleri)
            setTimeout(() => {
                // client.placeLimitOrder('BTCUSDT', 'BUY', 0.001, 40000);
                console.log('💡 Limit emir örneği (yorum satırında)');
            }, 4000);

        }, 2000);

        // 30 saniye sonra bağlantıyı kapat
        setTimeout(() => {
            client.disconnect();
            process.exit(0);
        }, 30000);

    } catch (error) {
        console.error('❌ Bağlantı hatası:', error);
    }
}

// Programı çalıştır
if (require.main === module) {
    main();
}

// Manuel test fonksiyonları - bu fonksiyonları kendi testleriniz için kullanabilirsiniz
function runManualTests() {
    const client = new BinanceFuturesWebSocket('YOUR_API_KEY', 'YOUR_SECRET_KEY');

    client.connect().then(() => {
        console.log('\n🔧 MANUEL TEST MOD AÇIK');
        console.log('Bu fonksiyonları terminalden çağırabilirsiniz:');
        console.log('');
        console.log('// Pozisyon bilgisi');
        console.log('client.getPositionInfo("BTCUSDT");');
        console.log('');
        console.log('// Limit emir ver');
        console.log('client.placeLimitOrder("BTCUSDT", "BUY", 0.001, 40000);');
        console.log('');
        console.log('// Market emir ver');
        console.log('client.placeMarketOrder("BTCUSDT", "BUY", 0.001);');
        console.log('');
        console.log('// Stop loss ver');
        console.log('client.placeStopLossOrder("BTCUSDT", "SELL", 0.001, 39000);');
        console.log('');
        console.log('// Emir iptal et');
        console.log('client.cancelOrder("BTCUSDT", ORDER_ID);');

        // Global olarak erişilebilir yap
        global.client = client;
    });
}

module.exports = { BinanceFuturesWebSocket, runManualTests };