New Order(TRADE)
API Description
Send in a new order.

Method
order.place

Request
{
    "id": "3f7df6e3-2df4-44b9-9919-d2f38f90a99a",
    "method": "order.place",
    "params": {
        "apiKey": "HMOchcfii9ZRZnhjp2XjGXhsOBd6msAhKz9joQaWwZ7arcJTlD2hGPHQj1lGdTjR",
        "positionSide": "BOTH",
        "price": 43187.00,
        "quantity": 0.1,
        "side": "BUY",
        "symbol": "BTCUSDT",
        "timeInForce": "GTC",
        "timestamp": 1702555533821,
        "type": "LIMIT",
        "signature": "0f04368b2d22aafd0ggc8809ea34297eff602272917b5f01267db4efbc1c9422"
    }
}

Request Weight
0

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	YES	
side	ENUM	YES	
positionSide	ENUM	NO	Default BOTH for One-way Mode ; LONG or SHORT for Hedge Mode. It must be sent in Hedge Mode.
type	ENUM	YES	
timeInForce	ENUM	NO	
quantity	DECIMAL	NO	Cannot be sent with closePosition=true(Close-All)
reduceOnly	STRING	NO	"true" or "false". default "false". Cannot be sent in Hedge Mode; cannot be sent with closePosition=true
price	DECIMAL	NO	
newClientOrderId	STRING	NO	A unique id among open orders. Automatically generated if not sent. Can only be string following the rule: ^[\.A-Z\:/a-z0-9_-]{1,36}$
stopPrice	DECIMAL	NO	Used with STOP/STOP_MARKET or TAKE_PROFIT/TAKE_PROFIT_MARKET orders.
closePosition	STRING	NO	true, false；Close-All，used with STOP_MARKET or TAKE_PROFIT_MARKET.
activationPrice	DECIMAL	NO	Used with TRAILING_STOP_MARKET orders, default as the latest price(supporting different workingType)
callbackRate	DECIMAL	NO	Used with TRAILING_STOP_MARKET orders, min 0.1, max 10 where 1 for 1%
workingType	ENUM	NO	stopPrice triggered by: "MARK_PRICE", "CONTRACT_PRICE". Default "CONTRACT_PRICE"
priceProtect	STRING	NO	"TRUE" or "FALSE", default "FALSE". Used with STOP/STOP_MARKET or TAKE_PROFIT/TAKE_PROFIT_MARKET orders.
newOrderRespType	ENUM	NO	"ACK", "RESULT", default "ACK"
priceMatch	ENUM	NO	only avaliable for LIMIT/STOP/TAKE_PROFIT order; can be set to OPPONENT/ OPPONENT_5/ OPPONENT_10/ OPPONENT_20: /QUEUE/ QUEUE_5/ QUEUE_10/ QUEUE_20; Can't be passed together with price
selfTradePreventionMode	ENUM	NO	NONE:No STP / EXPIRE_TAKER:expire taker order when STP triggers/ EXPIRE_MAKER:expire taker order when STP triggers/ EXPIRE_BOTH:expire both orders when STP triggers; default NONE
goodTillDate	LONG	NO	order cancel time for timeInForce GTD, mandatory when timeInforce set to GTD; order the timestamp only retains second-level precision, ms part will be ignored; The goodTillDate timestamp must be greater than the current time plus 600 seconds and smaller than 253402300799000
recvWindow	LONG	NO	
timestamp	LONG	YES	
Additional mandatory parameters based on type:

Type	Additional mandatory parameters
LIMIT	timeInForce, quantity, price or priceMatch
MARKET	quantity
STOP/TAKE_PROFIT	quantity, stopPrice, price or priceMatch
STOP_MARKET/TAKE_PROFIT_MARKET	stopPrice
TRAILING_STOP_MARKET	callbackRate
Order with type STOP, parameter timeInForce can be sent ( default GTC).

Order with type TAKE_PROFIT, parameter timeInForce can be sent ( default GTC).

Condition orders will be triggered when:

If parameterpriceProtectis sent as true:
when price reaches the stopPrice ，the difference rate between "MARK_PRICE" and "CONTRACT_PRICE" cannot be larger than the "triggerProtect" of the symbol
"triggerProtect" of a symbol can be got from GET /fapi/v1/exchangeInfo
STOP, STOP_MARKET:
BUY: latest price ("MARK_PRICE" or "CONTRACT_PRICE") >= stopPrice
SELL: latest price ("MARK_PRICE" or "CONTRACT_PRICE") <= stopPrice
TAKE_PROFIT, TAKE_PROFIT_MARKET:
BUY: latest price ("MARK_PRICE" or "CONTRACT_PRICE") <= stopPrice
SELL: latest price ("MARK_PRICE" or "CONTRACT_PRICE") >= stopPrice
TRAILING_STOP_MARKET:
BUY: the lowest price after order placed <= activationPrice, and the latest price >= the lowest price * (1 + callbackRate)
SELL: the highest price after order placed >= activationPrice, and the latest price <= the highest price * (1 - callbackRate)
For TRAILING_STOP_MARKET, if you got such error code.
{"code": -2021, "msg": "Order would immediately trigger."}
means that the parameters you send do not meet the following requirements:

BUY: activationPrice should be smaller than latest price.
SELL: activationPrice should be larger than latest price.
If newOrderRespType is sent as RESULT :

MARKET order: the final FILLED result of the order will be return directly.
LIMIT order with special timeInForce: the final status result of the order(FILLED or EXPIRED) will be returned directly.
STOP_MARKET, TAKE_PROFIT_MARKET with closePosition=true:

Follow the same rules for condition orders.
If triggered，close all current long position( if SELL) or current short position( if BUY).
Cannot be used with quantity paremeter
Cannot be used with reduceOnly parameter
In Hedge Mode,cannot be used with BUY orders in LONG position side. and cannot be used with SELL orders in SHORT position side
Response Example
{
    "id": "3f7df6e3-2df4-44b9-9919-d2f38f90a99a",
    "status": 200,
    "result": {
        "orderId": 325078477,
        "symbol": "BTCUSDT",
        "status": "NEW",
        "clientOrderId": "iCXL1BywlBaf2sesNUrVl3",
        "price": "43187.00",
        "avgPrice": "0.00",
        "origQty": "0.100",
        "executedQty": "0.000",
        "cumQty": "0.000",
        "cumQuote": "0.00000",
        "timeInForce": "GTC",
        "type": "LIMIT",
        "reduceOnly": false,
        "closePosition": false,
        "side": "BUY",
        "positionSide": "BOTH",
        "stopPrice": "0.00",
        "workingType": "CONTRACT_PRICE",
        "priceProtect": false,
        "origType": "LIMIT",
        "priceMatch": "NONE",
        "selfTradePreventionMode": "NONE",
        "goodTillDate": 0,
        "updateTime": 1702555534435
    },
    "rateLimits": [
        {
            "rateLimitType": "ORDERS",
            "interval": "SECOND",
            "intervalNum": 10,
            "limit": 300,
            "count": 1
        },
        {
            "rateLimitType": "ORDERS",
            "interval": "MINUTE",
            "intervalNum": 1,
            "limit": 1200,
            "count": 1
        },
        {
            "rateLimitType": "REQUEST_WEIGHT",
            "interval": "MINUTE",
            "intervalNum": 1,
            "limit": 2400,
            "count": 1
        }
    ]
}

*********
Modify Order (TRADE)
API Description​
Order modify function, currently only LIMIT order modification is supported, modified orders will be reordered in the match queue

Method​
order.modify

Request​
{
    "id": "c8c271ba-de70-479e-870c-e64951c753d9",
    "method": "order.modify",
    "params": {
        "apiKey": "HMOchcfiT9ZRZnhjp2XjGXhsOBd6msAhKz9joQaWwZ7arcJTlD2hGPHQj1lGdTjR",
        "orderId": 328971409,
        "origType": "LIMIT",
        "positionSide": "SHORT",
        "price": "43769.1",
        "priceMatch": "NONE",
        "quantity": "0.11",
        "side": "SELL",
        "symbol": "BTCUSDT",
        "timestamp": 1703426755754,
        "signature": "d30c9f0736a307f5a9988d4a40b688662d18324b17367d51421da5484e835923"
    }
}

Request Weight​
1 on 10s order rate limit(X-MBX-ORDER-COUNT-10S); 1 on 1min order rate limit(X-MBX-ORDER-COUNT-1M); 1 on IP rate limit(x-mbx-used-weight-1m)

Request Parameters​
Name	Type	Mandatory	Description
orderId	LONG	NO	
origClientOrderId	STRING	NO	
symbol	STRING	YES	
side	ENUM	YES	SELL, BUY
quantity	DECIMAL	YES	Order quantity, cannot be sent with closePosition=true
price	DECIMAL	YES	
priceMatch	ENUM	NO	only avaliable for LIMIT/STOP/TAKE_PROFIT order; can be set to OPPONENT/ OPPONENT_5/ OPPONENT_10/ OPPONENT_20: /QUEUE/ QUEUE_5/ QUEUE_10/ QUEUE_20; Can't be passed together with price
recvWindow	LONG	NO	
timestamp	LONG	YES	
Either orderId or origClientOrderId must be sent, and the orderId will prevail if both are sent.
Both quantity and price must be sent, which is different from dapi modify order endpoint.
When the new quantity or price doesn't satisfy PRICE_FILTER / PERCENT_FILTER / LOT_SIZE, amendment will be rejected and the order will stay as it is.
However the order will be cancelled by the amendment in the following situations:
when the order is in partially filled status and the new quantity <= executedQty
When the order is GTX and the new price will cause it to be executed immediately
One order can only be modfied for less than 10000 times
Response Example​
{
    "id": "c8c271ba-de70-479e-870c-e64951c753d9",
    "status": 200,
    "result": {
        "orderId": 328971409,
        "symbol": "BTCUSDT",
        "status": "NEW",
        "clientOrderId": "xGHfltUMExx0TbQstQQfRX",
        "price": "43769.10",
        "avgPrice": "0.00",
        "origQty": "0.110",
        "executedQty": "0.000",
        "cumQty": "0.000",
        "cumQuote": "0.00000",
        "timeInForce": "GTC",
        "type": "LIMIT",
        "reduceOnly": false,
        "closePosition": false,
        "side": "SELL",
        "positionSide": "SHORT",
        "stopPrice": "0.00",
        "workingType": "CONTRACT_PRICE",
        "priceProtect": false,
        "origType": "LIMIT",
        "priceMatch": "NONE",
        "selfTradePreventionMode": "NONE",
        "goodTillDate": 0,
        "updateTime": 1703426756190
    },
    "rateLimits": [
        {
            "rateLimitType": "ORDERS",
            "interval": "SECOND",
            "intervalNum": 10,
            "limit": 300,
            "count": 1
        },
        {
            "rateLimitType": "ORDERS",
            "interval": "MINUTE",
            "intervalNum": 1,
            "limit": 1200,
            "count": 1
        },
        {
            "rateLimitType": "REQUEST_WEIGHT",
            "interval": "MINUTE",
            "intervalNum": 1,
            "limit": 2400,
            "count": 1
        }
    ]
}
**************
Cancel Order (TRADE)
API Description​
Cancel an active order.

Method​
order.cancel

Request​
{
   	"id": "5633b6a2-90a9-4192-83e7-925c90b6a2fd",
    "method": "order.cancel", 
    "params": { 
        "apiKey": "HsOehcfih8ZRxnhjp2XjGXhsOBd6msAhKz9joQaWwZ7arcJTlD2hGOGQj1lGdTjR", 
        "orderId": 283194212, 
        "symbol": "BTCUSDT", 
        "timestamp": 1703439070722, 
        "signature": "b09c49815b4e3f1f6098cd9fbe26a933a9af79803deaaaae03c29f719c08a8a8" 
    }
}

Request Weight​
1

Request Parameters​
Name	Type	Mandatory	Description
symbol	STRING	YES	
orderId	LONG	NO	
origClientOrderId	STRING	NO	
recvWindow	LONG	NO	
timestamp	LONG	YES	
Either orderId or origClientOrderId must be sent.
Response Example​
{
  "id": "5633b6a2-90a9-4192-83e7-925c90b6a2fd",
  "status": 200,
  "result": {
    "clientOrderId": "myOrder1",
    "cumQty": "0",
    "cumQuote": "0",
    "executedQty": "0",
    "orderId": 283194212,
    "origQty": "11",
    "origType": "TRAILING_STOP_MARKET",
    "price": "0",
    "reduceOnly": false,
    "side": "BUY",
    "positionSide": "SHORT",
    "status": "CANCELED",
    "stopPrice": "9300",                
    "closePosition": false,  
    "symbol": "BTCUSDT",
    "timeInForce": "GTC",
    "type": "TRAILING_STOP_MARKET",
    "activatePrice": "9020",            
    "priceRate": "0.3",                
    "updateTime": 1571110484038,
    "workingType": "CONTRACT_PRICE",
    "priceProtect": false,           
    "priceMatch": "NONE",              
    "selfTradePreventionMode": "NONE",
    "goodTillDate": 0                 
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 1
    }
  ]
}
**********************
Query Order (USER_DATA)
API Description
Check an order's status.

These orders will not be found:
order status is CANCELED or EXPIRED AND order has NO filled trade AND created time + 3 days < current time
order create time + 90 days < current time
Method
order.status

Request
{
    "id": "0ce5d070-a5e5-4ff2-b57f-1556741a4204",
    "method": "order.status",
    "params": {
        "apiKey": "HMOchcfii9ZRZnhjp2XjGXhsOBd6msAhKz9joQaWwZ7arcJTlD2hGPHQj1lGdTjR",
        "orderId": 328999071,
        "symbol": "BTCUSDT",
        "timestamp": 1703441060152,
        "signature": "ba48184fc38a71d03d2b5435bd67c1206e3191e989fe99bda1bc643a880dfdbf"
    }
}

Request Weight
1

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	YES	
orderId	LONG	NO	
origClientOrderId	STRING	NO	
recvWindow	LONG	NO	
timestamp	LONG	YES	
Notes:

Either orderId or origClientOrderId must be sent.
orderId is self-increment for each specific symbol
Response Example
{
 "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
 "status": 200,
 "result": {
  "avgPrice": "0.00000",
  "clientOrderId": "abc",
  "cumQuote": "0",
  "executedQty": "0",
  "orderId": 1917641,
  "origQty": "0.40",
  "origType": "TRAILING_STOP_MARKET",
  "price": "0",
  "reduceOnly": false,
  "side": "BUY",
  "positionSide": "SHORT",
  "status": "NEW",
  "stopPrice": "9300",    // please ignore when order type is TRAILING_STOP_MARKET
  "closePosition": false,   // if Close-All
  "symbol": "BTCUSDT",
  "time": *************,    // order time
  "timeInForce": "GTC",
  "type": "TRAILING_STOP_MARKET",
  "activatePrice": "9020",   // activation price, only return with TRAILING_STOP_MARKET order
  "priceRate": "0.3",     // callback rate, only return with TRAILING_STOP_MARKET order
  "updateTime": *************,  // update time
  "workingType": "CONTRACT_PRICE",
  "priceProtect": false            // if conditional order trigger is protected
 }
}

****************
Position Information V2 (USER_DATA)
API Description
Get current position information(only symbol that has position or open orders will be returned).

Method
v2/account.position

Request
{
   	"id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
    "method": "v2/account.position",
    "params": {
        "apiKey": "xTaDyrmvA9XT2oBHHjy39zyPzKCvMdtH3b9q4xadkAg2dNSJXQGCxzui26L823W2",
        "symbol": "BTCUSDT",
        "timestamp": *************,
        "signature": "31ab02a51a3989b66c29d40fcdf78216978a60afc6d8dc1c753ae49fa3164a2a"
    }
}

Request Weight
5

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	NO	
recvWindow	LONG	NO	
timestamp	LONG	YES	
Note

Please use with user data stream ACCOUNT_UPDATE to meet your timeliness and accuracy needs.
Response Example
For One-way position mode:

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": [
    {
	    "symbol": "BTCUSDT",  
	    "positionSide": "BOTH",            // 持仓方向
	    "positionAmt": "1.000",  
	    "entryPrice": "0.00000",
	    "breakEvenPrice": "0.0",  
	    "markPrice": "6679.********",
	    "unrealizedProfit": "0.********",  // 持仓未实现盈亏 
	    "liquidationPrice": "0",  
	    "isolatedMargin": "0.********",	
	    "notional": "0",
	    "marginAsset": "USDT", 
	    "isolatedWallet": "0",
	    "initialMargin": "0",              // 初始保证金
	    "maintMargin": "0",                // 维持保证金
	    "positionInitialMargin": "0",      // 仓位初始保证金
	    "openOrderInitialMargin": "0",     // 订单初始保证金
	    "adl": 0,
	    "bidNotional": "0",  
	    "askNotional": "0",  
	    "updateTime": 0                    // 更新时间
    }
],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}

For Hedge position mode:

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": [
   {
	    "symbol": "BTCUSDT",  
	    "positionSide": "LONG",            
	    "positionAmt": "1.000",  
	    "entryPrice": "0.00000",
	    "breakEvenPrice": "0.0",  
	    "markPrice": "6679.********",
	    "unrealizedProfit": "0.********",  
	    "liquidationPrice": "0",  
	    "isolatedMargin": "0.********",	
	    "notional": "0",
	    "marginAsset": "USDT", 
	    "isolatedWallet": "0",
	    "initialMargin": "0",   
	    "maintMargin": "0",    
	    "positionInitialMargin": "0",      
	    "openOrderInitialMargin": "0",     
	    "adl": 0,
	    "bidNotional": "0",  
	    "askNotional": "0",  
	    "updateTime": 0
    },
    {
	    "symbol": "BTCUSDT",  
	    "positionSide": "SHORT",           
	    "positionAmt": "1.000",  
	    "entryPrice": "0.00000",
	    "breakEvenPrice": "0.0",  
	    "markPrice": "6679.********",
	    "unrealizedProfit": "0.********",  
	    "liquidationPrice": "0",  
	    "isolatedMargin": "0.********",	
	    "notional": "0",
	    "marginAsset": "USDT", 
	    "isolatedWallet": "0",
	    "initialMargin": "0",   
	    "maintMargin": "0",     
	    "positionInitialMargin": "0",      
	    "openOrderInitialMargin": "0",     
	    "adl": 0,
	    "bidNotional": "0",  
	    "askNotional": "0", 
	    "updateTime": 0
    }
  ],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}

*************
Position Information (USER_DATA)
API Description
Get current position information.

Method
account.position

Request
{
   	"id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
    "method": "account.position",
    "params": {
        "apiKey": "xTaDyrmvA9XT2oBHHjy39zyPzKCvMdtH3b9q4xadkAg2dNSJXQGCxzui26L823W2",
        "symbol": "BTCUSDT",
        "timestamp": *************,
        "signature": "31ab02a51a3989b66c29d40fcdf78216978a60afc6d8dc1c753ae49fa3164a2a"
    }
}

Request Weight
5

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	NO	
recvWindow	LONG	NO	
timestamp	LONG	YES	
Note

Please use with user data stream ACCOUNT_UPDATE to meet your timeliness and accuracy needs.
Response Example
For One-way position mode:

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": [
    {
        "entryPrice": "0.00000",
        "breakEvenPrice": "0.0",  
        "marginType": "isolated", 
        "isAutoAddMargin": "false",
        "isolatedMargin": "0.********", 
        "leverage": "10", 
        "liquidationPrice": "0", 
        "markPrice": "6679.********",   
        "maxNotionalValue": "********", 
        "positionAmt": "0.000",
        "notional": "0", 
        "isolatedWallet": "0",
        "symbol": "BTCUSDT", 
        "unRealizedProfit": "0.********", 
        "positionSide": "BOTH",
        "updateTime": 0
    }
],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}

For Hedge position mode:

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": [
    {
        "symbol": "BTCUSDT",
        "positionAmt": "0.001",
        "entryPrice": "22185.2",
        "breakEvenPrice": "0.0",  
        "markPrice": "21123.05052574",
        "unRealizedProfit": "-1.06214947",
        "liquidationPrice": "19731.45529116",
        "leverage": "4",
        "maxNotionalValue": "1********",
        "marginType": "cross",
        "isolatedMargin": "0.********",
        "isAutoAddMargin": "false",
        "positionSide": "LONG",
        "notional": "21.12305052",
        "isolatedWallet": "0",
        "updateTime": 1655217461579
    },
    {
        "symbol": "BTCUSDT",
        "positionAmt": "0.000",
        "entryPrice": "0.0",
        "breakEvenPrice": "0.0",  
        "markPrice": "21123.05052574",
        "unRealizedProfit": "0.********",
        "liquidationPrice": "0",
        "leverage": "4",
        "maxNotionalValue": "1********",
        "marginType": "cross",
        "isolatedMargin": "0.********",
        "isAutoAddMargin": "false",
        "positionSide": "SHORT",
        "notional": "0",
        "isolatedWallet": "0",
        "updateTime": 0
    }
],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}
