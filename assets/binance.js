import WebSocket from "ws";
import { sign, etc } from "@noble/ed25519";
import { sha512 } from "@noble/hashes/sha512";
import { TextEncoder } from "util";
import fetch from "node-fetch"; // Node 18 altı için: npm install node-fetch

// noble/ed25519 için SHA-512 tanımı
etc.sha512Sync = sha512;

// 🔐 API bilgilerin
const API_KEY = "gUw6dEV8norj2NvhCR7I7AQNldujPxbUO1PWUsdkWAFxt7rnNWolBSEFANXlttAg";
const PRIVATE_KEY_HEX = "5bcf9f5c3d43153a32bdfe5790d38a241fd092c67afd49cae933540eec5f4fe0";

// Konsola renkli çıktı fonksiyonları
const log = {
    info: (msg) => console.log(`\x1b[36m${msg}\x1b[0m`),
    success: (msg) => console.log(`\x1b[32m${msg}\x1b[0m`),
    warn: (msg) => console.log(`\x1b[33m${msg}\x1b[0m`),
    error: (msg) => console.log(`\x1b[31m${msg}\x1b[0m`),
    data: (msg) => console.log(msg),
    balance: (msg) => console.log(`\x1b[35m💰 ${msg}\x1b[0m`), // Bakiye için özel renk
};

// 🧠 Ed25519 imzalama fonksiyonu
async function ed25519Sign(message) {
    try {
        const encoder = new TextEncoder();
        const messageBytes = encoder.encode(message);
        const privateKeyBytes = Buffer.from(PRIVATE_KEY_HEX, "hex");
        const signature = await sign(messageBytes, privateKeyBytes);
        return Buffer.from(signature).toString("hex");
    } catch (error) {
        log.error(`İmzalama hatası: ${error.message}`);
        throw error;
    }
}

// 🔵 Binance Future REST API için listenKey al
async function getListenKey() {
    try {
        log.info("ListenKey alınıyor...");
        const response = await fetch("https://fapi.binance.com/fapi/v1/listenKey", {
            method: "POST",
            headers: {
                "X-MBX-APIKEY": API_KEY
            }
        });

        const data = await response.json();

        if (data.listenKey) {
            log.success(`ListenKey alındı: ${data.listenKey}`);
            return data.listenKey;
        } else {
            log.error(`ListenKey alınamadı: ${JSON.stringify(data)}`);
            throw new Error("ListenKey alınamadı");
        }
    } catch (error) {
        log.error(`ListenKey alınırken hata: ${error.message}`);
        throw error;
    }
}

// 💰 WebSocket bakiye verilerini işle
function processBalanceUpdate(balances) {
    log.balance("=== BAKİYE GÜNCELLEMESİ ===");

    const significantBalances = balances.filter(balance => {
        const walletBalance = parseFloat(balance.wb);
        const crossWalletBalance = parseFloat(balance.cw);
        return walletBalance > 0.001 || crossWalletBalance > 0.001; // Çok küçük miktarları filtrele
    });

    if (significantBalances.length > 0) {
        significantBalances.forEach(balance => {
            const asset = balance.a;
            const walletBalance = parseFloat(balance.wb);
            const crossWalletBalance = parseFloat(balance.cw);

            log.balance(`${asset}:`);
            log.balance(`  Cüzdan Bakiyesi: ${walletBalance.toFixed(8)}`);
            log.balance(`  Cross Wallet: ${crossWalletBalance.toFixed(8)}`);
            log.balance(`  ---`);
        });
    } else {
        log.warn("Önemli bakiye değişikliği yok");
    }
}

// 💰 WebSocket pozisyon verilerini işle
function processPositionUpdate(positions) {
    log.info("=== POZİSYON GÜNCELLEMESİ ===");

    const activePositions = positions.filter(pos => parseFloat(pos.pa) !== 0);

    if (activePositions.length > 0) {
        activePositions.forEach(pos => {
            const symbol = pos.s;
            const size = parseFloat(pos.pa);
            const entryPrice = parseFloat(pos.ep);
            const side = size > 0 ? 'LONG' : 'SHORT';
            const unrealizedPnl = parseFloat(pos.up || 0);

            log.success(`${symbol} (${side}):`);
            log.success(`  Miktar: ${Math.abs(size)}`);
            log.success(`  Giriş Fiyatı: ${entryPrice}`);
            log.success(`  PnL: ${unrealizedPnl.toFixed(4)} USDT`);
            log.success(`  ---`);
        });
    } else {
        log.warn("Aktif pozisyon bulunamadı");
    }

    // BTCUSDT pozisyonunu özellikle kontrol et
    const btcPosition = positions.find(p => p.s === "BTCUSDT");
    if (btcPosition && parseFloat(btcPosition.pa) !== 0) {
        log.success(`🔥 BTCUSDT pozisyon: ${btcPosition.pa} adet, giriş fiyatı: ${btcPosition.ep}`);
    }
}

// 🔵 WebSocket üzerinden kullanıcı verilerini izle
async function listenUserData() {
    try {
        // 1. ListenKey al
        const listenKey = await getListenKey();

        // 2. WebSocket bağlantısı kur
        const ws = new WebSocket(`wss://fstream.binance.com/ws/${listenKey}`);

        ws.on("open", () => {
            log.success("WebSocket bağlantısı kuruldu");
            log.info("Bakiye ve pozisyon güncellemeleri bekleniyor...");
        });

        ws.on("message", (data) => {
            try {
                const event = JSON.parse(data);
                log.info(`WebSocket event alındı: ${event.e}`);

                // Event türüne göre işlem yap
                if (event.e === "ACCOUNT_UPDATE") {
                    log.info("Hesap güncellemesi alındı:");

                    // Bakiye güncellemeleri
                    if (event.a?.B) {
                        processBalanceUpdate(event.a.B);
                    }

                    // Pozisyon güncellemeleri
                    if (event.a?.P) {
                        processPositionUpdate(event.a.P);
                    }
                }

                // Bakiye değişikliği eventi
                if (event.e === "BALANCE_UPDATE") {
                    log.balance("=== BAKİYE DEĞİŞİKLİĞİ ===");
                    log.balance(`Asset: ${event.a}`);
                    log.balance(`Bakiye Değişimi: ${event.d}`);
                    log.balance(`İşlem Zamanı: ${new Date(event.T).toLocaleString()}`);
                }

                log.data(JSON.stringify(event, null, 2));
            } catch (error) {
                log.error(`WebSocket veri işleme hatası: ${error.message}`);
            }
        });

        ws.on("error", (error) => {
            log.error(`WebSocket hatası: ${error.message}`);
        });

        ws.on("close", () => {
            log.warn("WebSocket bağlantısı kapandı");
        });

        // 30 dakikada bir listenKey yenile
        const keepAliveInterval = setInterval(async () => {
            try {
                await fetch("https://fapi.binance.com/fapi/v1/listenKey", {
                    method: "PUT",
                    headers: {
                        "X-MBX-APIKEY": API_KEY
                    }
                });
                log.info("ListenKey yenilendi");
            } catch (error) {
                log.error(`ListenKey yenilenirken hata: ${error.message}`);
            }
        }, 30 * 60 * 1000); // 30 dakika

        return { ws, keepAliveInterval };
    } catch (error) {
        log.error(`Kullanıcı verilerini dinlerken hata: ${error.message}`);
        throw error;
    }
}

// 🔵 REST API ile pozisyonları sorgula
async function getPositions() {
    try {
        log.info("Pozisyonlar sorgulanıyor...");

        const timestamp = Date.now();
        const queryString = `timestamp=${timestamp}`;

        // Ed25519 ile imzala
        const signature = await ed25519Sign(queryString);

        const url = `https://fapi.binance.com/fapi/v2/positionRisk?${queryString}&signature=${signature}`;

        const response = await fetch(url, {
            method: "GET",
            headers: {
                "X-MBX-APIKEY": API_KEY
            }
        });

        const data = await response.json();

        if (Array.isArray(data)) {
            log.success("Pozisyonlar alındı");

            // Aktif pozisyonları filtrele (sıfır olmayan)
            const activePositions = data.filter(p => parseFloat(p.positionAmt) !== 0);

            if (activePositions.length > 0) {
                log.success("=== AKTİF POZİSYONLAR ===");
                activePositions.forEach(pos => {
                    const symbol = pos.symbol;
                    const size = parseFloat(pos.positionAmt);
                    const entryPrice = parseFloat(pos.entryPrice);
                    const markPrice = parseFloat(pos.markPrice);
                    const pnl = parseFloat(pos.unRealizedProfit);
                    const side = size > 0 ? 'LONG' : 'SHORT';

                    log.success(`${symbol} (${side}):`);
                    log.success(`  Miktar: ${Math.abs(size)}`);
                    log.success(`  Giriş Fiyatı: ${entryPrice}`);
                    log.success(`  Mark Fiyatı: ${markPrice}`);
                    log.success(`  PnL: ${pnl.toFixed(4)} USDT`);
                    log.success(`  ---`);
                });
            } else {
                log.warn("Aktif pozisyon bulunamadı");
            }

            // BTCUSDT pozisyonunu özellikle kontrol et
            const btcPosition = data.find(p => p.symbol === "BTCUSDT");
            if (btcPosition && parseFloat(btcPosition.positionAmt) !== 0) {
                log.success(`BTCUSDT pozisyon: ${btcPosition.positionAmt} adet, mark fiyat: ${btcPosition.markPrice}`);
            }

            return data;
        } else {
            log.error(`Pozisyon sorgulama hatası: ${JSON.stringify(data)}`);

            // Detaylı imza bilgilerini yazdır
            log.data(`Query string: ${queryString}`);
            log.data(`Signature: ${signature}`);

            throw new Error("Pozisyon sorgulama hatası");
        }
    } catch (error) {
        log.error(`Pozisyonları sorgularken hata: ${error.message}`);
        throw error;
    }
}

// 🚀 Ana program başlat
(async () => {
    try {
        log.info("Binance Future WebSocket client başlatılıyor...");
        const { ws, keepAliveInterval } = await listenUserData();

        // Graceful shutdown
        process.on('SIGINT', () => {
            log.warn('Program sonlandırılıyor...');
            ws.close();
            clearInterval(keepAliveInterval);
            process.exit(0);
        });

    } catch (error) {
        log.error(`Program hatası: ${error.message}`);
    }
})();