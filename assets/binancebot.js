// binanceBot.js
import fetch from "node-fetch";
import WebSocket from "ws";
import crypto from "crypto";
import { sign, etc } from "@noble/ed25519";
import { sha512 } from "@noble/hashes/sha512";
import { TextEncoder } from "util";
import dotenv from "dotenv";
dotenv.config();

etc.sha512Sync = sha512;

const HMAC_API_KEY = process.env.HMAC_API_KEY;
const HMAC_API_SECRET = process.env.HMAC_API_SECRET;
const ED25519_API_KEY = process.env.ED25519_API_KEY;
const PRIVATE_KEY_HEX = process.env.PRIVATE_KEY_HEX;

const BASE_URL = "https://fapi.binance.com";
let accountSnapshot = {};

function hmacSign(query, secret) {
    return crypto.createHmac("sha256", secret).update(query).digest("hex");
}

async function getAccountInfo() {
    const timestamp = Date.now();
    const query = `timestamp=${timestamp}`;
    const signature = hmacSign(query, HMAC_API_SECRET);
    const res = await fetch(`${BASE_URL}/fapi/v2/account?${query}&signature=${signature}`, {
        headers: { "X-MBX-APIKEY": HMAC_API_KEY },
    });
    const json = await res.json();
    if (json.totalWalletBalance) {
        accountSnapshot = json;
        console.log("💰 Bakiye alındı:", json.totalWalletBalance);
    } else {
        console.error("❌ Bakiye alınamadı:", json);
    }
}

async function placeOrder({ symbol, side, type = "MARKET", quantity, price, reduceOnly = false }) {
    const timestamp = Date.now();
    const params = new URLSearchParams({
        symbol,
        side,
        type,
        quantity: quantity.toString(),
        ...(type === "LIMIT" && { price: price.toString(), timeInForce: "GTC" }),
        ...(reduceOnly && { reduceOnly: "true" }),
        timestamp: timestamp.toString(),
    });
    const signature = hmacSign(params.toString(), HMAC_API_SECRET);
    const url = `${BASE_URL}/fapi/v1/order?${params.toString()}&signature=${signature}`;
    const res = await fetch(url, {
        method: "POST",
        headers: { "X-MBX-APIKEY": HMAC_API_KEY },
    });
    const json = await res.json();
    console.log("✅ Emir sonucu:", json);
    return json;
}

async function closePosition(symbol) {
    const timestamp = Date.now();
    const position = accountSnapshot.positions?.find(p => p.symbol === symbol);
    if (!position || Number(position.positionAmt) === 0) {
        console.log("ℹ️ Kapatılacak pozisyon yok:", symbol);
        return;
    }
    const side = Number(position.positionAmt) > 0 ? "SELL" : "BUY";
    const quantity = Math.abs(Number(position.positionAmt));
    return placeOrder({ symbol, side, quantity, type: "MARKET", reduceOnly: true });
}

async function setLeverage(symbol, leverage) {
    const timestamp = Date.now();
    const params = new URLSearchParams({
        symbol,
        leverage: leverage.toString(),
        timestamp: timestamp.toString(),
    });
    const signature = hmacSign(params.toString(), HMAC_API_SECRET);
    const url = `${BASE_URL}/fapi/v1/leverage?${params.toString()}&signature=${signature}`;
    const res = await fetch(url, {
        method: "POST",
        headers: { "X-MBX-APIKEY": HMAC_API_KEY },
    });
    const json = await res.json();
    console.log("⚙️ Kaldıraç ayarlandı:", json);
}

async function startWebSocket() {
    const res = await fetch(`${BASE_URL}/fapi/v1/listenKey`, {
        method: "POST",
        headers: { "X-MBX-APIKEY": HMAC_API_KEY },
    });
    const { listenKey } = await res.json();
    const ws = new WebSocket(`wss://fstream.binance.com/ws/${listenKey}`);

    ws.on("message", (data) => {
        const msg = JSON.parse(data);
        if (msg.e === "ACCOUNT_UPDATE") {
            accountSnapshot = msg.a;
            console.log("✨ WS Bakiye güncellendi:", accountSnapshot);
        }
        if (msg.e === "ORDER_TRADE_UPDATE") {
            console.log("🌟 Emir olayı:", msg.o);
        }
    });

    ws.on("open", () => console.log("🔗 WebSocket bağlantısı kuruldu."));
    ws.on("close", () => console.log("❌ WebSocket kapandı."));
    ws.on("error", (err) => console.error("WebSocket hatası:", err));
}

async function exampleTrades() {
    await setLeverage("ETHUSDT", 10);
    await placeOrder({ symbol: "ETHUSDT", side: "BUY", type: "MARKET", quantity: 0.01 });
    await placeOrder({ symbol: "BTCUSDT", side: "SELL", type: "LIMIT", quantity: 0.001, price: 80000 });
    await closePosition("ETHUSDT");
}

(async () => {
    await getAccountInfo();
    await startWebSocket();
    // await exampleTrades();
})();
