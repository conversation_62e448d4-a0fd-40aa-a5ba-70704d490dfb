WebSocket API General Info
The base endpoint is: wss://ws-fapi.binance.com/ws-fapi/v1
The base endpoint for testnet is: wss://testnet.binancefuture.com/ws-fapi/v1
A single connection to the API is only valid for 24 hours; expect to be disconnected after the 24-hour mark.
Websocket server will send a ping frame every 3 minutes.
If the websocket server does not receive a pong frame back from the connection within a 10 minute period, the connection will be disconnected.
When you receive a ping, you must send a pong with a copy of ping's payload as soon as possible.
Unsolicited pong frames are allowed, but will not prevent disconnection. It is recommended that the payload for these pong frames are empty.
Signature payload must be generated by taking all request params except for the signature and sorting them by name in alphabetical order.
Lists are returned in chronological order, unless noted otherwise.
All timestamps are in milliseconds in UTC, unless noted otherwise.
All field names and values are case-sensitive, unless noted otherwise.
INT parameters such as timestamp are expected as JSON integers, not strings.
DECIMAL parameters such as price are expected as JSON strings, not floats.
User Data Stream requests - you will need to establish a separate WebSocket connection to listen to user data streams
WebSocket API Request format
Requests must be sent as JSON in text frames, one request per frame.

Example of request:

{
  "id": "9ca10e58-7452-467e-9454-f669bb9c764e",
  "method": "order.place",
  "params": {
    "apiKey": "yeqKcXjtA9Eu4Tr3nJk61UJAGzXsEmFqqfVterxpMpR4peNfqE7Zl7oans8Qj089",
    "price": "42088.0",
    "quantity": "0.1",
    "recvWindow": 5000,
    "side": "BUY",
    "signature": "996962a19802b5a09d7bc6ab1524227894533322a2f8a1f8934991689cabf8fe",
    "symbol": "BTCUSDT",
    "timeInForce": "GTC",
    "timestamp": 1705311512994,
    "type": "LIMIT"
  }
}

Request fields:

Name	Type	Mandatory	 Description
id	INT/STRING/null	YES	Arbitrary ID used to match responses to requests
method	STRING	YES	Request method name
params	OBJECT	NO	Request parameters. May be omitted if there are no parameters
  			
Request id is truly arbitrary. You can use UUIDs, sequential IDs, current timestamp, etc. The server does not interpret id in any way, simply echoing it back in the response.
You can freely reuse IDs within a session. However, be careful to not send more than one request at a time with the same ID, since otherwise it might be impossible to tell the responses apart.  

Request method names may be prefixed with explicit version: e.g., "v3/order.place".
The order of params is not significant.
Response format
Responses are returned as JSON in text frames, one response per frame.

Example of successful response:

{
  "id": "43a3843a-2321-4e45-8f79-351e5c354563",
  "status": 200,
  "result": {
    "orderId": 336829446,
    "symbol": "BTCUSDT",
    "status": "NEW",
    "clientOrderId": "FqEw6cn0vDhrkmfiwLYPeo",
    "price": "42088.00",
    "avgPrice": "0.00",
    "origQty": "0.100",
    "executedQty": "0.000",
    "cumQty": "0.000",
    "cumQuote": "0.00000",
    "timeInForce": "GTC",
    "type": "LIMIT",
    "reduceOnly": false,
    "closePosition": false,
    "side": "BUY",
    "positionSide": "BOTH",
    "stopPrice": "0.00",
    "workingType": "CONTRACT_PRICE",
    "priceProtect": false,
    "origType": "LIMIT",
    "priceMatch": "NONE",
    "selfTradePreventionMode": "NONE",
    "goodTillDate": 0,
    "updateTime": 1705385954229
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 1
    },
    {
      "rateLimitType": "ORDERS",
      "interval": "SECOND",
      "intervalNum": 10,
      "limit": 300,
      "count": 1
    },
    {
      "rateLimitType": "ORDERS",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 1200,
      "count": 0
    }
  ]
}

Example of failed response:

{
  "id": "5761b939-27b1-4948-ab87-4a372a3f6b72",
  "status": 400,
  "error": {
    "code": -1102,
    "msg": "Mandatory parameter 'quantity' was not sent, was empty/null, or malformed."
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 1
    },
    {
      "rateLimitType": "ORDERS",
      "interval": "SECOND",
      "intervalNum": 10,
      "limit": 300,
      "count": 1
    },
    {
      "rateLimitType": "ORDERS",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 1200,
      "count": 1
    }
  ]
}

Response fields:

Name	Type	Mandatory	 Description
id	INT/STRING/null	YES	Same as in the original request
status	INT	YES	Response status. See status codes
result	OBJECT/ARRAY	YES	Response content. Present if request succeeded
error	OBJECT	YES	Error description. Present if request failed
rateLimits	ARRAY	NO	Rate limiting status. See Rate limits
WebSocket API Rate limits
Rate limits are the same as on REST API and are shared with REST API.
WebSocket handshake attempt costs 5 weight.
Rate limit for ping/pong frames: maximum 5 per second.
Rate limit information is included in responses by default, see the rateLimits field.
rateLimits field visibility can be controlled with returnRateLimits boolean parameter in connection string or individual requests.
E.g., use wss://ws-fapi.binance.com/ws-fapi/v1?returnRateLimits=false to hide rateLimits in responses by default. With that, you can pass extra "returnRateLimits": true parameter in requests to show rate limit in response when it is otherwise hidden by default.
WebSocket API Authenticate after connection
You can authenticate an already established connection using session authentication requests:

session.logon - authenticate, or change the API key associated with the connection
session.status - check connection status and the current API key
session.logout - forget the API key associated with the connection
WebSocket API API key revocation
If during an active session the API key becomes invalid for any reason (e.g. IP address is not whitelisted, API key was deleted, API key doesn't have correct permissions, etc), after the next request the session will be revoked with the following error message:

{
  "id": null,
  "status": 401,
  "error": {
    "code": -2015,
    "msg": "Invalid API-key, IP, or permissions for action." 
  }
}

WebSocket API Authorize ad hoc requests
Only one API key can be authenticated with the WebSocket connection. The authenticated API key is used by default for requests that require an apiKey parameter. However, you can always specify the apiKey and signature explicitly for individual requests, overriding the authenticated API key and using a different one to authorize a specific request.

For example, you might want to authenticate your USER_DATA key to be used by default, but specify the TRADE key with an explicit signature when placing orders.

WebSocket API Authentication request
Note:

Only Ed25519 keys are supported for this feature.

Log in with API key (SIGNED)
Request

{
  "id": "c174a2b1-3f51-4580-b200-8528bd237cb7",
  "method": "session.logon",
  "params": {
    "apiKey": "vmPUZE6mv9SD5VNHk4HlWFsOr6aKE2zvsw0MuIgwCIPy6utIco14y7Ju91duEh8A",
    "signature": "1cf54395b336b0a9727ef27d5d98987962bc47aca6e13fe978612d0adee066ed",
    "timestamp": 1649729878532
  }
}

Response

{
  "id": "c174a2b1-3f51-4580-b200-8528bd237cb7",
  "status": 200,
  "result": {
    "apiKey": "vmPUZE6mv9SD5VNHk4HlWFsOr6aKE2zvsw0MuIgwCIPy6utIco14y7Ju91duEh8A",
    "authorizedSince": 1649729878532,
    "connectedSince": 1649729873021,
    "returnRateLimits": false,
    "serverTime": 1649729878630
  }
}

Authenticate WebSocket connection using the provided API key.

After calling session.logon, you can omit apiKey and signature parameters for future requests that require them.

Note that only one API key can be authenticated. Calling session.logon multiple times changes the current authenticated API key.

Weight: 2

Method: "session.logon"

Parameters

Name	Type	Mandatory	 Description
apiKey	STRING	YES	
recvWindow	INT	NO	
signature	STRING	YES	
timestamp	INT	YES	
Query session status
Request

{
  "id": "b50c16cd-62c9-4e29-89e4-37f10111f5bf",
  "method": "session.status"
}

Response

{
  "id": "b50c16cd-62c9-4e29-89e4-37f10111f5bf",
  "status": 200,
  "result": {
    // if the connection is not authenticated, "apiKey" and "authorizedSince" will be shown as null
    "apiKey": "vmPUZE6mv9SD5VNHk4HlWFsOr6aKE2zvsw0MuIgwCIPy6utIco14y7Ju91duEh8A",
    "authorizedSince": 1649729878532,
    "connectedSince": 1649729873021,
    "returnRateLimits": false,
    "serverTime": 1649730611671
  }
}

Query the status of the WebSocket connection, inspecting which API key (if any) is used to authorize requests.

Weight: 2

Method: "session.status"

Parameters: None

Log out of the session
Request

{
  "id": "c174a2b1-3f51-4580-b200-8528bd237cb7",
  "method": "session.logout"
}

Response

{
  "id": "c174a2b1-3f51-4580-b200-8528bd237cb7",
  "status": 200,
  "result": {
    "apiKey": null,
    "authorizedSince": null,
    "connectedSince": 1649729873021,
    "returnRateLimits": false,
    "serverTime": 1649730611671
  }
}

Forget the API key previously authenticated. If the connection is not authenticated, this request does nothing.

Note that the WebSocket connection stays open after session.logout request. You can continue using the connection, but now you will have to explicitly provide the apiKey and signature parameters where needed.

Weight: 2

Method: "session.logout"

Parameters: None

SIGNED (TRADE and USER_DATA) Endpoint Security
SIGNED request example (Ed25519)
Parameter	Value
symbol	BTCUSDT
side	SELL
type	LIMIT
timeInForce	GTC
quantity	1
price	0.2
timestamp	1668481559918
#!/usr/bin/env python3

import base64
import time
import json
from cryptography.hazmat.primitives.serialization import load_pem_private_key
from websocket import create_connection

# Set up authentication
API_KEY='put your own API Key here'
PRIVATE_KEY_PATH='test-prv-key.pem'

# Load the private key.
# In this example the key is expected to be stored without encryption,
# but we recommend using a strong password for improved security.
with open(PRIVATE_KEY_PATH, 'rb') as f:
    private_key = load_pem_private_key(data=f.read(),
                                       password=None)

# Set up the request parameters
params = {
    'apiKey':        API_KEY,
    'symbol':       'BTCUSDT',
    'side':         'SELL',
    'type':         'LIMIT',
    'timeInForce':  'GTC',
    'quantity':     '1.0000000',
    'price':        '0.20'
}

# Timestamp the request
timestamp = int(time.time() * 1000) # UNIX timestamp in milliseconds
params['timestamp'] = timestamp

# Sign the request
payload = '&'.join([f'{param}={value}' for param, value in sorted(params.items())])

signature = base64.b64encode(private_key.sign(payload.encode('ASCII')))
params['signature'] = signature.decode('ASCII')

# Send the request
request = {
    'id': 'my_new_order',
    'method': 'order.place',
    'params': params
}

ws = create_connection("wss://ws-fapi.binance.com/ws-fapi/v1")
ws.send(json.dumps(request))
result =  ws.recv()
ws.close()

print(result)


A sample code in Python to show how to sign the payload with an Ed25519 key is available on the right side.

Public Endpoints Info
Terminology
base asset refers to the asset that is the quantity of a symbol.
quote asset refers to the asset that is the price of a symbol.
ENUM definitions
Symbol type:

FUTURE
Contract type (contractType):

PERPETUAL
CURRENT_MONTH
NEXT_MONTH
CURRENT_QUARTER
NEXT_QUARTER
PERPETUAL_DELIVERING
Contract status (contractStatus, status):

PENDING_TRADING
TRADING
PRE_DELIVERING
DELIVERING
DELIVERED
PRE_SETTLE
SETTLING
CLOSE
Order status (status):

NEW
PARTIALLY_FILLED
FILLED
CANCELED
REJECTED
EXPIRED
EXPIRED_IN_MATCH
Order types (orderTypes, type):

LIMIT
MARKET
STOP
STOP_MARKET
TAKE_PROFIT
TAKE_PROFIT_MARKET
TRAILING_STOP_MARKET
Order side (side):

BUY
SELL
Position side (positionSide):

BOTH
LONG
SHORT
Time in force (timeInForce):

GTC - Good Till Cancel(GTC order valitidy is 1 year from placement)
IOC - Immediate or Cancel
FOK - Fill or Kill
GTX - Good Till Crossing (Post Only)
GTD - Good Till Date
Working Type (workingType)

MARK_PRICE
CONTRACT_PRICE
Response Type (newOrderRespType)

ACK
RESULT
Kline/Candlestick chart intervals:

m -> minutes; h -> hours; d -> days; w -> weeks; M -> months

1m
3m
5m
15m
30m
1h
2h
4h
6h
8h
12h
1d
3d
1w
1M
STP MODE (selfTradePreventionMode):

EXPIRE_TAKER
EXPIRE_BOTH
EXPIRE_MAKER
Price Match (priceMatch)

NONE (No price match)
OPPONENT (counterparty best price)
OPPONENT_5 (the 5th best price from the counterparty)
OPPONENT_10 (the 10th best price from the counterparty)
OPPONENT_20 (the 20th best price from the counterparty)
QUEUE (the best price on the same side of the order book)
QUEUE_5 (the 5th best price on the same side of the order book)
QUEUE_10 (the 10th best price on the same side of the order book)
QUEUE_20 (the 20th best price on the same side of the order book)
Rate limiters (rateLimitType)

REQUEST_WEIGHT

  {
  	"rateLimitType": "REQUEST_WEIGHT",
  	"interval": "MINUTE",
  	"intervalNum": 1,
  	"limit": 2400
  }

ORDERS

  {
  	"rateLimitType": "ORDERS",
  	"interval": "MINUTE",
  	"intervalNum": 1,
  	"limit": 1200
   }

REQUEST_WEIGHT

ORDERS

Rate limit intervals (interval)

MINUTE
Filters
Filters define trading rules on a symbol or an exchange.

Symbol filters
PRICE_FILTER
/exchangeInfo format:

  {
    "filterType": "PRICE_FILTER",
    "minPrice": "0.00000100",
    "maxPrice": "100000.********",
    "tickSize": "0.00000100"
  }

The PRICE_FILTER defines the price rules for a symbol. There are 3 parts:

minPrice defines the minimum price/stopPrice allowed; disabled on minPrice == 0.
maxPrice defines the maximum price/stopPrice allowed; disabled on maxPrice == 0.
tickSize defines the intervals that a price/stopPrice can be increased/decreased by; disabled on tickSize == 0.
Any of the above variables can be set to 0, which disables that rule in the price filter. In order to pass the price filter, the following must be true for price/stopPrice of the enabled rules:

price >= minPrice
price <= maxPrice
(price-minPrice) % tickSize == 0
LOT_SIZE
/exchangeInfo format:

  {
    "filterType": "LOT_SIZE",
    "minQty": "0.00100000",
    "maxQty": "100000.********",
    "stepSize": "0.00100000"
  }

The LOT_SIZE filter defines the quantity (aka "lots" in auction terms) rules for a symbol. There are 3 parts:

minQty defines the minimum quantity allowed.
maxQty defines the maximum quantity allowed.
stepSize defines the intervals that a quantity can be increased/decreased by.
In order to pass the lot size, the following must be true for quantity:

quantity >= minQty
quantity <= maxQty
(quantity-minQty) % stepSize == 0
MARKET_LOT_SIZE
/exchangeInfo format:

  {
    "filterType": "MARKET_LOT_SIZE",
    "minQty": "0.00100000",
    "maxQty": "100000.********",
    "stepSize": "0.00100000"
  }

The MARKET_LOT_SIZE filter defines the quantity (aka "lots" in auction terms) rules for MARKET orders on a symbol. There are 3 parts:

minQty defines the minimum quantity allowed.
maxQty defines the maximum quantity allowed.
stepSize defines the intervals that a quantity can be increased/decreased by.
In order to pass the market lot size, the following must be true for quantity:

quantity >= minQty
quantity <= maxQty
(quantity-minQty) % stepSize == 0
MAX_NUM_ORDERS
/exchangeInfo format:

  {
    "filterType": "MAX_NUM_ORDERS",
    "limit": 200
  }

The MAX_NUM_ORDERS filter defines the maximum number of orders an account is allowed to have open on a symbol.

Note that both "algo" orders and normal orders are counted for this filter.

MAX_NUM_ALGO_ORDERS
/exchangeInfo format:

  {
    "filterType": "MAX_NUM_ALGO_ORDERS",
    "limit": 100
  }

The MAX_NUM_ALGO_ORDERS filter defines the maximum number of all kinds of algo orders an account is allowed to have open on a symbol.

The algo orders include STOP, STOP_MARKET, TAKE_PROFIT, TAKE_PROFIT_MARKET, and TRAILING_STOP_MARKET orders.

PERCENT_PRICE
/exchangeInfo format:

  {
    "filterType": "PERCENT_PRICE",
    "multiplierUp": "1.1500",
    "multiplierDown": "0.8500",
    "multiplierDecimal": 4
  }

The PERCENT_PRICE filter defines valid range for a price based on the mark price.

In order to pass the percent price, the following must be true for price:

BUY: price <= markPrice * multiplierUp
SELL: price >= markPrice * multiplierDown
MIN_NOTIONAL
/exchangeInfo format:

  {
    "filterType": "MIN_NOTIONAL",
    "notional": "5.0"
  }

The MIN_NOTIONAL filter defines the minimum notional value allowed for an order on a symbol. 
An order's notional value is the price * quantity. Since MARKET orders have no price, the mark price is used.

Order Book
API Description
Get current order book. Note that this request returns limited market depth. If you need to continuously monitor order book updates, please consider using Websocket Market Streams:

<symbol>@depth<levels>
<symbol>@depth
You can use depth request together with <symbol>@depth streams to maintain a local order book.

Method
depth

Request
{
    "id": "51e2affb-0aba-4821-ba75-f2625006eb43",
    "method": "depth",
    "params": {
      "symbol": "BTCUSDT"
    }
}

Request Weight
Adjusted based on the limit:

Limit	Weight
5, 10, 20, 50	2
100	5
500	10
1000	20
Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	YES	
limit	INT	NO	Default 500; Valid limits:[5, 10, 20, 50, 100, 500, 1000]
Response Example
{
  "id": "51e2affb-0aba-4821-ba75-f2625006eb43",
  "status": 200,
  "result": {
    "lastUpdateId": 1027024,
    "E": 1589436922972,   // Message output time
    "T": 1589436922959,   // Transaction time
    "bids": [
      [
        "4.********",     // PRICE
        "431.********"    // QTY
      ]
    ],
    "asks": [
      [
        "4.00000200",
        "12.********"
      ]
    ]
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 5
    }
  ]
}

Symbol Price Ticker
API Description
Latest price for a symbol or symbols.

Method
ticker.price

Request
{
   	"id": "9d32157c-a556-4d27-9866-66760a174b57",
    "method": "ticker.price",
    "params": {
        "symbol": "BTCUSDT"
    }
}

Weight:

1 for a single symbol;
2 when the symbol parameter is omitted

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	NO	
If the symbol is not sent, prices for all symbols will be returned in an array.
Response Example
{
  "id": "9d32157c-a556-4d27-9866-66760a174b57",
  "status": 200,
  "result": {
	"symbol": "BTCUSDT",
	"price": "6000.01",
	"time": 1589437530011   // Transaction time
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 2
    }
  ]
}

OR

{
  "id": "9d32157c-a556-4d27-9866-66760a174b57",
  "status": 200,
  "result": [
	{
    	"symbol": "BTCUSDT",
      	"price": "6000.01",
      	"time": 1589437530011
  	}
  ],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 2
    }
  ]
}


Symbol Order Book Ticker
API Description
Best price/qty on the order book for a symbol or symbols.

Method
ticker.book

Request
{
    "id": "9d32157c-a556-4d27-9866-66760a174b57",
    "method": "ticker.book",
    "params": {
        "symbol": "BTCUSDT"
    }
}

Request Weight
2 for a single symbol;
5 when the symbol parameter is omitted

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	NO	
If the symbol is not sent, bookTickers for all symbols will be returned in an array.
The field X-MBX-USED-WEIGHT-1M in response header is not accurate from this endpoint, please ignore.
Response Example
{
  "id": "9d32157c-a556-4d27-9866-66760a174b57",
  "status": 200,
  "result": {
    "lastUpdateId": 1027024,
    "symbol": "BTCUSDT",
    "bidPrice": "4.********",
    "bidQty": "431.********",
    "askPrice": "4.00000200",
    "askQty": "9.********",
    "time": 1589437530011   // Transaction time
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 2
    }
  ]
}

OR

{
  "id": "9d32157c-a556-4d27-9866-66760a174b57",
  "status": 200,
  "result": [
    {
      "lastUpdateId": 1027024,
      "symbol": "BTCUSDT",
      "bidPrice": "4.********",
      "bidQty": "431.********",
      "askPrice": "4.00000200",
      "askQty": "9.********",
      "time": 1589437530011
    }
  ],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 2
    }
  ]
}


New Order(TRADE)
API Description
Send in a new order.

Method
order.place

Request
{
    "id": "3f7df6e3-2df4-44b9-9919-d2f38f90a99a",
    "method": "order.place",
    "params": {
        "apiKey": "HMOchcfii9ZRZnhjp2XjGXhsOBd6msAhKz9joQaWwZ7arcJTlD2hGPHQj1lGdTjR",
        "positionSide": "BOTH",
        "price": 43187.00,
        "quantity": 0.1,
        "side": "BUY",
        "symbol": "BTCUSDT",
        "timeInForce": "GTC",
        "timestamp": 1702555533821,
        "type": "LIMIT",
        "signature": "0f04368b2d22aafd0ggc8809ea34297eff602272917b5f01267db4efbc1c9422"
    }
}

Request Weight
0

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	YES	
side	ENUM	YES	
positionSide	ENUM	NO	Default BOTH for One-way Mode ; LONG or SHORT for Hedge Mode. It must be sent in Hedge Mode.
type	ENUM	YES	
timeInForce	ENUM	NO	
quantity	DECIMAL	NO	Cannot be sent with closePosition=true(Close-All)
reduceOnly	STRING	NO	"true" or "false". default "false". Cannot be sent in Hedge Mode; cannot be sent with closePosition=true
price	DECIMAL	NO	
newClientOrderId	STRING	NO	A unique id among open orders. Automatically generated if not sent. Can only be string following the rule: ^[\.A-Z\:/a-z0-9_-]{1,36}$
stopPrice	DECIMAL	NO	Used with STOP/STOP_MARKET or TAKE_PROFIT/TAKE_PROFIT_MARKET orders.
closePosition	STRING	NO	true, false；Close-All，used with STOP_MARKET or TAKE_PROFIT_MARKET.
activationPrice	DECIMAL	NO	Used with TRAILING_STOP_MARKET orders, default as the latest price(supporting different workingType)
callbackRate	DECIMAL	NO	Used with TRAILING_STOP_MARKET orders, min 0.1, max 10 where 1 for 1%
workingType	ENUM	NO	stopPrice triggered by: "MARK_PRICE", "CONTRACT_PRICE". Default "CONTRACT_PRICE"
priceProtect	STRING	NO	"TRUE" or "FALSE", default "FALSE". Used with STOP/STOP_MARKET or TAKE_PROFIT/TAKE_PROFIT_MARKET orders.
newOrderRespType	ENUM	NO	"ACK", "RESULT", default "ACK"
priceMatch	ENUM	NO	only avaliable for LIMIT/STOP/TAKE_PROFIT order; can be set to OPPONENT/ OPPONENT_5/ OPPONENT_10/ OPPONENT_20: /QUEUE/ QUEUE_5/ QUEUE_10/ QUEUE_20; Can't be passed together with price
selfTradePreventionMode	ENUM	NO	NONE:No STP / EXPIRE_TAKER:expire taker order when STP triggers/ EXPIRE_MAKER:expire taker order when STP triggers/ EXPIRE_BOTH:expire both orders when STP triggers; default NONE
goodTillDate	LONG	NO	order cancel time for timeInForce GTD, mandatory when timeInforce set to GTD; order the timestamp only retains second-level precision, ms part will be ignored; The goodTillDate timestamp must be greater than the current time plus 600 seconds and smaller than 253402300799000
recvWindow	LONG	NO	
timestamp	LONG	YES	
Additional mandatory parameters based on type:

Type	Additional mandatory parameters
LIMIT	timeInForce, quantity, price or priceMatch
MARKET	quantity
STOP/TAKE_PROFIT	quantity, stopPrice, price or priceMatch
STOP_MARKET/TAKE_PROFIT_MARKET	stopPrice
TRAILING_STOP_MARKET	callbackRate
Order with type STOP, parameter timeInForce can be sent ( default GTC).

Order with type TAKE_PROFIT, parameter timeInForce can be sent ( default GTC).

Condition orders will be triggered when:

If parameterpriceProtectis sent as true:
when price reaches the stopPrice ，the difference rate between "MARK_PRICE" and "CONTRACT_PRICE" cannot be larger than the "triggerProtect" of the symbol
"triggerProtect" of a symbol can be got from GET /fapi/v1/exchangeInfo
STOP, STOP_MARKET:
BUY: latest price ("MARK_PRICE" or "CONTRACT_PRICE") >= stopPrice
SELL: latest price ("MARK_PRICE" or "CONTRACT_PRICE") <= stopPrice
TAKE_PROFIT, TAKE_PROFIT_MARKET:
BUY: latest price ("MARK_PRICE" or "CONTRACT_PRICE") <= stopPrice
SELL: latest price ("MARK_PRICE" or "CONTRACT_PRICE") >= stopPrice
TRAILING_STOP_MARKET:
BUY: the lowest price after order placed <= activationPrice, and the latest price >= the lowest price * (1 + callbackRate)
SELL: the highest price after order placed >= activationPrice, and the latest price <= the highest price * (1 - callbackRate)
For TRAILING_STOP_MARKET, if you got such error code.
{"code": -2021, "msg": "Order would immediately trigger."}
means that the parameters you send do not meet the following requirements:

BUY: activationPrice should be smaller than latest price.
SELL: activationPrice should be larger than latest price.
If newOrderRespType is sent as RESULT :

MARKET order: the final FILLED result of the order will be return directly.
LIMIT order with special timeInForce: the final status result of the order(FILLED or EXPIRED) will be returned directly.
STOP_MARKET, TAKE_PROFIT_MARKET with closePosition=true:

Follow the same rules for condition orders.
If triggered，close all current long position( if SELL) or current short position( if BUY).
Cannot be used with quantity paremeter
Cannot be used with reduceOnly parameter
In Hedge Mode,cannot be used with BUY orders in LONG position side. and cannot be used with SELL orders in SHORT position side
Response Example
{
    "id": "3f7df6e3-2df4-44b9-9919-d2f38f90a99a",
    "status": 200,
    "result": {
        "orderId": 325078477,
        "symbol": "BTCUSDT",
        "status": "NEW",
        "clientOrderId": "iCXL1BywlBaf2sesNUrVl3",
        "price": "43187.00",
        "avgPrice": "0.00",
        "origQty": "0.100",
        "executedQty": "0.000",
        "cumQty": "0.000",
        "cumQuote": "0.00000",
        "timeInForce": "GTC",
        "type": "LIMIT",
        "reduceOnly": false,
        "closePosition": false,
        "side": "BUY",
        "positionSide": "BOTH",
        "stopPrice": "0.00",
        "workingType": "CONTRACT_PRICE",
        "priceProtect": false,
        "origType": "LIMIT",
        "priceMatch": "NONE",
        "selfTradePreventionMode": "NONE",
        "goodTillDate": 0,
        "updateTime": 1702555534435
    },
    "rateLimits": [
        {
            "rateLimitType": "ORDERS",
            "interval": "SECOND",
            "intervalNum": 10,
            "limit": 300,
            "count": 1
        },
        {
            "rateLimitType": "ORDERS",
            "interval": "MINUTE",
            "intervalNum": 1,
            "limit": 1200,
            "count": 1
        },
        {
            "rateLimitType": "REQUEST_WEIGHT",
            "interval": "MINUTE",
            "intervalNum": 1,
            "limit": 2400,
            "count": 1
        }
    ]
}


Modify Order (TRADE)
API Description
Order modify function, currently only LIMIT order modification is supported, modified orders will be reordered in the match queue

Method
order.modify

Request
{
    "id": "c8c271ba-de70-479e-870c-e64951c753d9",
    "method": "order.modify",
    "params": {
        "apiKey": "HMOchcfiT9ZRZnhjp2XjGXhsOBd6msAhKz9joQaWwZ7arcJTlD2hGPHQj1lGdTjR",
        "orderId": 328971409,
        "origType": "LIMIT",
        "positionSide": "SHORT",
        "price": "43769.1",
        "priceMatch": "NONE",
        "quantity": "0.11",
        "side": "SELL",
        "symbol": "BTCUSDT",
        "timestamp": 1703426755754,
        "signature": "d30c9f0736a307f5a9988d4a40b688662d18324b17367d51421da5484e835923"
    }
}

Request Weight
1 on 10s order rate limit(X-MBX-ORDER-COUNT-10S); 1 on 1min order rate limit(X-MBX-ORDER-COUNT-1M); 1 on IP rate limit(x-mbx-used-weight-1m)

Request Parameters
Name	Type	Mandatory	Description
orderId	LONG	NO	
origClientOrderId	STRING	NO	
symbol	STRING	YES	
side	ENUM	YES	SELL, BUY
quantity	DECIMAL	YES	Order quantity, cannot be sent with closePosition=true
price	DECIMAL	YES	
priceMatch	ENUM	NO	only avaliable for LIMIT/STOP/TAKE_PROFIT order; can be set to OPPONENT/ OPPONENT_5/ OPPONENT_10/ OPPONENT_20: /QUEUE/ QUEUE_5/ QUEUE_10/ QUEUE_20; Can't be passed together with price
recvWindow	LONG	NO	
timestamp	LONG	YES	
Either orderId or origClientOrderId must be sent, and the orderId will prevail if both are sent.
Both quantity and price must be sent, which is different from dapi modify order endpoint.
When the new quantity or price doesn't satisfy PRICE_FILTER / PERCENT_FILTER / LOT_SIZE, amendment will be rejected and the order will stay as it is.
However the order will be cancelled by the amendment in the following situations:
when the order is in partially filled status and the new quantity <= executedQty
When the order is GTX and the new price will cause it to be executed immediately
One order can only be modfied for less than 10000 times
Response Example
{
    "id": "c8c271ba-de70-479e-870c-e64951c753d9",
    "status": 200,
    "result": {
        "orderId": 328971409,
        "symbol": "BTCUSDT",
        "status": "NEW",
        "clientOrderId": "xGHfltUMExx0TbQstQQfRX",
        "price": "43769.10",
        "avgPrice": "0.00",
        "origQty": "0.110",
        "executedQty": "0.000",
        "cumQty": "0.000",
        "cumQuote": "0.00000",
        "timeInForce": "GTC",
        "type": "LIMIT",
        "reduceOnly": false,
        "closePosition": false,
        "side": "SELL",
        "positionSide": "SHORT",
        "stopPrice": "0.00",
        "workingType": "CONTRACT_PRICE",
        "priceProtect": false,
        "origType": "LIMIT",
        "priceMatch": "NONE",
        "selfTradePreventionMode": "NONE",
        "goodTillDate": 0,
        "updateTime": 1703426756190
    },
    "rateLimits": [
        {
            "rateLimitType": "ORDERS",
            "interval": "SECOND",
            "intervalNum": 10,
            "limit": 300,
            "count": 1
        },
        {
            "rateLimitType": "ORDERS",
            "interval": "MINUTE",
            "intervalNum": 1,
            "limit": 1200,
            "count": 1
        },
        {
            "rateLimitType": "REQUEST_WEIGHT",
            "interval": "MINUTE",
            "intervalNum": 1,
            "limit": 2400,
            "count": 1
        }
    ]
}

Cancel Order (TRADE)
API Description
Cancel an active order.

Method
order.cancel

Request
{
   	"id": "5633b6a2-90a9-4192-83e7-925c90b6a2fd",
    "method": "order.cancel", 
    "params": { 
        "apiKey": "HsOehcfih8ZRxnhjp2XjGXhsOBd6msAhKz9joQaWwZ7arcJTlD2hGOGQj1lGdTjR", 
        "orderId": 283194212, 
        "symbol": "BTCUSDT", 
        "timestamp": 1703439070722, 
        "signature": "b09c49815b4e3f1f6098cd9fbe26a933a9af79803deaaaae03c29f719c08a8a8" 
    }
}

Request Weight
1

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	YES	
orderId	LONG	NO	
origClientOrderId	STRING	NO	
recvWindow	LONG	NO	
timestamp	LONG	YES	
Either orderId or origClientOrderId must be sent.
Response Example
{
  "id": "5633b6a2-90a9-4192-83e7-925c90b6a2fd",
  "status": 200,
  "result": {
    "clientOrderId": "myOrder1",
    "cumQty": "0",
    "cumQuote": "0",
    "executedQty": "0",
    "orderId": 283194212,
    "origQty": "11",
    "origType": "TRAILING_STOP_MARKET",
    "price": "0",
    "reduceOnly": false,
    "side": "BUY",
    "positionSide": "SHORT",
    "status": "CANCELED",
    "stopPrice": "9300",                
    "closePosition": false,  
    "symbol": "BTCUSDT",
    "timeInForce": "GTC",
    "type": "TRAILING_STOP_MARKET",
    "activatePrice": "9020",            
    "priceRate": "0.3",                
    "updateTime": 1571110484038,
    "workingType": "CONTRACT_PRICE",
    "priceProtect": false,           
    "priceMatch": "NONE",              
    "selfTradePreventionMode": "NONE",
    "goodTillDate": 0                 
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 1
    }
  ]
}


Query Order (USER_DATA)
API Description
Check an order's status.

These orders will not be found:
order status is CANCELED or EXPIRED AND order has NO filled trade AND created time + 3 days < current time
order create time + 90 days < current time
Method
order.status

Request
{
    "id": "0ce5d070-a5e5-4ff2-b57f-1556741a4204",
    "method": "order.status",
    "params": {
        "apiKey": "HMOchcfii9ZRZnhjp2XjGXhsOBd6msAhKz9joQaWwZ7arcJTlD2hGPHQj1lGdTjR",
        "orderId": 328999071,
        "symbol": "BTCUSDT",
        "timestamp": 1703441060152,
        "signature": "ba48184fc38a71d03d2b5435bd67c1206e3191e989fe99bda1bc643a880dfdbf"
    }
}

Request Weight
1

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	YES	
orderId	LONG	NO	
origClientOrderId	STRING	NO	
recvWindow	LONG	NO	
timestamp	LONG	YES	
Notes:

Either orderId or origClientOrderId must be sent.
orderId is self-increment for each specific symbol
Response Example
{
 "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
 "status": 200,
 "result": {
  "avgPrice": "0.00000",
  "clientOrderId": "abc",
  "cumQuote": "0",
  "executedQty": "0",
  "orderId": 1917641,
  "origQty": "0.40",
  "origType": "TRAILING_STOP_MARKET",
  "price": "0",
  "reduceOnly": false,
  "side": "BUY",
  "positionSide": "SHORT",
  "status": "NEW",
  "stopPrice": "9300",    // please ignore when order type is TRAILING_STOP_MARKET
  "closePosition": false,   // if Close-All
  "symbol": "BTCUSDT",
  "time": *************,    // order time
  "timeInForce": "GTC",
  "type": "TRAILING_STOP_MARKET",
  "activatePrice": "9020",   // activation price, only return with TRAILING_STOP_MARKET order
  "priceRate": "0.3",     // callback rate, only return with TRAILING_STOP_MARKET order
  "updateTime": *************,  // update time
  "workingType": "CONTRACT_PRICE",
  "priceProtect": false            // if conditional order trigger is protected
 }
}


Position Information V2 (USER_DATA)
API Description
Get current position information(only symbol that has position or open orders will be returned).

Method
v2/account.position

Request
{
   	"id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
    "method": "v2/account.position",
    "params": {
        "apiKey": "xTaDyrmvA9XT2oBHHjy39zyPzKCvMdtH3b9q4xadkAg2dNSJXQGCxzui26L823W2",
        "symbol": "BTCUSDT",
        "timestamp": *************,
        "signature": "31ab02a51a3989b66c29d40fcdf78216978a60afc6d8dc1c753ae49fa3164a2a"
    }
}

Request Weight
5

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	NO	
recvWindow	LONG	NO	
timestamp	LONG	YES	
Note

Please use with user data stream ACCOUNT_UPDATE to meet your timeliness and accuracy needs.
Response Example
For One-way position mode:

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": [
    {
	    "symbol": "BTCUSDT",  
	    "positionSide": "BOTH",            // 持仓方向
	    "positionAmt": "1.000",  
	    "entryPrice": "0.00000",
	    "breakEvenPrice": "0.0",  
	    "markPrice": "6679.********",
	    "unrealizedProfit": "0.********",  // 持仓未实现盈亏 
	    "liquidationPrice": "0",  
	    "isolatedMargin": "0.********",	
	    "notional": "0",
	    "marginAsset": "USDT", 
	    "isolatedWallet": "0",
	    "initialMargin": "0",              // 初始保证金
	    "maintMargin": "0",                // 维持保证金
	    "positionInitialMargin": "0",      // 仓位初始保证金
	    "openOrderInitialMargin": "0",     // 订单初始保证金
	    "adl": 0,
	    "bidNotional": "0",  
	    "askNotional": "0",  
	    "updateTime": 0                    // 更新时间
    }
],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}

For Hedge position mode:

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": [
   {
	    "symbol": "BTCUSDT",  
	    "positionSide": "LONG",            
	    "positionAmt": "1.000",  
	    "entryPrice": "0.00000",
	    "breakEvenPrice": "0.0",  
	    "markPrice": "6679.********",
	    "unrealizedProfit": "0.********",  
	    "liquidationPrice": "0",  
	    "isolatedMargin": "0.********",	
	    "notional": "0",
	    "marginAsset": "USDT", 
	    "isolatedWallet": "0",
	    "initialMargin": "0",   
	    "maintMargin": "0",    
	    "positionInitialMargin": "0",      
	    "openOrderInitialMargin": "0",     
	    "adl": 0,
	    "bidNotional": "0",  
	    "askNotional": "0",  
	    "updateTime": 0
    },
    {
	    "symbol": "BTCUSDT",  
	    "positionSide": "SHORT",           
	    "positionAmt": "1.000",  
	    "entryPrice": "0.00000",
	    "breakEvenPrice": "0.0",  
	    "markPrice": "6679.********",
	    "unrealizedProfit": "0.********",  
	    "liquidationPrice": "0",  
	    "isolatedMargin": "0.********",	
	    "notional": "0",
	    "marginAsset": "USDT", 
	    "isolatedWallet": "0",
	    "initialMargin": "0",   
	    "maintMargin": "0",     
	    "positionInitialMargin": "0",      
	    "openOrderInitialMargin": "0",     
	    "adl": 0,
	    "bidNotional": "0",  
	    "askNotional": "0", 
	    "updateTime": 0
    }
  ],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}

Position Information (USER_DATA)
API Description
Get current position information.

Method
account.position

Request
{
   	"id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
    "method": "account.position",
    "params": {
        "apiKey": "xTaDyrmvA9XT2oBHHjy39zyPzKCvMdtH3b9q4xadkAg2dNSJXQGCxzui26L823W2",
        "symbol": "BTCUSDT",
        "timestamp": *************,
        "signature": "31ab02a51a3989b66c29d40fcdf78216978a60afc6d8dc1c753ae49fa3164a2a"
    }
}

Request Weight
5

Request Parameters
Name	Type	Mandatory	Description
symbol	STRING	NO	
recvWindow	LONG	NO	
timestamp	LONG	YES	
Note

Please use with user data stream ACCOUNT_UPDATE to meet your timeliness and accuracy needs.
Response Example
For One-way position mode:

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": [
    {
        "entryPrice": "0.00000",
        "breakEvenPrice": "0.0",  
        "marginType": "isolated", 
        "isAutoAddMargin": "false",
        "isolatedMargin": "0.********", 
        "leverage": "10", 
        "liquidationPrice": "0", 
        "markPrice": "6679.********",   
        "maxNotionalValue": "********", 
        "positionAmt": "0.000",
        "notional": "0", 
        "isolatedWallet": "0",
        "symbol": "BTCUSDT", 
        "unRealizedProfit": "0.********", 
        "positionSide": "BOTH",
        "updateTime": 0
    }
],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}

For Hedge position mode:

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": [
    {
        "symbol": "BTCUSDT",
        "positionAmt": "0.001",
        "entryPrice": "22185.2",
        "breakEvenPrice": "0.0",  
        "markPrice": "21123.05052574",
        "unRealizedProfit": "-1.06214947",
        "liquidationPrice": "19731.45529116",
        "leverage": "4",
        "maxNotionalValue": "1********",
        "marginType": "cross",
        "isolatedMargin": "0.********",
        "isAutoAddMargin": "false",
        "positionSide": "LONG",
        "notional": "21.12305052",
        "isolatedWallet": "0",
        "updateTime": 1655217461579
    },
    {
        "symbol": "BTCUSDT",
        "positionAmt": "0.000",
        "entryPrice": "0.0",
        "breakEvenPrice": "0.0",  
        "markPrice": "21123.05052574",
        "unRealizedProfit": "0.********",
        "liquidationPrice": "0",
        "leverage": "4",
        "maxNotionalValue": "1********",
        "marginType": "cross",
        "isolatedMargin": "0.********",
        "isAutoAddMargin": "false",
        "positionSide": "SHORT",
        "notional": "0",
        "isolatedWallet": "0",
        "updateTime": 0
    }
],
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}

Websocket Market Streams
The connection method for Websocket is：

Base Url: wss://fstream.binance.com
Streams can be access either in a single raw stream or a combined stream
Raw streams are accessed at /ws/<streamName>
Combined streams are accessed at /stream?streams=<streamName1>/<streamName2>/<streamName3>
Example:
wss://fstream.binance.com/ws/bnbusdt@aggTrade
wss://fstream.binance.com/stream?streams=bnbusdt@aggTrade/btcusdt@markPrice
Combined stream events are wrapped as follows: {"stream":"<streamName>","data":<rawPayload>}

All symbols for streams are lowercase

A single connection is only valid for 24 hours; expect to be disconnected at the 24 hour mark

The websocket server will send a ping frame every 3 minutes. If the websocket server does not receive a pong frame back from 
the connection within a 10 minute period, 
the connection will be disconnected. 
Unsolicited pong frames are allowed(the client can send pong frames at a frequency higher than every 15 minutes to maintain the connection).

WebSocket connections have a limit of 10 incoming messages per second.

A connection that goes beyond the limit will be disconnected; IPs that are repeatedly disconnected may be banned.

A single connection can listen to a maximum of 1024 streams.

Considering the possible data latency from RESTful endpoints during an extremely volatile market, 
it is highly recommended to get the order status, position, etc from the Websocket user data stream.

Live Subscribing/Unsubscribing to streams
The following data can be sent through the websocket instance in order to subscribe/unsubscribe from streams. Examples can be seen below.
The id used in the JSON payloads is an unsigned INT used as an identifier to uniquely identify the messages going back and forth.
Subscribe to a stream
Response

{
  "result": null,
  "id": 1
}

Request

{
"method": "SUBSCRIBE",
"params":
[
"btcusdt@aggTrade",
"btcusdt@depth"
],
"id": 1
}

Unsubscribe to a stream
Response

{
  "result": null,
  "id": 312
}

Request

{
"method": "UNSUBSCRIBE",
"params":
[
"btcusdt@depth"
],
"id": 312
}

Listing Subscriptions
Response

{
  "result": [
    "btcusdt@aggTrade"
  ],
  "id": 3
}

Request

{
"method": "LIST_SUBSCRIPTIONS",
"id": 3
}

Setting Properties
Currently, the only property can be set is to set whether combined stream payloads are enabled are not. The combined property is set to false when connecting using /ws/ ("raw streams") and true when connecting using /stream/.

Response

{
  "result": null,
  "id": 5
}

Request

{
"method": "SET_PROPERTY",
"params":
[
"combined",
true
],
"id": 5
}

Retrieving Properties
Response

{
  "result": true, // Indicates that combined is set to true.
  "id": 2
}

Request

{
"method": "GET_PROPERTY",
"params":
[
"combined"
],
"id": 2
}

Error Messages
Error Message	Description
{"code": 0, "msg": "Unknown property"}	Parameter used in the SET_PROPERTY or GET_PROPERTY was invalid
{"code": 1, "msg": "Invalid value type: expected Boolean"}	Value should only be true or false
{"code": 2, "msg": "Invalid request: property name must be a string"}	Property name provided was invalid
{"code": 2, "msg": "Invalid request: request ID must be an unsigned integer"}	Parameter id had to be provided or the value provided in the id parameter is an unsupported type
{"code": 2, "msg": "Invalid request: unknown variant %s, expected one of SUBSCRIBE, UNSUBSCRIBE, LIST_SUBSCRIPTIONS, SET_PROPERTY, GET_PROPERTY at line 1 column 28"}	Possible typo in the provided method or provided method was neither of the expected values
{"code": 2, "msg": "Invalid request: too many parameters"}	Unnecessary parameters provided in the data
{"code": 2, "msg": "Invalid request: property name must be a string"}	Property name was not provided
{"code": 2, "msg": "Invalid request: missing field method at line 1 column 73"}	method was not provided in the data
{"code":3,"msg":"Invalid JSON: expected value at line %s column %s"}	JSON data sent has incorrect syntax.

Aggregate Trade Streams
Stream Description
The Aggregate Trade Streams push market trade information that is aggregated for fills with same price and taking side every 100 milliseconds. Only market trades will be aggregated, which means the insurance fund trades and ADL trades won't be aggregated.

Stream Name
<symbol>@aggTrade

Update Speed
100ms

Response Example
{
  "e": "aggTrade",  // Event type
  "E": ********9,   // Event time
  "s": "BTCUSDT",    // Symbol
  "a": 5933014,		// Aggregate trade ID
  "p": "0.001",     // Price
  "q": "100",       // Quantity
  "f": 100,         // First trade ID
  "l": 105,         // Last trade ID
  "T": ********5,   // Trade time
  "m": true,        // Is the buyer the market maker?
}

Mark Price Stream
Stream Description
Mark price and funding rate for a single symbol pushed every 3 seconds or every second.

Stream Name
<symbol>@markPrice or <symbol>@markPrice@1s

Update Speed
3000ms or 1000ms

Response Example
  {
    "e": "markPriceUpdate",  	// Event type
    "E": 1562305380000,      	// Event time
    "s": "BTCUSDT",          	// Symbol
    "p": "11794.15000000",   	// Mark price
    "i": "11784.62659091",		// Index price
    "P": "11784.25641265",		// Estimated Settle Price, only useful in the last hour before the settlement starts
    "r": "0.00038167",       	// Funding rate
    "T": 1562306400000       	// Next funding time
  }

Mark Price Stream for All market
Stream Description
Mark price and funding rate for all symbols pushed every 3 seconds or every second.

Stream Name
!markPrice@arr or !markPrice@arr@1s

Update Speed
3000ms or 1000ms

Response Example
[ 
  {
    "e": "markPriceUpdate",  	// Event type
    "E": 1562305380000,      	// Event time
    "s": "BTCUSDT",          	// Symbol
    "p": "11185.87786614",   	// Mark price
    "i": "11784.62659091"		// Index price
    "P": "11784.25641265",		// Estimated Settle Price, only useful in the last hour before the settlement starts
    "r": "0.00030000",       	// Funding rate
    "T": 1562306400000       	// Next funding time
  }
]

Kline/Candlestick Streams
Stream Description
The Kline/Candlestick Stream push updates to the current klines/candlestick every 250 milliseconds (if existing).

Kline/Candlestick chart intervals:

m -> minutes; h -> hours; d -> days; w -> weeks; M -> months

1m
3m
5m
15m
30m
1h
2h
4h
6h
8h
12h
1d
3d
1w
1M
Stream Name
<symbol>@kline_<interval>

Update Speed
250ms

Response Example
{
  "e": "kline",     // Event type
  "E": 1638747660000,   // Event time
  "s": "BTCUSDT",    // Symbol
  "k": {
    "t": 1638747660000, // Kline start time
    "T": 1638747719999, // Kline close time
    "s": "BTCUSDT",  // Symbol
    "i": "1m",      // Interval
    "f": 100,       // First trade ID
    "L": 200,       // Last trade ID
    "o": "0.0010",  // Open price
    "c": "0.0020",  // Close price
    "h": "0.0025",  // High price
    "l": "0.0015",  // Low price
    "v": "1000",    // Base asset volume
    "n": 100,       // Number of trades
    "x": false,     // Is this kline closed?
    "q": "1.0000",  // Quote asset volume
    "V": "500",     // Taker buy base asset volume
    "Q": "0.500",   // Taker buy quote asset volume
    "B": "123456"   // Ignore
  }
}


Continuous Contract Kline/Candlestick Streams
Stream Description
Contract type:

perpetual
current_quarter
next_quarter
Kline/Candlestick chart intervals:

m -> minutes; h -> hours; d -> days; w -> weeks; M -> months

1m
3m
5m
15m
30m
1h
2h
4h
6h
8h
12h
1d
3d
1w
1M
Stream Name
<pair>_<contractType>@continuousKline_<interval>

Update Speed
250ms

Response Example
{
  "e":"continuous_kline",	// Event type
  "E":1607443058651,		// Event time
  "ps":"BTCUSDT",			// Pair
  "ct":"PERPETUAL"			// Contract type
  "k":{
    "t":1607443020000,		// Kline start time
    "T":1607443079999,		// Kline close time
    "i":"1m",				// Interval
    "f":116467658886,		// First updateId
    "L":116468012423,		// Last updateId
    "o":"18787.00",			// Open price
    "c":"18804.04",			// Close price
    "h":"18804.04",			// High price
    "l":"18786.54",			// Low price
    "v":"197.664",			// volume
    "n": 543,				// Number of trades
    "x":false,				// Is this kline closed?
    "q":"3715253.19494",	// Quote asset volume
    "V":"184.769",			// Taker buy volume
    "Q":"3472925.84746",	//Taker buy quote asset volume
    "B":"0"					// Ignore
  }
}

Individual Symbol Mini Ticker Stream
Stream Description
24hr rolling window mini-ticker statistics for a single symbol. These are NOT the statistics of the UTC day, but a 24hr rolling window from requestTime to 24hrs before.

Stream Name
<symbol>@miniTicker

Update Speed
2s

Response Example
  {
    "e": "24hrMiniTicker",  // Event type
    "E": ********9,         // Event time
    "s": "BTCUSDT",         // Symbol
    "c": "0.0025",          // Close price
    "o": "0.0010",          // Open price
    "h": "0.0025",          // High price
    "l": "0.0010",          // Low price
    "v": "10000",           // Total traded base asset volume
    "q": "18"               // Total traded quote asset volume
  }


All Market Tickers Streams
Stream Description
24hr rolling window ticker statistics for all symbols. These are NOT the statistics of the UTC day, but a 24hr rolling window from requestTime to 24hrs before. Note that only tickers that have changed will be present in the array.

Stream Name
!ticker@arr

Update Speed
1000ms

Response Example
[
	{
	  "e": "24hrTicker",  // Event type
	  "E": ********9,     // Event time
	  "s": "BTCUSDT",     // Symbol
	  "p": "0.0015",      // Price change
	  "P": "250.00",      // Price change percent
	  "w": "0.0018",      // Weighted average price
	  "c": "0.0025",      // Last price
	  "Q": "10",          // Last quantity
	  "o": "0.0010",      // Open price
	  "h": "0.0025",      // High price
	  "l": "0.0010",      // Low price
	  "v": "10000",       // Total traded base asset volume
	  "q": "18",          // Total traded quote asset volume
	  "O": 0,             // Statistics open time
	  "C": 86400000,      // Statistics close time
	  "F": 0,             // First trade ID
	  "L": 18150,         // Last trade Id
	  "n": 18151          // Total number of trades
	}
]


Individual Symbol Ticker Streams
Stream Description
24hr rolling window ticker statistics for a single symbol. These are NOT the statistics of the UTC day, but a 24hr rolling window from requestTime to 24hrs before.

Stream Name
<symbol>@ticker

Update Speed
2000ms

Response Example
{
  "e": "24hrTicker",  // Event type
  "E": ********9,     // Event time
  "s": "BTCUSDT",     // Symbol
  "p": "0.0015",      // Price change
  "P": "250.00",      // Price change percent
  "w": "0.0018",      // Weighted average price
  "c": "0.0025",      // Last price
  "Q": "10",          // Last quantity
  "o": "0.0010",      // Open price
  "h": "0.0025",      // High price
  "l": "0.0010",      // Low price
  "v": "10000",       // Total traded base asset volume
  "q": "18",          // Total traded quote asset volume
  "O": 0,             // Statistics open time
  "C": 86400000,      // Statistics close time
  "F": 0,             // First trade ID
  "L": 18150,         // Last trade Id
  "n": 18151          // Total number of trades
}


All Market Mini Tickers Stream
Stream Description
24hr rolling window mini-ticker statistics for all symbols. These are NOT the statistics of the UTC day, but a 24hr rolling window from requestTime to 24hrs before. Note that only tickers that have changed will be present in the array.

Stream Name
!miniTicker@arr

Update Speed
1000ms

Response Example
[  
  {
    "e": "24hrMiniTicker",  // Event type
    "E": ********9,         // Event time
    "s": "BTCUSDT",         // Symbol
    "c": "0.0025",          // Close price
    "o": "0.0010",          // Open price
    "h": "0.0025",          // High price
    "l": "0.0010",          // Low price
    "v": "10000",           // Total traded base asset volume
    "q": "18"               // Total traded quote asset volume
  }
]


Individual Symbol Book Ticker Streams
Stream Description
Pushes any update to the best bid or ask's price or quantity in real-time for a specified symbol.

Stream Name
<symbol>@bookTicker

Update Speed
Real-time

Response Example
{
  "e":"bookTicker",			// event type
  "u":400900217,     		// order book updateId
  "E": 1568014460893,  		// event time
  "T": 1568014460891,  		// transaction time
  "s":"BNBUSDT",     		// symbol
  "b":"25.35190000", 		// best bid price
  "B":"31.21000000", 		// best bid qty
  "a":"25.36520000", 		// best ask price
  "A":"40.66000000"  		// best ask qty
}


All Book Tickers Stream
Stream Description
Pushes any update to the best bid or ask's price or quantity in real-time for all symbols.

Stream Name
!bookTicker

Update Speed
5s

Response Example
{
  "e":"bookTicker",			// event type
  "u":400900217,     		// order book updateId
  "E": 1568014460893,  	// event time
  "T": 1568014460891,  	// transaction time
  "s":"BNBUSDT",     		// symbol
  "b":"25.35190000", 		// best bid price
  "B":"31.21000000", 		// best bid qty
  "a":"25.36520000", 		// best ask price
  "A":"40.66000000"  		// best ask qty
}


Liquidation Order Streams
Stream Description
The Liquidation Order Snapshot Streams push force liquidation order information for specific symbol. For each symbol，only the latest one liquidation order within 1000ms will be pushed as the snapshot. If no liquidation happens in the interval of 1000ms, no stream will be pushed.

Stream Name
   <symbol>@forceOrder

Update Speed
1000ms

Response Example
{

	"e":"forceOrder",                   // Event Type
	"E":1568014460893,                  // Event Time
	"o":{
	
		"s":"BTCUSDT",                   // Symbol
		"S":"SELL",                      // Side
		"o":"LIMIT",                     // Order Type
		"f":"IOC",                       // Time in Force
		"q":"0.014",                     // Original Quantity
		"p":"9910",                      // Price
		"ap":"9910",                     // Average Price
		"X":"FILLED",                    // Order Status
		"l":"0.014",                     // Order Last Filled Quantity
		"z":"0.014",                     // Order Filled Accumulated Quantity
		"T":1568014460893,          	 // Order Trade Time
	
	}

}

All Market Liquidation Order Streams
Stream Description
The All Liquidation Order Snapshot Streams push force liquidation order information for all symbols in the market. For each symbol，only the latest one liquidation order within 1000ms will be pushed as the snapshot. If no liquidation happens in the interval of 1000ms, no stream will be pushed.

Stream Name
!forceOrder@arr

Update Speed
1000ms

Response Example
{

	"e":"forceOrder",                   // Event Type
	"E":1568014460893,                  // Event Time
	"o":{
	
		"s":"BTCUSDT",                   // Symbol
		"S":"SELL",                      // Side
		"o":"LIMIT",                     // Order Type
		"f":"IOC",                       // Time in Force
		"q":"0.014",                     // Original Quantity
		"p":"9910",                      // Price
		"ap":"9910",                     // Average Price
		"X":"FILLED",                    // Order Status
		"l":"0.014",                     // Order Last Filled Quantity
		"z":"0.014",                     // Order Filled Accumulated Quantity
		"T":1568014460893,          	 // Order Trade Time
	}
}


Partial Book Depth Streams
Stream Description
Top <levels> bids and asks, Valid <levels> are 5, 10, or 20.

Stream Name
<symbol>@depth<levels> OR <symbol>@depth<levels>@500ms OR <symbol>@depth<levels>@100ms.

Update Speed
250ms, 500ms or 100ms

Response Example
{
  "e": "depthUpdate", // Event type
  "E": 1571889248277, // Event time
  "T": 1571889248276, // Transaction time
  "s": "BTCUSDT",
  "U": 390497796,     // First update ID in event
  "u": 390497878,     // Final update ID in event
  "pu": 390497794,    // Final update Id in last stream(ie `u` in last stream)
  "b": [              // Bids to be updated
    [
      "7403.89",      // Price Level to be updated
      "0.002"         // Quantity
    ],
    [
      "7403.90",
      "3.906"
    ],
    [
      "7404.00",
      "1.428"
    ],
    [
      "7404.85",
      "5.239"
    ],
    [
      "7405.43",
      "2.562"
    ]
  ],
  "a": [              // Asks to be updated
    [
      "7405.96",      // Price level to be
      "3.340"         // Quantity
    ],
    [
      "7406.63",
      "4.525"
    ],
    [
      "7407.08",
      "2.475"
    ],
    [
      "7407.15",
      "4.800"
    ],
    [
      "7407.20",
      "0.175"
    ]
  ]
}


Diff. Book Depth Streams
Stream Description
Bids and asks, pushed every 250 milliseconds, 500 milliseconds, 100 milliseconds (if existing)

Stream Name
<symbol>@depth OR <symbol>@depth@500ms OR <symbol>@depth@100ms

Update Speed
250ms, 500ms, 100ms

Response Example
{
  "e": "depthUpdate", // Event type
  "E": ********9,     // Event time
  "T": ********8,     // Transaction time 
  "s": "BTCUSDT",     // Symbol
  "U": 157,           // First update ID in event
  "u": 160,           // Final update ID in event
  "pu": 149,          // Final update Id in last stream(ie `u` in last stream)
  "b": [              // Bids to be updated
    [
      "0.0024",       // Price level to be updated
      "10"            // Quantity
    ]
  ],
  "a": [              // Asks to be updated
    [
      "0.0026",       // Price level to be updated
      "100"          // Quantity
    ]
  ]
}


How to manage a local order book correctly
Open a stream to wss://fstream.binance.com/stream?streams=btcusdt@depth.
Buffer the events you receive from the stream. For same price, latest received update covers the previous one.
Get a depth snapshot from https://fapi.binance.com/fapi/v1/depth?symbol=BTCUSDT&limit=1000 .
Drop any event where u is < lastUpdateId in the snapshot.
The first processed event should have U <= lastUpdateId**AND**u >= lastUpdateId
While listening to the stream, each new event's pu should be equal to the previous event's u, otherwise initialize the process from step 3.
The data in each event is the absolute quantity for a price level.
If the quantity is 0, remove the price level.
Receiving an event that removes a price level that is not in your local order book can happen and is normal.

Composite Index Symbol Information Streams
Stream Description
Composite index information for index symbols pushed every second.

Stream Name
<symbol>@compositeIndex

Update Speed
1000ms

Response Example
{
  "e":"compositeIndex",		// Event type
  "E":1602310596000,		// Event time
  "s":"DEFIUSDT",			// Symbol
  "p":"554.41604065",		// Price
  "C":"baseAsset",
  "c":[      				// Composition
  	{
  		"b":"BAL",			// Base asset
  		"q":"USDT",         // Quote asset
  		"w":"1.04884844",	// Weight in quantity
  		"W":"0.01457800",   // Weight in percentage
  		"i":"24.33521021"   // Index price
  	},
  	{
  		"b":"BAND",
  		"q":"USDT" ,
  		"w":"3.53782729",
  		"W":"0.03935200",
  		"i":"7.26420084"
    }
  ]
}


Contract Info Stream
Stream Description
ContractInfo stream pushes when contract info updates(listing/settlement/contract bracket update). bks field only shows up when bracket gets updated.

Stream Name
!contractInfo

Update Speed
Real-time

Response Example
{
    "e":"contractInfo",          // Event Type
    "E":1669356423908,           // Event Time
    "s":"IOTAUSDT",              // Symbol
    "ps":"IOTAUSDT",             // Pair
    "ct":"PERPETUAL",            // Contract type
    "dt":4133404800000,          // Delivery date time 
    "ot":1569398400000,          // onboard date time 
    "cs":"TRADING",              // Contract status 
    "bks":[
        {
            "bs":1,              // Notional bracket
            "bnf":0,             // Floor notional of this bracket
            "bnc":5000,          // Cap notional of this bracket
            "mmr":0.01,          // Maintenance ratio for this bracket
            "cf":0,              // Auxiliary number for quick calculation 
            "mi":21,             // Min leverage for this bracket
            "ma":50              // Max leverage for this bracket
        },
        {
            "bs":2,
            "bnf":5000,
            "bnc":25000,
            "mmr":0.025,
            "cf":75,
            "mi":11,
            "ma":20
        }
    ]
}


Multi-Assets Mode Asset Index
Stream Description
Asset index for multi-assets mode user

Stream Name
!assetIndex@arr OR <assetSymbol>@assetIndex

Update Speed
1s

Response Example
[
    {
      "e":"assetIndexUpdate",
      "E":*************,
      "s":"ADAUSD",           // asset index symbol
      "i":"0.27462452",       // index price
      "b":"0.10000000",       // bid buffer
      "a":"0.10000000",       // ask buffer
      "B":"0.24716207",       // bid rate
      "A":"0.30208698",       // ask rate
      "q":"0.********",       // auto exchange bid buffer
      "g":"0.********",       // auto exchange ask buffer 
      "Q":"0.********",       // auto exchange bid rate
      "G":"0.********"        // auto exchange ask rate
    },
    {
      "e":"assetIndexUpdate",
      "E":*************,
      "s":"USDTUSD",
      "i":"0.********",  
      "b":"0.********",
      "a":"0.********",
      "B":"0.********",
      "A":"0.********",
      "q":"0.********",
      "g":"0.********",
      "Q":"0.********",
      "G":"0.********"
    }
]


Futures Account Balance V2(USER_DATA)
API Description
Query account balance info

Method
v2/account.balance

Request
{
    "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
    "method": "v2/account.balance",
    "params": {
        "apiKey": "xTaDyrmvA9XT2oBHHjy39zyPzKCvMdtH3b9q4xadkAg2dNSJXQGCxzui26L823W2",
        "timestamp": *************,
        "signature": "208bb94a26f99aa122b1319490ca9cb2798fccc81d9b6449521a26268d53217a"
    }
}

Request Weight
5

Request Parameters
Name	Type	Mandatory	Description
recvWindow	LONG	NO	
timestamp	LONG	YES	
Response Example
{
    "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
    "status": 200,
    "result": [
      {
        "accountAlias": "SgsR",              // unique account code
        "asset": "USDT",  	                // asset name
        "balance": "122607.********",        // wallet balance
        "crossWalletBalance": "23.********", // crossed wallet balance
        "crossUnPnl": "0.********"           // unrealized profit of crossed positions
        "availableBalance": "23.********",   // available balance
        "maxWithdrawAmount": "23.********",  // maximum amount for transfer out
        "marginAvailable": true,             // whether the asset can be used as margin in Multi-Assets mode
        "updateTime": *************
      }
    ],
    "rateLimits": [
      {
        "rateLimitType": "REQUEST_WEIGHT",
        "interval": "MINUTE",
        "intervalNum": 1,
        "limit": 2400,
        "count": 20
      }
    ]
}

Futures Account Balance(USER_DATA)
API Description
Query account balance info

Method
account.balance

Request
{
    "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
    "method": "account.balance",
    "params": {
        "apiKey": "xTaDyrmvA9XT2oBHHjy39zyPzKCvMdtH3b9q4xadkAg2dNSJXQGCxzui26L823W2",
        "timestamp": *************,
        "signature": "208bb94a26f99aa122b1319490ca9cb2798fccc81d9b6449521a26268d53217a"
    }
}

Request Weight
5

Request Parameters
Name	Type	Mandatory	Description
recvWindow	LONG	NO	
timestamp	LONG	YES	
Response Example
{
    "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
    "status": 200,
    "result": [
        {
            "accountAlias": "SgsR",    // unique account code
            "asset": "USDT",    // asset name
            "balance": "122607.********", // wallet balance
            "crossWalletBalance": "23.********", // crossed wallet balance
            "crossUnPnl": "0.********"  // unrealized profit of crossed positions
            "availableBalance": "23.********",       // available balance
            "maxWithdrawAmount": "23.********",     // maximum amount for transfer out
            "marginAvailable": true,    // whether the asset can be used as margin in Multi-Assets mode
            "updateTime": *************
        }
    ],
    "rateLimits": [
      {
        "rateLimitType": "REQUEST_WEIGHT",
        "interval": "MINUTE",
        "intervalNum": 1,
        "limit": 2400,
        "count": 20
      }
    ]
}

Account Information V2(USER_DATA)
API Description
Get current account information. User in single-asset/ multi-assets mode will see different value, see comments in response section for detail.

Method
v2/account.status

Request
{
    "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
    "method": "v2/account.status",
    "params": {
        "apiKey": "xTaDyrmvA9XT2oBHHjy39zyPzKCvMdtH3b9q4xadkAg2dNSJXQGCxzui26L823W2",
        "timestamp": *************,
        "signature": "6bb98ef84170c70ba3d01f44261bfdf50fef374e551e590de22b5c3b729b1d8c"
    }
}

 

Request Weight
5

Request Parameters
Name	Type	Mandatory	Description
recvWindow	LONG	NO	
timestamp	LONG	YES	
Response Example
Single Asset Mode

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": {   
  	"totalInitialMargin": "0.********",            // total initial margin required with current mark price (useless with isolated positions), only for USDT asset
  	"totalMaintMargin": "0.********",  	           // total maintenance margin required, only for USDT asset
  	"totalWalletBalance": "103.********",           // total wallet balance, only for USDT asset
  	"totalUnrealizedProfit": "0.********",         // total unrealized profit, only for USDT asset
  	"totalMarginBalance": "103.********",           // total margin balance, only for USDT asset
  	"totalPositionInitialMargin": "0.********",    // initial margin required for positions with current mark price, only for USDT asset
  	"totalOpenOrderInitialMargin": "0.********",   // initial margin required for open orders with current mark price, only for USDT asset
  	"totalCrossWalletBalance": "103.********",      // crossed wallet balance, only for USDT asset
  	"totalCrossUnPnl": "0.********",	           // unrealized profit of crossed positions, only for USDT asset
  	"availableBalance": "103.********",             // available balance, only for USDT asset
  	"maxWithdrawAmount": "103.********"             // maximum amount for transfer out, only for USDT asset
  	"assets": [ // For assets that are quote assets, USDT/USDC/BTC
  		{
  			"asset": "USDT",			            // asset name
  			"walletBalance": "23.********",         // wallet balance
  			"unrealizedProfit": "0.********",       // unrealized profit
  			"marginBalance": "23.********",         // margin balance
  			"maintMargin": "0.********",	        // maintenance margin required
  			"initialMargin": "0.********",          // total initial margin required with current mark price 
  			"positionInitialMargin": "0.********",  // initial margin required for positions with current mark price
  			"openOrderInitialMargin": "0.********", // initial margin required for open orders with current mark price
  			"crossWalletBalance": "23.********",    // crossed wallet balance
  			"crossUnPnl": "0.********"              // unrealized profit of crossed positions
  			"availableBalance": "23.********",      // available balance
  			"maxWithdrawAmount": "23.********",     // maximum amount for transfer out
  			"updateTime": 1625474304765             // last update time 
  		},   
   		{
  			"asset": "USDC",			            // asset name
  			"walletBalance": "103.********",         // wallet balance
  			"unrealizedProfit": "0.********",       // unrealized profit
  			"marginBalance": "103.********",         // margin balance
  			"maintMargin": "0.********",	        // maintenance margin required
  			"initialMargin": "0.********",          // total initial margin required with current mark price 
  			"positionInitialMargin": "0.********",  // initial margin required for positions with current mark price
  			"openOrderInitialMargin": "0.********", // initial margin required for open orders with current mark price
  			"crossWalletBalance": "103.********",    // crossed wallet balance
  			"crossUnPnl": "0.********"              // unrealized profit of crossed positions
  			"availableBalance": "126.********",      // available balance
  			"maxWithdrawAmount": "103.********",     // maximum amount for transfer out
  			"updateTime": 1625474304765             // last update time 
  		},    
      ],
  	"positions": [  // positions of all symbols user had position/ open orders are returned
  		            // only "BOTH" positions will be returned with One-way mode
  		            // only "LONG" and "SHORT" positions will be returned with Hedge mode
     	  {
             "symbol": "BTCUSDT",   
             "positionSide": "BOTH",            // position side 
             "positionAmt": "1.000",  
             "unrealizedProfit": "0.********",  // unrealized profit      
             "isolatedMargin": "0.********",	
             "notional": "0",
             "isolatedWallet": "0",
             "initialMargin": "0",              // initial margin required with current mark price 
             "maintMargin": "0",                // maintenance margin required
             "updateTime": 0
    	  } 
  	]
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}


Multi-Asset Mode

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": {   
  	"totalInitialMargin": "0.********",            // the sum of USD value of all cross positions/open order initial margin
  	"totalMaintMargin": "0.********",  	           // the sum of USD value of all cross positions maintenance margin
  	"totalWalletBalance": "126.********",          // total wallet balance in USD
  	"totalUnrealizedProfit": "0.********",         // total unrealized profit in USD
  	"totalMarginBalance": "126.********",          // total margin balance in USD
  	"totalPositionInitialMargin": "0.********",    // the sum of USD value of all cross positions initial margin
  	"totalOpenOrderInitialMargin": "0.********",   // initial margin required for open orders with current mark price in USD
  	"totalCrossWalletBalance": "126.********",     // crossed wallet balance in USD
  	"totalCrossUnPnl": "0.********",	           // unrealized profit of crossed positions in USD
  	"availableBalance": "126.********",            // available balance in USD
  	"maxWithdrawAmount": "126.********"            // maximum virtual amount for transfer out in USD
  	"assets": [
  		{
  			"asset": "USDT",			         // asset name
  			"walletBalance": "23.********",      // wallet balance
  			"unrealizedProfit": "0.********",    // unrealized profit
  			"marginBalance": "23.********",      // margin balance
  			"maintMargin": "0.********",	     // maintenance margin required
  			"initialMargin": "0.********",       // total initial margin required with current mark price 
  			"positionInitialMargin": "0.********",    //initial margin required for positions with current mark price
  			"openOrderInitialMargin": "0.********",   // initial margin required for open orders with current mark price
  			"crossWalletBalance": "23.********",      // crossed wallet balance
  			"crossUnPnl": "0.********"       // unrealized profit of crossed positions
  			"availableBalance": "126.********",       // available balance
  			"maxWithdrawAmount": "23.********",     // maximum amount for transfer out
  			"marginAvailable": true,    // whether the asset can be used as margin in Multi-Assets mode
  			"updateTime": 1625474304765 // last update time 
  		},
  		{
  			"asset": "BUSD",			// asset name
  			"walletBalance": "103.********",      // wallet balance
  			"unrealizedProfit": "0.********",    // unrealized profit
  			"marginBalance": "103.********",      // margin balance
  			"maintMargin": "0.********",	    // maintenance margin required
  			"initialMargin": "0.********",    // total initial margin required with current mark price 
  			"positionInitialMargin": "0.********",    //initial margin required for positions with current mark price
  			"openOrderInitialMargin": "0.********",   // initial margin required for open orders with current mark price
  			"crossWalletBalance": "103.********",      // crossed wallet balance
  			"crossUnPnl": "0.********"       // unrealized profit of crossed positions
  			"availableBalance": "126.********",       // available balance
  			"maxWithdrawAmount": "103.********",     // maximum amount for transfer out
  			"marginAvailable": true,    // whether the asset can be used as margin in Multi-Assets mode
  			"updateTime": 1625474304765 // last update time
  		}
  	],
   	"positions": [  // positions of all symbols user had position are returned
                      // only "BOTH" positions will be returned with One-way mode
  		            // only "LONG" and "SHORT" positions will be returned with Hedge mode
     	  {
             "symbol": "BTCUSDT",   
             "positionSide": "BOTH",            // position side 
             "positionAmt": "1.000",  
             "unrealizedProfit": "0.********",  // unrealized profit      
             "isolatedMargin": "0.********",	
             "notional": "0",
             "isolatedWallet": "0",
             "initialMargin": "0",              // initial margin required with current mark price 
             "maintMargin": "0",                // maintenance margin required
             "updateTime": 0
    	  } 
  	] 
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}


Account Information(USER_DATA)
API Description
Get current account information. User in single-asset/ multi-assets mode will see different value, see comments in response section for detail.

Method
account.status

Request
{
    "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
    "method": "account.status",
    "params": {
        "apiKey": "xTaDyrmvA9XT2oBHHjy39zyPzKCvMdtH3b9q4xadkAg2dNSJXQGCxzui26L823W2",
        "timestamp": *************,
        "signature": "6bb98ef84170c70ba3d01f44261bfdf50fef374e551e590de22b5c3b729b1d8c"
    }
}

 

Request Weight
5

Request Parameters
Name	Type	Mandatory	Description
recvWindow	LONG	NO	
timestamp	LONG	YES	
Response Example
Single Asset Mode

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": {
    "feeTier": 0,       // account commission tier 
    "canTrade": true,   // if can trade
    "canDeposit": true,     // if can transfer in asset
    "canWithdraw": true,    // if can transfer out asset
    "updateTime": 0,        // reserved property, please ignore 
    "multiAssetsMargin": false,
    "tradeGroupId": -1,
    "totalInitialMargin": "0.********",    // total initial margin required with current mark price (useless with isolated positions), only for USDT asset
    "totalMaintMargin": "0.********",     // total maintenance margin required, only for USDT asset
    "totalWalletBalance": "23.********",     // total wallet balance, only for USDT asset
    "totalUnrealizedProfit": "0.********",   // total unrealized profit, only for USDT asset
    "totalMarginBalance": "23.********",     // total margin balance, only for USDT asset
    "totalPositionInitialMargin": "0.********",    // initial margin required for positions with current mark price, only for USDT asset
    "totalOpenOrderInitialMargin": "0.********",   // initial margin required for open orders with current mark price, only for USDT asset
    "totalCrossWalletBalance": "23.********",      // crossed wallet balance, only for USDT asset
    "totalCrossUnPnl": "0.********",      // unrealized profit of crossed positions, only for USDT asset
    "availableBalance": "23.********",       // available balance, only for USDT asset
    "maxWithdrawAmount": "23.********"     // maximum amount for transfer out, only for USDT asset
    "assets": [
        {
            "asset": "USDT",            // asset name
            "walletBalance": "23.********",      // wallet balance
            "unrealizedProfit": "0.********",    // unrealized profit
            "marginBalance": "23.********",      // margin balance
            "maintMargin": "0.********",        // maintenance margin required
            "initialMargin": "0.********",    // total initial margin required with current mark price 
            "positionInitialMargin": "0.********",    //initial margin required for positions with current mark price
            "openOrderInitialMargin": "0.********",   // initial margin required for open orders with current mark price
            "crossWalletBalance": "23.********",      // crossed wallet balance
            "crossUnPnl": "0.********"       // unrealized profit of crossed positions
            "availableBalance": "23.********",       // available balance
            "maxWithdrawAmount": "23.********",     // maximum amount for transfer out
            "marginAvailable": true,    // whether the asset can be used as margin in Multi-Assets mode
            "updateTime": 1625474304765 // last update time 
        },
        {
            "asset": "BUSD",            // asset name
            "walletBalance": "103.********",      // wallet balance
            "unrealizedProfit": "0.********",    // unrealized profit
            "marginBalance": "103.********",      // margin balance
            "maintMargin": "0.********",        // maintenance margin required
            "initialMargin": "0.********",    // total initial margin required with current mark price 
            "positionInitialMargin": "0.********",    //initial margin required for positions with current mark price
            "openOrderInitialMargin": "0.********",   // initial margin required for open orders with current mark price
            "crossWalletBalance": "103.********",      // crossed wallet balance
            "crossUnPnl": "0.********"       // unrealized profit of crossed positions
            "availableBalance": "103.********",       // available balance
            "maxWithdrawAmount": "103.********",     // maximum amount for transfer out
            "marginAvailable": true,    // whether the asset can be used as margin in Multi-Assets mode
            "updateTime": 1625474304765 // last update time
        }
    ],
    "positions": [  // positions of all symbols in the market are returned
        // only "BOTH" positions will be returned with One-way mode
        // only "LONG" and "SHORT" positions will be returned with Hedge mode
        {
            "symbol": "BTCUSDT",    // symbol name
            "initialMargin": "0",   // initial margin required with current mark price 
            "maintMargin": "0",     // maintenance margin required
            "unrealizedProfit": "0.********",  // unrealized profit
            "positionInitialMargin": "0",      // initial margin required for positions with current mark price
            "openOrderInitialMargin": "0",     // initial margin required for open orders with current mark price
            "leverage": "100",      // current initial leverage
            "isolated": true,       // if the position is isolated
            "entryPrice": "0.00000",    // average entry price
            "maxNotional": "250000",    // maximum available notional with current leverage
            "bidNotional": "0",  // bids notional, ignore
            "askNotional": "0",  // ask notional, ignore
            "positionSide": "BOTH",     // position side
            "positionAmt": "0",         // position amount
            "updateTime": 0           // last update time
        }
    ]
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}


Multi-Asset Mode

{
  "id": "605a6d20-6588-4cb9-afa0-b0ab087507ba",
  "status": 200,
  "result": {
      "feeTier": 0,       // account commission tier 
      "canTrade": true,   // if can trade
      "canDeposit": true,     // if can transfer in asset
      "canWithdraw": true,    // if can transfer out asset
      "updateTime": 0,        // reserved property, please ignore 
      "multiAssetsMargin": true,
      "tradeGroupId": -1,
      "totalInitialMargin": "0.********",    // the sum of USD value of all cross positions/open order initial margin
      "totalMaintMargin": "0.********",     // the sum of USD value of all cross positions maintenance margin
      "totalWalletBalance": "126.********",     // total wallet balance in USD
      "totalUnrealizedProfit": "0.********",   // total unrealized profit in USD
      "totalMarginBalance": "126.********",     // total margin balance in USD
      "totalPositionInitialMargin": "0.********",    // the sum of USD value of all cross positions initial margin
      "totalOpenOrderInitialMargin": "0.********",   // initial margin required for open orders with current mark price in USD
      "totalCrossWalletBalance": "126.********",      // crossed wallet balance in USD
      "totalCrossUnPnl": "0.********",      // unrealized profit of crossed positions in USD
      "availableBalance": "126.********",       // available balance in USD
      "maxWithdrawAmount": "126.********"     // maximum virtual amount for transfer out in USD
      "assets": [
          {
              "asset": "USDT",            // asset name
              "walletBalance": "23.********",      // wallet balance
              "unrealizedProfit": "0.********",    // unrealized profit
              "marginBalance": "23.********",      // margin balance
              "maintMargin": "0.********",        // maintenance margin required
              "initialMargin": "0.********",    // total initial margin required with current mark price 
              "positionInitialMargin": "0.********",    //initial margin required for positions with current mark price
              "openOrderInitialMargin": "0.********",   // initial margin required for open orders with current mark price
              "crossWalletBalance": "23.********",      // crossed wallet balance
              "crossUnPnl": "0.********"       // unrealized profit of crossed positions
              "availableBalance": "126.********",       // available balance
              "maxWithdrawAmount": "23.********",     // maximum amount for transfer out
              "marginAvailable": true,    // whether the asset can be used as margin in Multi-Assets mode
              "updateTime": 1625474304765 // last update time 
          },
          {
              "asset": "BUSD",            // asset name
              "walletBalance": "103.********",      // wallet balance
              "unrealizedProfit": "0.********",    // unrealized profit
              "marginBalance": "103.********",      // margin balance
              "maintMargin": "0.********",        // maintenance margin required
              "initialMargin": "0.********",    // total initial margin required with current mark price 
              "positionInitialMargin": "0.********",    //initial margin required for positions with current mark price
              "openOrderInitialMargin": "0.********",   // initial margin required for open orders with current mark price
              "crossWalletBalance": "103.********",      // crossed wallet balance
              "crossUnPnl": "0.********"       // unrealized profit of crossed positions
              "availableBalance": "126.********",       // available balance
              "maxWithdrawAmount": "103.********",     // maximum amount for transfer out
              "marginAvailable": true,    // whether the asset can be used as margin in Multi-Assets mode
              "updateTime": 1625474304765 // last update time
          }
      ],
      "positions": [  // positions of all symbols in the market are returned
          // only "BOTH" positions will be returned with One-way mode
          // only "LONG" and "SHORT" positions will be returned with Hedge mode
          {
              "symbol": "BTCUSDT",    // symbol name
              "initialMargin": "0",   // initial margin required with current mark price 
              "maintMargin": "0",     // maintenance margin required
              "unrealizedProfit": "0.********",  // unrealized profit
              "positionInitialMargin": "0",      // initial margin required for positions with current mark price
              "openOrderInitialMargin": "0",     // initial margin required for open orders with current mark price
              "leverage": "100",      // current initial leverage
              "isolated": true,       // if the position is isolated
              "entryPrice": "0.00000",    // average entry price
              "breakEvenPrice": "0.0",    // average entry price
              "maxNotional": "250000",    // maximum available notional with current leverage
              "bidNotional": "0",  // bids notional, ignore
              "askNotional": "0",  // ask notional, ignore
              "positionSide": "BOTH",     // position side
              "positionAmt": "0",         // position amount
              "updateTime": 0           // last update time
          }
      ]
  },
  "rateLimits": [
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 2400,
      "count": 20
    }
  ]
}




