import express from 'express';
import cors from 'cors';
import path from 'path';
import crypto from 'crypto';
import fs from 'fs';
import https from 'https';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import fetch from 'node-fetch';
import { sign, etc } from "@noble/ed25519";
import { sha512 } from "@noble/hashes/sha512";
etc.sha512Sync = sha512;

import { TextEncoder } from 'util';

// Binance API sabitleri
const BINANCE_FUTURES_BASE_URL = "https://fapi.binance.com";

// ESM için __dirname ve __filename equivalent
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Binance credentials dosyasını oku
let credentialsPath;
if (fs.existsSync('/var/www/html/src/config/binance-credentials.json')) {
    credentialsPath = '/var/www/html/src/config/binance-credentials.json';
} else {
    credentialsPath = path.join(__dirname, 'src', 'config', 'binance-credentials.json');
}

console.log(`📂 Credentials dosyası yolu: ${credentialsPath}`);

let credentials;
try {
    const credentialsData = fs.readFileSync(credentialsPath, 'utf8');
    credentials = JSON.parse(credentialsData);
    console.log('✅ Binance credentials başarıyla yüklendi');
} catch (error) {
    console.error('❌ Binance credentials yüklenemedi:', error);
    console.error(`❌ Denenen yol: ${credentialsPath}`);
    process.exit(1);
}

const app = express();
const PORT = 3001;

// --- GÜVENLİ API ANAHTARI YÖNETİMİ ---
// Credentials dosyasından oku
const ED25519_API_KEY = credentials.ed25519ApiKey;
const PRIVATE_KEY_HEX = credentials.privateKeyHex;
const HMAC_API_KEY = credentials.apiKey;
const HMAC_API_SECRET = credentials.apiSecret;

console.log(`🔑 HMAC API Key: ${HMAC_API_KEY}`);
console.log(`🔐 HMAC API Secret (first 8 chars): ${HMAC_API_SECRET.substring(0, 8)}...`);
console.log(`🔑 Ed25519 API Key: ${ED25519_API_KEY}`);
console.log(`🔐 Private Key (first 8 chars): ${PRIVATE_KEY_HEX.substring(0, 8)}...`);

// ÖNEMLİ: CORS middleware'i tüm route'lardan ÖNCE gelmelidir!
app.use(cors({
    origin: ['https://parabot.fun', 'http://localhost:5173', 'http://localhost:8080', 'http://***********:8080'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-MBX-APIKEY', 'X-Requested-With']
}));

// OPTIONS istekleri için özel yanıt
app.options('*', cors());

// Parse JSON bodies
app.use(express.json());

// Timestamp oluşturmak için yardımcı fonksiyon
function getTimestamp() {
    return Date.now();
}

// HMAC imzası oluşturmak için yardımcı fonksiyon
function createSignature(queryString, apiSecret) {
    return crypto.createHmac('sha256', apiSecret).update(queryString).digest('hex');
}

// Türkiye saatini almak için helper function
const getTurkeyTime = () => {
    return new Date().toLocaleString('sv-SE', {
        timeZone: 'Europe/Istanbul',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).replace(' ', 'T') + '.000Z';
};

// Ortam değişkenine göre statik dosya yolu belirle
const isProduction = process.env.NODE_ENV === 'production';
const staticPath = isProduction ? '/var/www/html' : path.join(__dirname, 'dist');
console.log(`📁 Statik dosya yolu: ${staticPath}`);

if (fs.existsSync(staticPath)) {
    // MIME type'ları düzelt
    app.use(express.static(staticPath, {
        setHeaders: (res, path) => {
            if (path.endsWith('.js')) {
                res.setHeader('Content-Type', 'application/javascript');
            } else if (path.endsWith('.mjs')) {
                res.setHeader('Content-Type', 'application/javascript');
            } else if (path.endsWith('.css')) {
                res.setHeader('Content-Type', 'text/css');
            } else if (path.endsWith('.html')) {
                res.setHeader('Content-Type', 'text/html');
            } else if (path.endsWith('.json')) {
                res.setHeader('Content-Type', 'application/json');
            }
        }
    }));
    console.log('✅ Statik dosya servisi aktif (MIME types düzeltildi)');
} else {
    console.warn(`⚠️ Statik dosya yolu bulunamadı: ${staticPath}`);
}

app.use((req, res, next) => {
    const turkeyTime = getTurkeyTime();
    console.log(`${turkeyTime} - ${req.method} ${req.path} - IP: ${req.ip}`);
    next();
});

app.get('/health', (req, res) => {
    const turkeyTime = getTurkeyTime();
    res.json({
        status: 'healthy',
        serverIP: '************',
        timestamp: turkeyTime,
        utcTimestamp: new Date().toISOString(),
        nodeVersion: process.version,
        environment: process.env.NODE_ENV || 'development',
    });
});

app.get('/api/status', (req, res) => {
    const turkeyTime = getTurkeyTime();
    res.json({
        server: 'Crypto Future Streamer API Proxy',
        status: 'running',
        timestamp: turkeyTime
    });
});

// HMAC SHA256 imzalama fonksiyonu
function generateHmacSignature(messageString, secret) {
    return crypto.createHmac('sha256', secret).update(messageString).digest('hex');
}

// Ed25519 imzalama fonksiyonu - noble/ed25519 kullanarak
async function generateEd25519Signature(messageString) {
    const turkeyTime = getTurkeyTime();
    try {
        console.log(`${turkeyTime} - [SIGN] İmzanacak mesaj (string): "${messageString}"`);

        // DÜZELTME: Binance için Ed25519 yerine HMAC-SHA256 kullanılmalı
        // Bu fonksiyon adını değiştirmiyoruz ama içeriğini HMAC-SHA256 ile değiştiriyoruz
        const signature = crypto.createHmac('sha256', HMAC_API_SECRET).update(messageString).digest('hex');

        console.log(`${turkeyTime} - ✅ HMAC-SHA256 imzası oluşturuldu: ${signature}`);
        return signature;

        /* ESKİ ED25519 KOD - ÇALIŞMIYOR
        const messageBytes = Buffer.from(messageString, 'utf8');
        const privateKeyBytes = Buffer.from(PRIVATE_KEY_HEX, 'hex');

        if (privateKeyBytes.length !== 32) {
            throw new Error(`Geçersiz özel anahtar uzunluğu: ${privateKeyBytes.length}. 32 byte olmalıdır.`);
        }

        const signatureBytes = await sign(messageBytes, privateKeyBytes);
        const signatureHex = Buffer.from(signatureBytes).toString('hex');

        console.log(`${turkeyTime} - ✅ Ed25519 imzası oluşturuldu: ${signatureHex}`);
        return signatureHex;
        */
    } catch (error) {
        console.error(`${turkeyTime} - ❌ İmza oluşturulurken hata:`, error);
        throw new Error('İmza oluşturma hatası.');
    }
}

// Binance API Proxy endpoint
app.post('/api/binance/proxy', async (req, res) => {
    const turkeyTime = getTurkeyTime();
    console.log(`${turkeyTime} - 🎉 /api/binance/proxy POST isteği alındı. Body:`, req.body);

    try {
        const { endpoint, method = 'GET', headers: clientHeaders = {}, body: requestBody = null, params: clientParams = {} } = req.body;

        if (!endpoint) {
            return res.status(400).json({ error: 'Endpoint is required', timestamp: turkeyTime });
        }

        const binanceBaseUrl = "https://fapi.binance.com";
        let finalUrl = `${binanceBaseUrl}${endpoint}`;
        const requestOptions = {
            method,
            headers: {
                'User-Agent': 'crypto-future-streamer-server/1.0'
            }
        };

        // Body varsa ekle
        if (requestBody && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            if (typeof requestBody === 'string') {
                requestOptions.body = requestBody;
                if (!requestOptions.headers['Content-Type']) requestOptions.headers['Content-Type'] = 'application/x-www-form-urlencoded';
            } else if (typeof requestBody === 'object') {
                requestOptions.body = JSON.stringify(requestBody);
                if (!requestOptions.headers['Content-Type']) requestOptions.headers['Content-Type'] = 'application/json';
            }
        }

        let timestamp = Date.now();
        let queryString = `timestamp=${timestamp}`;

        // /fapi/v2/account endpoint'i için HMAC imzalama
        if (endpoint === '/fapi/v2/account') {
            const signature = generateHmacSignature(queryString, HMAC_API_SECRET);
            finalUrl = `${finalUrl}?${queryString}&signature=${signature}`;
            requestOptions.headers['X-MBX-APIKEY'] = HMAC_API_KEY;
            console.log(`${turkeyTime} - 🔑 HMAC imza oluşturuldu. Query: ${queryString}, Signature (hex): ${signature.substring(0, 16)}...`);
        }
        // Ed25519 gerektiren endpoint'ler için
        else if (clientHeaders['X-MBX-APIKEY'] && clientHeaders['X-MBX-APIKEY'] === ED25519_API_KEY) {
            let queryParams = {
                timestamp,
                ...clientParams
            };

            // Query string'i parametreleri alfabetik olarak sıralayarak oluştur
            queryString = Object.keys(queryParams)
                .sort()
                .filter(key => queryParams[key] !== undefined)
                .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
                .join('&');

            const signature = await generateEd25519Signature(queryString);
            finalUrl = `${binanceBaseUrl}${endpoint}?${queryString}&signature=${signature}`;
            requestOptions.headers['X-MBX-APIKEY'] = ED25519_API_KEY;
            console.log(`${turkeyTime} - 🔑 Ed25519 imza oluşturuldu. Query: ${queryString}, Signature (hex): ${signature.substring(0, 16)}...`);
        }
        // İmzasız endpoint'ler için (listenKey gibi)
        else if (clientHeaders['X-MBX-APIKEY']) {
            requestOptions.headers['X-MBX-APIKEY'] = clientHeaders['X-MBX-APIKEY'];
        }

        console.log(`${turkeyTime} - 🚀 Sunucu taraflı Binance API İsteği: ${method} ${finalUrl}`);
        console.log(`${turkeyTime} - 📤 Request options:`, JSON.stringify(requestOptions, null, 2));

        const response = await fetch(finalUrl, requestOptions);

        console.log(`${turkeyTime} - 📊 Binance API Yanıt Durumu: ${response.status}`);
        const responseText = await response.text();
        let responseData;

        try {
            responseData = JSON.parse(responseText);
        } catch (parseError) {
            console.warn(`${turkeyTime} - ⚠️ Binance API yanıtı JSON parse edilemedi. Yanıt:`, responseText.substring(0, 200));
            if (!response.ok) {
                return res.status(response.status).json({
                    status: response.status,
                    result: { error: 'Binance API returned non-JSON error', details: responseText },
                    success: false,
                    headers: Object.fromEntries(response.headers.entries()),
                    timestamp: turkeyTime
                });
            }
            responseData = { raw_text: responseText };
        }
        console.log(`${turkeyTime} - 📊 Binance API Yanıt Verisi:`, responseData);

        res.status(response.status).json({
            status: response.status,
            result: responseData,
            success: response.ok,
            headers: Object.fromEntries(response.headers.entries()),
            timestamp: turkeyTime
        });

    } catch (error) {
        console.error(`${turkeyTime} - ❌ Server-side Binance API Proxy Hatası:`, error);
        res.status(500).json({
            error: 'Server-side proxy error',
            message: error.message,
            success: false,
            timestamp: turkeyTime
        });
    }
});

// Binance Futures User Data Stream - Create Listen Key
app.post('/api/binance/fapi/v1/listenKey', async (req, res) => {
    try {
        const apiKey = req.headers['x-mbx-apikey'];

        if (!apiKey) {
            return res.status(400).json({ error: 'API key required' });
        }

        const response = await fetch(`https://fapi.binance.com/fapi/v1/listenKey`, {
            method: 'POST',
            headers: {
                'X-MBX-APIKEY': apiKey,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (response.ok) {
            console.log('✅ Listen Key oluşturuldu');
            res.json(data);
        } else {
            console.error('❌ Listen Key oluşturma hatası:', data);
            res.status(response.status).json(data);
        }
    } catch (error) {
        console.error('❌ Listen Key API hatası:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Binance Futures User Data Stream - Keep Alive Listen Key
app.put('/api/binance/fapi/v1/listenKey', async (req, res) => {
    try {
        const apiKey = req.headers['x-mbx-apikey'];
        const { listenKey } = req.body;

        if (!apiKey || !listenKey) {
            return res.status(400).json({ error: 'API key and listen key required' });
        }

        const response = await fetch(`https://fapi.binance.com/fapi/v1/listenKey`, {
            method: 'PUT',
            headers: {
                'X-MBX-APIKEY': apiKey,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ listenKey })
        });

        if (response.ok) {
            console.log('✅ Listen Key yenilendi');
            res.json({ success: true });
        } else {
            const data = await response.json();
            console.error('❌ Listen Key yenileme hatası:', data);
            res.status(response.status).json(data);
        }
    } catch (error) {
        console.error('❌ Listen Key yenileme API hatası:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Binance Futures User Data Stream - Delete Listen Key
app.delete('/api/binance/fapi/v1/listenKey', async (req, res) => {
    try {
        const apiKey = req.headers['x-mbx-apikey'];
        const { listenKey } = req.body;

        if (!apiKey || !listenKey) {
            return res.status(400).json({ error: 'API key and listen key required' });
        }

        const response = await fetch(`https://fapi.binance.com/fapi/v1/listenKey`, {
            method: 'DELETE',
            headers: {
                'X-MBX-APIKEY': apiKey,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ listenKey })
        });

        if (response.ok) {
            console.log('✅ Listen Key silindi');
            res.json({ success: true });
        } else {
            const data = await response.json();
            console.error('❌ Listen Key silme hatası:', data);
            res.status(response.status).json(data);
        }
    } catch (error) {
        console.error('❌ Listen Key silme API hatası:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Binance Futures V2 Account Information
app.get('/api/binance/fapi/v2/account', async (req, res) => {
    try {
        const apiKey = req.headers['x-mbx-apikey'];
        const apiSecret = req.headers['x-mbx-apisecret'] || process.env.BINANCE_API_SECRET;

        if (!apiKey || !apiSecret) {
            return res.status(400).json({
                error: 'API key ve secret gerekli',
                details: {
                    hasApiKey: !!apiKey,
                    hasApiSecret: !!apiSecret
                }
            });
        }

        const timestamp = getTimestamp();
        const queryString = `timestamp=${timestamp}`;
        const signature = createSignature(queryString, apiSecret);

        console.log('🔑 Making request to Binance with:', {
            url: `${BINANCE_FUTURES_BASE_URL}/fapi/v2/account`,
            hasApiKey: !!apiKey,
            hasSignature: !!signature,
            timestamp
        });

        const response = await fetch(`${BINANCE_FUTURES_BASE_URL}/fapi/v2/account?${queryString}&signature=${signature}`, {
            method: 'GET',
            headers: {
                'X-MBX-APIKEY': apiKey,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        const data = await response.json();

        if (!response.ok) {
            console.error('Binance API error:', {
                status: response.status,
                statusText: response.statusText,
                data
            });
            return res.status(response.status).json(data);
        }

        console.log('✅ Account V2 data retrieved successfully');
        console.log('📊 Account summary:', {
            totalWalletBalance: data.totalWalletBalance,
            availableBalance: data.availableBalance,
            assetsCount: data.assets?.length || 0,
            positionsCount: data.positions?.length || 0
        });

        res.json(data);
    } catch (error) {
        console.error('Account V2 endpoint error:', error);
        res.status(500).json({
            error: 'Server error',
            message: error.message
        });
    }
});

app.get('*', (req, res) => {
    const turkeyTime = getTurkeyTime();

    // API endpoint'leri için 404 döndür
    if (req.path.startsWith('/api/') || req.path.startsWith('/health')) {
        return res.status(404).json({ error: 'API endpoint not found', path: req.path, timestamp: getTurkeyTime() });
    }

    // Static dosyalar için doğru MIME type ile serve et
    if (req.path.endsWith('.js') || req.path.endsWith('.mjs')) {
        const filePath = path.join(staticPath, req.path);
        if (fs.existsSync(filePath)) {
            res.setHeader('Content-Type', 'application/javascript');
            return res.sendFile(filePath);
        }
    }

    if (req.path.endsWith('.css')) {
        const filePath = path.join(staticPath, req.path);
        if (fs.existsSync(filePath)) {
            res.setHeader('Content-Type', 'text/css');
            return res.sendFile(filePath);
        }
    }

    // Diğer tüm istekler için index.html döndür (SPA routing)
    const indexPath = path.join(staticPath, 'index.html');
    if (fs.existsSync(indexPath)) {
        res.setHeader('Content-Type', 'text/html');
        res.sendFile(indexPath);
    } else {
        res.status(404).json({ error: 'Frontend not found', message: `index.html not found at ${indexPath}` });
    }
});

app.use((error, req, res, next) => {
    const turkeyTime = getTurkeyTime();
    console.error(`${turkeyTime} - 🔥 Unhandled server error:`, error);
    res.status(500).json({
        error: 'Internal server error',
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
});

const privateKeyPath = '/etc/letsencrypt/live/parabot.fun/privkey.pem';
const certificatePath = '/etc/letsencrypt/live/parabot.fun/fullchain.pem';

let privateKeySsl;
let certificateSsl;
let isHttps = false;

try {
    privateKeySsl = fs.readFileSync(privateKeyPath, 'utf8');
    certificateSsl = fs.readFileSync(certificatePath, 'utf8');
    console.log('✅ SSL sertifika dosyaları başarıyla okundu.');
    isHttps = true;
} catch (err) {
    console.log(`🔶 SSL sertifika dosyaları bulunamadı: ${err.message}`);
    console.log('🔶 HTTP modunda devam ediliyor (SSL olmadan)');
}

// SSL varsa HTTPS, yoksa HTTP sunucusu başlat
if (isHttps) {
    const httpsOptions = {
        key: privateKeySsl,
        cert: certificateSsl
    };

    https.createServer(httpsOptions, app).listen(PORT, () => {
        console.log(`🚀 Crypto Future Streamer API Proxy Server başlatıldı: https://localhost:${PORT} -> https://parabot.fun`);
    });
} else {
    // HTTP sunucusu başlat
    app.listen(PORT, () => {
        console.log(`🚀 Crypto Future Streamer API Proxy Server başlatıldı: http://localhost:${PORT} (HTTP modunda)`);
    });
}

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    process.exit(0);
});