import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { BinanceWebSocketManager } from '@/services/binanceWs';
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

const BinanceConnectionTester = () => {
    const [apiKey, setApiKey] = useState('');
    const [testnet, setTestnet] = useState(false);
    const [status, setStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
    const [messages, setMessages] = useState<any[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [wsManager, setWsManager] = useState<BinanceWebSocketManager | null>(null);
    const [serverStatus, setServerStatus] = useState<'checking' | 'online' | 'offline'>('checking');

    // API sunucu durumunu kontrol edelim
    useEffect(() => {
        const checkServerStatus = async () => {
            try {
                setServerStatus('checking');
                const response = await fetch('https://parabot.fun:3001/health');
                if (response.ok) {
                    setServerStatus('online');
                } else {
                    setServerStatus('offline');
                }
            } catch (error) {
                setServerStatus('offline');
            }
        };
        checkServerStatus();
    }, []);

    const handleConnect = async () => {
        if (!apiKey) {
            setError('API Key gereklidir');
            return;
        }
        if (serverStatus !== 'online') {
            setError('API sunucusu çevrimdışı! Bağlantı kurulamıyor.');
            return;
        }
        setStatus('connecting');
        setError(null);
        try {
            if (wsManager) wsManager.disconnect();
            const manager = new BinanceWebSocketManager(
                { apiKey, apiSecret: '' },
                (data) => setMessages(prev => [data, ...prev].slice(0, 10)),
                (err) => {
                    setError(`Bağlantı hatası: ${err.message || JSON.stringify(err)}`);
                    setStatus('error');
                },
                testnet
            );
            await manager.connect();
            setStatus('connected');
            setWsManager(manager);
        } catch (err) {
            setError(`Bağlantı hatası: ${err.message}`);
            setStatus('error');
        }
    };

    const handleDisconnect = () => {
        if (wsManager) {
            wsManager.disconnect();
            setWsManager(null);
        }
        setStatus('disconnected');
        setMessages([]);
    };

    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle>Binance WebSocket Bağlantı Testi</CardTitle>
                <CardDescription>
                    Binance Futures API anahtarlarınızı girerek WebSocket bağlantısını test edin
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                {serverStatus === 'checking' && (
                    <Alert>
                        <AlertTitle>Sunucu Kontrol Ediliyor</AlertTitle>
                        <AlertDescription>API sunucu durumu kontrol ediliyor...</AlertDescription>
                    </Alert>
                )}
                {serverStatus === 'offline' && (
                    <Alert variant="destructive">
                        <AlertTitle>Sunucu Çevrimdışı</AlertTitle>
                        <AlertDescription>
                            API sunucusuna (************:3001) bağlanılamıyor.
                            Lütfen sunucunun çalıştığından emin olun.
                        </AlertDescription>
                    </Alert>
                )}
                {serverStatus === 'online' && (
                    <Alert variant="default">
                        <AlertTitle>Sunucu Çevrimiçi</AlertTitle>
                        <AlertDescription>
                            API sunucusu (************:3001) çalışıyor ve hazır durumda.
                        </AlertDescription>
                    </Alert>
                )}
                <div className="space-y-2">
                    <Label htmlFor="apiKey">API Key</Label>
                    <Input
                        id="apiKey"
                        type="text"
                        placeholder="Binance API Key"
                        value={apiKey}
                        onChange={(e) => setApiKey(e.target.value)}
                    />
                </div>
                <div className="flex items-center space-x-2">
                    <Switch
                        id="testnet"
                        checked={testnet}
                        onCheckedChange={setTestnet}
                    />
                    <Label htmlFor="testnet">Testnet Modu (Test sunucularını kullan)</Label>
                </div>
                <div className="p-2 bg-amber-50 border border-amber-200 rounded-md text-amber-700 text-sm">
                    <p className="font-semibold">ÖNEMLİ NOT:</p>
                    <p>Bu bağlantı ************ IP adresli sunucu üzerinden yapılacaktır. Binance bu IP adresine izin vermiş olmalıdır.</p>
                </div>
                {error && (
                    <Alert variant="destructive">
                        <AlertTitle>Hata</AlertTitle>
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                )}
                {status === 'connected' && (
                    <Alert>
                        <AlertTitle>Bağlantı Başarılı</AlertTitle>
                        <AlertDescription>WebSocket bağlantısı kuruldu!</AlertDescription>
                    </Alert>
                )}
                <div className="mt-4">
                    <h4 className="text-sm font-semibold mb-2">Bağlantı Durumu: {status}</h4>
                    {messages.length > 0 && (
                        <div className="border rounded-md p-2 bg-muted/50 max-h-60 overflow-auto">
                            <h5 className="text-xs font-medium mb-1">Son Alınan Mesajlar:</h5>
                            {messages.map((msg, index) => (
                                <div key={index} className="text-xs font-mono bg-muted p-1 mb-1 overflow-x-auto">
                                    {JSON.stringify(msg, null, 2)}
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </CardContent>
            <CardFooter className="flex justify-between">
                <Button
                    variant="outline"
                    onClick={handleDisconnect}
                    disabled={status === 'disconnected'}
                >
                    Bağlantıyı Kes
                </Button>
                <Button
                    onClick={handleConnect}
                    disabled={status === 'connecting' || status === 'connected' || serverStatus !== 'online'}
                >
                    {status === 'connecting' ? 'Bağlanıyor...' : 'Bağlan'}
                </Button>
            </CardFooter>
        </Card>
    );
};

export default BinanceConnectionTester; 
