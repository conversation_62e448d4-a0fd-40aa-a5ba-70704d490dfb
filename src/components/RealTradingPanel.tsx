import React, { useEffect, useState } from 'react';
import { useRealTrading, RealTradeParams } from '@/hooks/useRealTrading';
import useBinanceConnection from '@/hooks/useBinanceConnection';
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { RotateCw, AlertCircle, TrendingUp, TrendingDown, ChevronDown, ChevronUp } from "lucide-react";
import { CoinData } from '@/services/binanceService';

interface RealTradingPanelProps {
    availableSymbols: string[];
    symbolData: Record<string, CoinData>;
}

export const RealTradingPanel: React.FC<RealTradingPanelProps> = ({
    availableSymbols,
    symbolData
}) => {
    const { toast } = useToast();

    // API Kimlik Bilgileri
    const [apiKey, setApiKey] = useState<string>('');
    const [enabled, setEnabled] = useState<boolean>(false);
    const [isTestnet, setIsTestnet] = useState<boolean>(true); // Varsayılan olarak testnet

    // İşlem Ayarları
    const [selectedSymbol, setSelectedSymbol] = useState<string>('BTCUSDT');
    const [tradeAmount, setTradeAmount] = useState<number>(10);
    const [tradeLeverage, setTradeLeverage] = useState<number>(5);
    const [maxPositions, setMaxPositions] = useState<number>(3);
    const [tradeDirection, setTradeDirection] = useState<'long' | 'short'>('long');

    // Binance Bağlantısı
    const binanceConnection = useBinanceConnection({
        apiKey,
        apiSecret: '',
        autoConnect: false
    });

    // Gerçek İşlemler Hook'u
    const realTrading = useRealTrading({
        credentials: {
            apiKey,
            apiSecret: ''
        },
        leverage: tradeLeverage,
        maxOpenPositions: maxPositions,
        minVolumeForTrade: 1000000, // Minimum 24 saatlik hacim (1M$)
        emergencyStopLoss: 15,      // %15 Acil Stop Loss
        maxLeverage: 20,
        restrictLowVolumeCoins: true,
    });

    // Bağlantı ve Pozisyon Yenileme
    useEffect(() => {
        if (enabled && apiKey) {
            // API anahtarları ve etkinleştirme varsa bağlan
            binanceConnection.connect({ apiKey, apiSecret: '' });

            // 30 saniyede bir pozisyonları yenile
            const refreshInterval = setInterval(() => {
                if (binanceConnection.isConnected && binanceConnection.isAuthenticated) {
                    realTrading.refreshPositions();
                }
            }, 30000);

            return () => clearInterval(refreshInterval);
        } else {
            // Bağlantı kesilmesi gerekiyorsa
            binanceConnection.disconnect();
        }
    }, [enabled, apiKey, binanceConnection, realTrading]);

    // API kimlik bilgileri değiştiğinde bağlantıyı güncelle
    useEffect(() => {
        if (apiKey) {
            binanceConnection.updateCredentials(apiKey, '');
        }
    }, [apiKey]);

    // Testnet modu değiştiğinde bağlantıyı güncelle
    useEffect(() => {
    }, [isTestnet]);

    // İşlem gönderme fonksiyonu
    const handlePlaceTrade = async () => {
        if (!binanceConnection.isConnected || !binanceConnection.isAuthenticated) {
            toast({
                title: "Bağlantı Hatası",
                description: "Binance API'sine bağlı değilsiniz. Lütfen önce bağlanın.",
                variant: "destructive",
            });
            return;
        }

        const currentPrice = symbolData[selectedSymbol]?.price || 0;
        const volume24h = symbolData[selectedSymbol]?.volume || 0;

        if (!currentPrice) {
            toast({
                title: "Fiyat Hatası",
                description: `${selectedSymbol} için güncel fiyat alınamadı.`,
                variant: "destructive",
            });
            return;
        }

        const tradeParams: RealTradeParams = {
            symbol: selectedSymbol,
            direction: tradeDirection,
            amount: tradeAmount,
            currentPrice,
            volume24h
        };

        // İşlemi doğrula
        const validation = realTrading.validateRealTrade(tradeParams);
        if (!validation.isValid) {
            toast({
                title: "İşlem Geçersiz",
                description: validation.reason || "Bilinmeyen doğrulama hatası",
                variant: "destructive",
            });
            return;
        }

        // İşlemi gönder
        const result = await realTrading.placeRealTrade(tradeParams);

        if (result.success) {
            toast({
                title: "İşlem Başarılı",
                description: `${selectedSymbol} için ${tradeDirection === 'long' ? 'LONG' : 'SHORT'} pozisyon açıldı.`,
                variant: "default",
            });
        } else {
            toast({
                title: "İşlem Hatası",
                description: result.error || "Bilinmeyen bir hata oluştu",
                variant: "destructive",
            });
        }
    };

    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle className="flex justify-between items-center">
                    <span>Gerçek İşlem Paneli</span>
                    <Badge variant={isTestnet ? "outline" : "destructive"}>
                        {isTestnet ? "TESTNET" : "GERÇEK"}
                    </Badge>
                </CardTitle>
                <CardDescription>
                    {binanceConnection.isConnected
                        ? `Binance Futures API'ye bağlı${isTestnet ? ' (TESTNET)' : ''}`
                        : "Gerçek işlemler için API kimlik bilgilerinizi girin"}
                </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
                <Tabs defaultValue="settings" className="w-full">
                    <TabsList className="grid grid-cols-3 w-full">
                        <TabsTrigger value="settings">Ayarlar</TabsTrigger>
                        <TabsTrigger value="trading">İşlem</TabsTrigger>
                        <TabsTrigger value="positions">Pozisyonlar</TabsTrigger>
                    </TabsList>

                    {/* AYARLAR SEKMESİ */}
                    <TabsContent value="settings" className="space-y-4">
                        <div className="space-y-4 p-4 border rounded-lg bg-card">
                            <div className="flex justify-between items-center">
                                <h3 className="text-lg font-semibold">API Ayarları</h3>
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="real-trading-enabled"
                                        checked={enabled}
                                        onCheckedChange={setEnabled}
                                    />
                                    <Label htmlFor="real-trading-enabled">
                                        {enabled ? "Aktif" : "Devre Dışı"}
                                    </Label>
                                </div>
                            </div>

                            {/* Testnet modu seçimi */}
                            <div className="flex items-center justify-between">
                                <Label htmlFor="testnet-mode">Testnet Modu</Label>
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="testnet-mode"
                                        checked={isTestnet}
                                        onCheckedChange={setIsTestnet}
                                    />
                                    <span className="text-xs">
                                        {isTestnet ?
                                            <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Test</Badge> :
                                            <Badge variant="destructive">Gerçek</Badge>
                                        }
                                    </span>
                                </div>
                            </div>

                            {/* API Anahtarları */}
                            <div className="space-y-2">
                                <Label htmlFor="api-key">API Key</Label>
                                <Input
                                    id="api-key"
                                    type="password"
                                    value={apiKey}
                                    onChange={(e) => setApiKey(e.target.value)}
                                    placeholder="Binance API Key"
                                />
                            </div>

                            {/* Kaldıraç Ayarı */}
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <Label htmlFor="leverage">Kaldıraç</Label>
                                    <span className="text-sm">{tradeLeverage}x</span>
                                </div>
                                <Slider
                                    id="leverage"
                                    min={1}
                                    max={20}
                                    step={1}
                                    value={[tradeLeverage]}
                                    onValueChange={(vals) => setTradeLeverage(vals[0])}
                                />
                            </div>

                            {/* Maksimum Pozisyon Sayısı */}
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <Label htmlFor="max-positions">Maksimum Pozisyon</Label>
                                    <span className="text-sm">{maxPositions}</span>
                                </div>
                                <Slider
                                    id="max-positions"
                                    min={1}
                                    max={10}
                                    step={1}
                                    value={[maxPositions]}
                                    onValueChange={(vals) => setMaxPositions(vals[0])}
                                />
                            </div>

                            {enabled && !isTestnet && (
                                <Alert variant="destructive">
                                    <AlertCircle className="h-4 w-4" />
                                    <AlertTitle>Dikkat!</AlertTitle>
                                    <AlertDescription>
                                        Gerçek işlem modu etkinleştirildi. Gerçek paralarınızla işlem yapılacak!
                                    </AlertDescription>
                                </Alert>
                            )}

                            <div className="pt-2">
                                <Button
                                    onClick={() => binanceConnection.isConnected ?
                                        binanceConnection.disconnect() :
                                        binanceConnection.connect({ apiKey, apiSecret: '' })
                                    }
                                    variant={binanceConnection.isConnected ? "destructive" : "default"}
                                    className="w-full"
                                >
                                    {binanceConnection.isConnecting ? (
                                        <>
                                            <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                                            Bağlanıyor...
                                        </>
                                    ) : binanceConnection.isConnected ? (
                                        "Bağlantıyı Kes"
                                    ) : (
                                        "Bağlan"
                                    )}
                                </Button>
                            </div>
                        </div>
                    </TabsContent>

                    {/* İŞLEM SEKMESİ */}
                    <TabsContent value="trading" className="space-y-4">
                        <div className="space-y-4 p-4 border rounded-lg bg-card">
                            <h3 className="text-lg font-semibold">İşlem Paneli</h3>

                            {/* Bağlantı durumunu göster */}
                            <div className="flex items-center space-x-2 py-2">
                                <div className={`w-3 h-3 rounded-full ${binanceConnection.isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                                <span>{binanceConnection.isConnected ? 'Bağlı' : 'Bağlı Değil'}</span>
                            </div>

                            {/* Sembol Seçimi */}
                            <div className="space-y-2">
                                <Label htmlFor="symbol-select">İşlem Çifti</Label>
                                <Select
                                    value={selectedSymbol}
                                    onValueChange={setSelectedSymbol}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Sembol seçin" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {availableSymbols.map(symbol => (
                                            <SelectItem key={symbol} value={symbol}>
                                                {symbol}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            {/* Seçilen sembolün fiyat bilgisi */}
                            {selectedSymbol && symbolData[selectedSymbol] && (
                                <div className="flex justify-between items-center p-3 border rounded-md">
                                    <div>
                                        <div className="text-sm text-muted-foreground">Fiyat</div>
                                        <div className="text-xl font-semibold">
                                            ${symbolData[selectedSymbol].price?.toFixed(2)}
                                        </div>
                                    </div>
                                    <div>
                                        <div className="text-sm text-muted-foreground">24s Değişim</div>
                                        <div className={`flex items-center ${(symbolData[selectedSymbol].priceChangePercent || 0) >= 0
                                            ? 'text-green-500'
                                            : 'text-red-500'
                                            }`}>
                                            {(symbolData[selectedSymbol].priceChangePercent || 0) >= 0 ? (
                                                <ChevronUp className="h-4 w-4 mr-1" />
                                            ) : (
                                                <ChevronDown className="h-4 w-4 mr-1" />
                                            )}
                                            {Math.abs(symbolData[selectedSymbol].priceChangePercent || 0).toFixed(2)}%
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* İşlem Yönü */}
                            <div className="grid grid-cols-2 gap-4 py-2">
                                <Button
                                    variant={tradeDirection === 'long' ? "success" : "destructive"}
                                    onClick={() => setTradeDirection('long')}
                                    className={tradeDirection === 'long' ? "bg-green-600 hover:bg-green-700" : ""}
                                >
                                    <TrendingUp className="mr-2 h-4 w-4" />
                                    LONG
                                </Button>
                                <Button
                                    variant={tradeDirection === 'short' ? "destructive" : "success"}
                                    onClick={() => setTradeDirection('short')}
                                    className={tradeDirection === 'short' ? "bg-red-600 hover:bg-red-700" : ""}
                                >
                                    <TrendingDown className="mr-2 h-4 w-4" />
                                    SHORT
                                </Button>
                            </div>

                            {/* İşlem Miktarı */}
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <Label htmlFor="trade-amount">İşlem Miktarı (USDT)</Label>
                                    <span className="text-sm">${tradeAmount}</span>
                                </div>
                                <Slider
                                    id="trade-amount"
                                    min={5}
                                    max={100}
                                    step={5}
                                    value={[tradeAmount]}
                                    onValueChange={(vals) => setTradeAmount(vals[0])}
                                />
                                <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>$5</span>
                                    <span>$100</span>
                                </div>
                            </div>

                            {/* İşlem Özeti */}
                            <div className="space-y-2 p-3 border rounded-md bg-muted/50">
                                <h4 className="font-medium">İşlem Özeti</h4>
                                <div className="grid grid-cols-2 gap-1 text-sm">
                                    <div>Sembol:</div>
                                    <div className="font-medium">{selectedSymbol}</div>

                                    <div>Yön:</div>
                                    <div className={`font-medium ${tradeDirection === 'long' ? 'text-green-600' : 'text-red-600'
                                        }`}>
                                        {tradeDirection === 'long' ? 'LONG' : 'SHORT'}
                                    </div>

                                    <div>Tutar:</div>
                                    <div className="font-medium">${tradeAmount}</div>

                                    <div>Kaldıraç:</div>
                                    <div className="font-medium">{tradeLeverage}x</div>

                                    <div>Toplam Değer:</div>
                                    <div className="font-medium">${tradeAmount * tradeLeverage}</div>
                                </div>
                            </div>

                            {/* İşlem Butonu */}
                            <Button
                                onClick={handlePlaceTrade}
                                disabled={!binanceConnection.isConnected || realTrading.isPlacingOrder}
                                className="w-full"
                                variant={tradeDirection === 'long' ? "success" : "destructive"}
                            >
                                {realTrading.isPlacingOrder ? (
                                    <>
                                        <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                                        İşlem Gönderiliyor...
                                    </>
                                ) : (
                                    `${tradeDirection === 'long' ? 'LONG' : 'SHORT'} POZİSYON AÇ`
                                )}
                            </Button>

                            {!binanceConnection.isConnected && (
                                <Alert>
                                    <AlertCircle className="h-4 w-4" />
                                    <AlertTitle>Bağlantı Yok</AlertTitle>
                                    <AlertDescription>
                                        İşlem yapmak için önce API'ye bağlanın.
                                    </AlertDescription>
                                </Alert>
                            )}
                        </div>
                    </TabsContent>

                    {/* POZİSYONLAR SEKMESİ */}
                    <TabsContent value="positions" className="space-y-4">
                        <div className="space-y-4 p-4 border rounded-lg bg-card">
                            <div className="flex justify-between items-center">
                                <h3 className="text-lg font-semibold">Açık Pozisyonlar</h3>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => realTrading.refreshPositions()}
                                >
                                    <RotateCw className="h-4 w-4 mr-2" />
                                    Yenile
                                </Button>
                            </div>

                            {realTrading.positions.length === 0 ? (
                                <div className="text-center py-8 text-muted-foreground">
                                    Açık pozisyon bulunmuyor
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    {realTrading.positions
                                        .filter(pos => parseFloat(pos.positionAmt) !== 0)
                                        .map((position, index) => {
                                            const isLong = parseFloat(position.positionAmt) > 0;
                                            const unrealizedProfit = parseFloat(position.unRealizedProfit);
                                            const entryPrice = parseFloat(position.entryPrice);
                                            const positionSize = Math.abs(parseFloat(position.positionAmt));
                                            const currentPrice = parseFloat(position.markPrice);

                                            return (
                                                <div key={index} className="p-3 border rounded-md">
                                                    <div className="flex justify-between items-center">
                                                        <div className="font-medium">{position.symbol}</div>
                                                        <Badge variant={isLong ? "success" : "destructive"}>
                                                            {isLong ? 'LONG' : 'SHORT'}
                                                        </Badge>
                                                    </div>

                                                    <div className="grid grid-cols-2 gap-2 mt-2 text-sm">
                                                        <div>Miktar:</div>
                                                        <div>{positionSize}</div>

                                                        <div>Giriş Fiyatı:</div>
                                                        <div>${entryPrice.toFixed(2)}</div>

                                                        <div>Güncel Fiyat:</div>
                                                        <div>${currentPrice.toFixed(2)}</div>

                                                        <div>Kâr/Zarar:</div>
                                                        <div className={unrealizedProfit >= 0 ? 'text-green-500' : 'text-red-500'}>
                                                            ${unrealizedProfit.toFixed(2)}
                                                        </div>
                                                    </div>

                                                    <Button
                                                        className="w-full mt-3"
                                                        variant="destructive"
                                                        size="sm"
                                                        onClick={() => {
                                                            // Pozisyonu kapat
                                                            const trade = {
                                                                id: `real_${position.symbol}`,
                                                                symbol: position.symbol,
                                                                entryPrice: entryPrice,
                                                                quantity: positionSize,
                                                                direction: isLong ? 'long' as 'long' : 'short' as 'short',
                                                                openTime: new Date(),
                                                                status: 'open' as 'open',
                                                                binanceOrderId: 0 // Sadece sembol bilgisi kullanılacak
                                                            };

                                                            realTrading.closeRealTrade(trade);
                                                        }}
                                                    >
                                                        Pozisyonu Kapat
                                                    </Button>
                                                </div>
                                            );
                                        })}
                                </div>
                            )}
                        </div>
                    </TabsContent>
                </Tabs>
            </CardContent>

            <CardFooter className="flex justify-between">
                <div className="text-xs text-muted-foreground">
                    {/* Gerçek para uyarısı kaldırıldı */}
                </div>
            </CardFooter>
        </Card>
    );
}; 
