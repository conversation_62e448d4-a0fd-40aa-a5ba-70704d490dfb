import React from 'react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface RealTradingWarningProps {
    isVisible: boolean;
}

const RealTradingWarning: React.FC<RealTradingWarningProps> = ({ isVisible }) => {
    if (!isVisible) {
        return null;
    }

    return (
        <div className="w-full p-2 bg-yellow-100 text-yellow-800 border-b border-yellow-200 flex items-center justify-center text-sm font-medium">
            <AlertCircle className="h-4 w-4 mr-2" />
            GERÇEK PARA İLE İŞLEM YAPIYORSUNUZ! DİKKATLİ OLUN!
        </div>
    );
};

export default RealTradingWarning;
