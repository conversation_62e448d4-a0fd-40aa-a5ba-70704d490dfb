import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useContext7 } from '@/hooks/useContext7';
import { Loader2, Search, ExternalLink } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export const ApiDocumentation = () => {
    const {
        isLoading,
        results,
        searchDocumentation,
        getEndpointDocumentation,
        clearResults
    } = useContext7();

    const [searchQuery, setSearchQuery] = useState('');
    const [activeLibrary, setActiveLibrary] = useState('binance/api');

    const libraries = [
        { id: 'binance/api', name: 'Binance API' },
        { id: 'binance/futures', name: 'Binance Futures' },
        { id: 'crypto/trading', name: 'Kripto Trading' }
    ];

    const handleSearch = async () => {
        if (!searchQuery.trim()) return;
        await searchDocumentation(searchQuery, activeLibrary);
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            handleSearch();
        }
    };

    const handleLibraryChange = (libraryId: string) => {
        setActiveLibrary(libraryId);
        if (searchQuery.trim()) {
            searchDocumentation(searchQuery, libraryId);
        }
    };

    return (
        <Card className="w-full">
            <CardHeader className="pb-3">
                <CardTitle className="text-xl flex justify-between items-center">
                    <span>API Belgeleri</span>
                    <div className="flex gap-2">
                        {libraries.map(lib => (
                            <Badge
                                key={lib.id}
                                variant={activeLibrary === lib.id ? "default" : "outline"}
                                className="cursor-pointer"
                                onClick={() => handleLibraryChange(lib.id)}
                            >
                                {lib.name}
                            </Badge>
                        ))}
                    </div>
                </CardTitle>
                <CardDescription>
                    Binance ve kripto trading API belgeleri için arama yapın
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="flex gap-2 mb-4">
                    <Input
                        placeholder="API endpoint veya konu arayın..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyDown={handleKeyDown}
                        className="flex-1"
                    />
                    <Button onClick={handleSearch} disabled={isLoading || !searchQuery.trim()}>
                        {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Search className="h-4 w-4 mr-2" />}
                        Ara
                    </Button>
                </div>

                <div className="space-y-4 max-h-[400px] overflow-y-auto">
                    {results.length === 0 ? (
                        <div className="text-center text-muted-foreground py-8">
                            {isLoading ? (
                                <div className="flex flex-col items-center">
                                    <Loader2 className="h-8 w-8 animate-spin mb-2" />
                                    <span>Belgeler aranıyor...</span>
                                </div>
                            ) : (
                                <span>Belge araması yapmak için bir sorgu girin</span>
                            )}
                        </div>
                    ) : (
                        results.map((result, index) => (
                            <Card key={index} className="bg-muted/50">
                                <CardHeader className="py-3">
                                    <CardTitle className="text-md flex justify-between items-center">
                                        {result.title}
                                        <Badge variant="outline">{result.language}</Badge>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="py-2">
                                    <div className="text-sm text-muted-foreground whitespace-pre-line">
                                        {result.content.length > 150
                                            ? `${result.content.substring(0, 150)}...`
                                            : result.content}
                                    </div>
                                </CardContent>
                                {result.link && (
                                    <CardFooter className="pt-0 pb-3">
                                        <a
                                            href={result.link}
                                            target="_blank"
                                            rel="noreferrer"
                                            className="text-xs flex items-center text-blue-600 hover:underline"
                                        >
                                            <ExternalLink className="h-3 w-3 mr-1" />
                                            Belgeleri Görüntüle
                                        </a>
                                    </CardFooter>
                                )}
                            </Card>
                        ))
                    )}
                </div>
            </CardContent>
        </Card>
    );
};

export default ApiDocumentation; 
