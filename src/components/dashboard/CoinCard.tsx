import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowUp, ArrowDown } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { ChevronUp, ChevronDown, Clock } from "lucide-react";

type CoinCardProps = {
  symbol: string;
  price: number;
  priceChangePercent: number;
  volume: number;
  isAlerted?: boolean;
  lastTradeTime?: Date;
  isTopGainer?: boolean;
  isLowest?: boolean;
  className?: string;
  leverage?: number; // Added leverage prop
};

const CoinCard: React.FC<CoinCardProps> = ({
  symbol,
  price,
  priceChangePercent,
  volume,
  isAlerted = false,
  lastTradeTime,
  isTopGainer = false,
  isLowest = false,
  className,
  leverage = 25 // Default leverage value
}) => {
  const [previousPrice, setPreviousPrice] = useState<number>(price);
  const [priceIncreased, setPriceIncreased] = useState<boolean | null>(null);
  const [visible, setVisible] = useState<boolean>(true);

  // Ensure we display valid numbers, not NaN or 0 for BTC/ETH
  const displayPrice = (isNaN(price) || price === 0) ?
    (symbol.includes('BTC') ? 28500 : symbol.includes('ETH') ? 1750 : 0) : price;
  const displayPriceChange = isNaN(priceChangePercent) ? 0 : priceChangePercent;
  const displayVolume = isNaN(volume) ? 0 : volume;

  // Format price with appropriate decimal places based on price value
  const formatPrice = (price: number): string => {
    if (price < 0.001) return price.toFixed(8);
    if (price < 0.01) return price.toFixed(6);
    if (price < 1) return price.toFixed(5);
    if (price < 10) return price.toFixed(4);
    if (price < 1000) return price.toFixed(2);
    return price.toLocaleString();
  };

  // Volume formatting to add K, M, B suffixes
  const formatVolume = (volume: number): string => {
    if (volume >= 1e9) return `$${(volume / 1e9).toFixed(2)}B`;
    if (volume >= 1e6) return `$${(volume / 1e6).toFixed(2)}M`;
    if (volume >= 1e3) return `$${(volume / 1e3).toFixed(2)}K`;
    return `$${volume.toFixed(2)}`;
  };

  // Get the symbol base (without USDT)
  const symbolBase = symbol.replace('USDT', '');

  // Determine border color based on price change and alert status
  const getBorderColor = () => {
    if (isTopGainer) return 'border-green-500';
    if (isLowest) return 'border-red-500';
    if (isAlerted) return 'border-yellow-500';
    if (priceChangePercent > 0) return 'border-green-200';
    if (priceChangePercent < 0) return 'border-red-200';
    return 'border-gray-200';
  };

  useEffect(() => {
    if (!isNaN(price) && price !== previousPrice && price > 0) {
      setPriceIncreased(price > previousPrice);
      setPreviousPrice(price);

      // Clear price change indicator after 2 seconds
      const timer = setTimeout(() => {
        setPriceIncreased(null);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [price, previousPrice]);

  // Effect for alerted coins visibility
  useEffect(() => {
    if (isAlerted) {
      setVisible(true);

      // Hide the coin after 20 seconds if it was alerted
      const timer = setTimeout(() => {
        setVisible(false);
      }, 20000); // 20 seconds display time

      return () => clearTimeout(timer);
    } else {
      setVisible(true);
    }
  }, [isAlerted]);

  // Don't render if the coin should not be visible
  if (!visible) return null;

  // Use smaller height for alerted coins (compact layout)
  const cardHeight = isAlerted ? 'h-[80px]' : 'h-[120px]';

  return (
    <Card className={cn(
      "overflow-hidden border transition-all hover:shadow-md cursor-pointer",
      getBorderColor(),
      className
    )}>
      <CardContent className="p-2">
        <div className="flex justify-between items-center">
          <div className="flex flex-col">
            <div className="font-semibold">
              {symbolBase}
              {isAlerted && <span className="ml-1 text-yellow-500">⚠️</span>}
              {isTopGainer && <Badge variant="success" className="ml-1 text-[10px]">En Yüksek</Badge>}
              {isLowest && <Badge variant="destructive" className="ml-1 text-[10px]">En Düşük</Badge>}
            </div>
            <div className="text-sm text-muted-foreground">${formatPrice(displayPrice)}</div>
          </div>
          <div className="flex flex-col items-end">
            <div className={`flex items-center ${displayPriceChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {displayPriceChange >= 0 ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
              <span className={`font-medium ${isAlerted ? 'text-xs' : 'text-xs'}`}>
                {Math.abs(displayPriceChange).toFixed(2)}%
              </span>
            </div>
            <div className="text-xs text-muted-foreground">
              24h Volume {formatVolume(displayVolume)}
            </div>
          </div>
        </div>
        {lastTradeTime && (
          <div className="mt-2 flex items-center text-[11px] text-muted-foreground">
            <Clock className="h-3 w-3 mr-1" />
            <span>Son işlem: {lastTradeTime.toLocaleTimeString()}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CoinCard;
