import React from 'react';
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import CoinCard from "@/components/dashboard/CoinCard";
import { Badge } from "@/components/ui/badge";

interface CoinCardData {
    symbol: string;
    price: number;
    priceChangePercent: number;
    volume: number;
    isAlerted?: boolean;
    lastTradeTime?: Date;
    isTopGainer?: boolean;
    isLowest?: boolean;
}

interface CoinSummaryProps {
    alertedCoins: Set<string>;
    marketData: any[] | null;
    movingAverages: Record<string, { average: number; history: number[] }>;
    lastTradeTimeMap: Record<string, Date>;
    configSettings: {
        alertPercentage: number;
    };
    criteriaEligibleCoins: Set<string>;
    lastCriteriaCheck: Record<string, {
        timestamp: number;
        signal: 'long' | 'short' | null;
        confidence: number;
        reasons: string[];
        blockingReasons: string[];
        canTrade: boolean;
    }>;
}

const CoinSummary: React.FC<CoinSummaryProps> = ({
    alertedCoins,
    marketData,
    movingAverages,
    lastTradeTimeMap,
    configSettings,
    criteriaEligibleCoins,
    lastCriteriaCheck
}) => {

    // En çok yükselen coini bul
    const getTopCoin = React.useMemo(() => {
        if (!marketData || !Array.isArray(marketData)) return null;

        let topGainer = { symbol: '', price: 0, priceChangePercent: -100, volume: 0 };

        marketData.forEach(ticker => {
            const changePercent = parseFloat(ticker.P || '0');
            if (changePercent > topGainer.priceChangePercent) {
                topGainer = {
                    symbol: ticker.s || '',
                    price: parseFloat(ticker.c || '0'),
                    priceChangePercent: changePercent,
                    volume: parseFloat(ticker.q || '0')
                };
            }
        });

        return topGainer.symbol ? {
            ...topGainer,
            isTopGainer: true,
            lastTradeTime: lastTradeTimeMap[topGainer.symbol]
        } : null;
    }, [marketData, lastTradeTimeMap]);

    // En çok düşen coini bul
    const getLowestCoin = React.useMemo(() => {
        if (!marketData || !Array.isArray(marketData)) return null;

        let topLoser = { symbol: '', price: 0, priceChangePercent: 100, volume: 0 };

        marketData.forEach(ticker => {
            const changePercent = parseFloat(ticker.P || '0');
            if (changePercent < topLoser.priceChangePercent) {
                topLoser = {
                    symbol: ticker.s || '',
                    price: parseFloat(ticker.c || '0'),
                    priceChangePercent: changePercent,
                    volume: parseFloat(ticker.q || '0')
                };
            }
        });

        return topLoser.symbol ? {
            ...topLoser,
            isLowest: true,
            lastTradeTime: lastTradeTimeMap[topLoser.symbol]
        } : null;
    }, [marketData, lastTradeTimeMap]);

    // Alarm verilen coinleri listele
    const alertedCoinsList = React.useMemo(() => {
        if (!marketData || !Array.isArray(marketData)) return [];

        return marketData
            .filter(ticker => {
                if (!ticker.s) return false;

                const symbol = ticker.s;
                const currentPrice = parseFloat(ticker.c || '0');
                const avgPrice = movingAverages[symbol]?.average;

                // Use configSettings.alertPercentage
                return avgPrice && currentPrice > avgPrice * (1 + configSettings.alertPercentage / 100);
            })
            .map(ticker => ({
                symbol: ticker.s || '',
                price: parseFloat(ticker.c || '0'),
                priceChangePercent: parseFloat(ticker.P || '0'),
                volume: parseFloat(ticker.q || '0'),
                isAlerted: true,
                lastTradeTime: lastTradeTimeMap[ticker.s || '']
            }));
    }, [marketData, movingAverages, configSettings.alertPercentage, lastTradeTimeMap]);

    // Tüm kriterleri sağlayan coinleri göster
    const criteriaMatchingCoins = React.useMemo(() => {
        const coins: CoinCardData[] = [];

        // Öncelikle criteria eligible coinleri göster
        const criteriaArray = Array.from(criteriaEligibleCoins);
        criteriaArray.slice(0, 6).forEach(symbol => { // En fazla 6 coin göster
            if (marketData && Array.isArray(marketData)) {
                const ticker = marketData.find(t => t.s === symbol);
                if (ticker) {
                    const lastCheck = lastCriteriaCheck[symbol];
                    coins.push({
                        symbol,
                        price: parseFloat(ticker.c || '0'),
                        priceChangePercent: parseFloat(ticker.P || '0'),
                        volume: parseFloat(ticker.q || '0'),
                        isAlerted: true, // Criteria sağlayan coinler için true
                        lastTradeTime: lastTradeTimeMap[symbol]
                    });
                }
            }
        });

        // Eğer criteria eligible coin yeterli değilse, varsayılan coinleri ekle
        if (coins.length < 2) {
            const defaultSymbols = ['BTCUSDT', 'ETHUSDT'];
            defaultSymbols.forEach(symbol => {
                if (!coins.find(c => c.symbol === symbol)) {
                    if (marketData && Array.isArray(marketData)) {
                        const ticker = marketData.find(t => t.s === symbol);
                        if (ticker) {
                            coins.push({
                                symbol,
                                price: parseFloat(ticker.c || '0'),
                                priceChangePercent: parseFloat(ticker.P || '0'),
                                volume: parseFloat(ticker.q || '0'),
                                isAlerted: alertedCoins.has(symbol),
                                lastTradeTime: lastTradeTimeMap[symbol]
                            });
                        } else {
                            coins.push({
                                symbol,
                                price: 0,
                                priceChangePercent: 0,
                                volume: 0,
                                isAlerted: false,
                                lastTradeTime: lastTradeTimeMap[symbol]
                            });
                        }
                    }
                }
            });
        }

        return coins;
    }, [marketData, criteriaEligibleCoins, lastCriteriaCheck, alertedCoins, lastTradeTimeMap]);

    return (
        <div className="mt-4">
            <Card className="animate-fade-in">
                <CardHeader className="pb-2 pt-3">
                    <CardTitle className="flex items-center justify-between text-md">
                        <span>Coin Özeti</span>
                        <Badge>{criteriaMatchingCoins.length} Coin</Badge>
                    </CardTitle>
                </CardHeader>
            </Card>

            <div className="grid grid-cols-4 gap-4 mt-4">
                {/* Kriterleri sağlayan coinleri göster */}
                {criteriaMatchingCoins.map((coin) => (
                    <CoinCard
                        key={coin.symbol}
                        symbol={coin.symbol}
                        price={coin.price}
                        priceChangePercent={coin.priceChangePercent}
                        volume={coin.volume}
                        isAlerted={coin.isAlerted}
                        lastTradeTime={coin.lastTradeTime}
                        className="w-full"
                    />
                ))}

                {/* En çok yükselen ve düşen coinleri göster */}
                {getTopCoin && (
                    <CoinCard
                        key={getTopCoin.symbol}
                        symbol={getTopCoin.symbol}
                        price={getTopCoin.price}
                        priceChangePercent={getTopCoin.priceChangePercent}
                        volume={getTopCoin.volume}
                        isTopGainer={true}
                        lastTradeTime={getTopCoin.lastTradeTime}
                        className="w-full"
                    />
                )}

                {getLowestCoin && (
                    <CoinCard
                        key={getLowestCoin.symbol}
                        symbol={getLowestCoin.symbol}
                        price={getLowestCoin.price}
                        priceChangePercent={getLowestCoin.priceChangePercent}
                        volume={getLowestCoin.volume}
                        isLowest={true}
                        lastTradeTime={getLowestCoin.lastTradeTime}
                        className="w-full"
                    />
                )}
            </div>
        </div>
    );
};

export default CoinSummary; 
