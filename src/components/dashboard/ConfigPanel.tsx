import React from 'react';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Settings, Shield, Sliders } from 'lucide-react';
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import useTradingRules from '@/hooks/useTradingRules';

export interface ConfigSettings {
    movingAveragePoints: number;
    alertPercentage: number;
    maxOpenPositions: number;
    minVolumeForTrade: number;
    securityEnabled: boolean;
    emergencyStopLossEnabled: boolean;
    emergencyStopLossPercent: number;
    tradingTimeoutMinutes: number;
}

interface ConfigPanelProps {
    settings: ConfigSettings;
    onSettingsChange: (settings: ConfigSettings) => void;
}

export function ConfigPanel({ settings, onSettingsChange }: ConfigPanelProps) {
    const { rules } = useTradingRules();

    // Yapılandırma ayarını güncelleme fonksiyonu
    const updateSetting = (key: keyof ConfigSettings, value: any) => {
        onSettingsChange({
            ...settings,
            [key]: value
        });
    };

    const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>, key: keyof ConfigSettings) => {
        let value = parseFloat(e.target.value);
        if (!isNaN(value)) {
            updateSetting(key, value);
        }
    };

    // Kurallardan varsayılan değerleri yükle
    React.useEffect(() => {
        if (rules) {
            onSettingsChange({
                ...settings,
                movingAveragePoints: rules.rules.signal.movingAveragePeriod,
                alertPercentage: rules.rules.signal.alertThresholdPercent,
                maxOpenPositions: rules.rules.global.maxOpenPositions,
                minVolumeForTrade: rules.rules.filters.minVolumeMillions,
                emergencyStopLossPercent: rules.rules.global.emergencyStopLossPercent,
                tradingTimeoutMinutes: rules.rules.global.tradingTimeoutMinutes
            });
        }
    }, [rules]);

    return (
        <Card className="border-blue-400/20">
            <CardHeader className="pb-3">
                <CardTitle className="text-lg font-medium flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Settings className="h-5 w-5 text-blue-500" />
                        İşlem Ayarları
                    </div>
                    <Badge variant="outline">Yapılandırma</Badge>
                </CardTitle>
            </CardHeader>
            <CardContent className="pb-4">
                <Tabs defaultValue="strategy">
                    <TabsList className="w-full mb-3">
                        <TabsTrigger value="strategy" className="flex-1">
                            <Sliders className="mr-2 h-4 w-4" />
                            Strateji
                        </TabsTrigger>
                        <TabsTrigger value="security" className="flex-1">
                            <Shield className="mr-2 h-4 w-4" />
                            Güvenlik
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="strategy">
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="movingAveragePoints" className="text-sm">
                                    Hareketli Ortalama (MA) Periyodu
                                </Label>
                                <div className="flex items-center mt-1">
                                    <Input
                                        id="movingAveragePoints"
                                        type="number"
                                        min="5"
                                        max="200"
                                        value={settings.movingAveragePoints}
                                        onChange={(e) => handleNumberChange(e, 'movingAveragePoints')}
                                        className="h-8"
                                    />
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    Ortalama periyodu (5-200 arası)
                                </p>
                            </div>

                            <div>
                                <Label htmlFor="alertPercentage" className="text-sm">
                                    Sinyal Eşik Değeri (%)
                                </Label>
                                <div className="flex items-center mt-1">
                                    <Input
                                        id="alertPercentage"
                                        type="number"
                                        min="0.1"
                                        max="5"
                                        step="0.1"
                                        value={settings.alertPercentage}
                                        onChange={(e) => handleNumberChange(e, 'alertPercentage')}
                                        className="h-8"
                                    />
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    MA'yı geçme yüzdesi (0.1-5 arası)
                                </p>
                            </div>

                            <Separator />

                            <div>
                                <Label htmlFor="maxOpenPositions" className="text-sm">
                                    Maksimum Açık Pozisyon
                                </Label>
                                <div className="flex items-center mt-1">
                                    <Input
                                        id="maxOpenPositions"
                                        type="number"
                                        min="1"
                                        max="10"
                                        value={settings.maxOpenPositions}
                                        onChange={(e) => handleNumberChange(e, 'maxOpenPositions')}
                                        className="h-8"
                                    />
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    Aynı anda açılabilecek maksimum işlem sayısı
                                </p>
                            </div>

                            <div>
                                <Label htmlFor="minVolumeForTrade" className="text-sm">
                                    Minimum İşlem Hacmi (Milyon $)
                                </Label>
                                <div className="flex items-center mt-1">
                                    <Input
                                        id="minVolumeForTrade"
                                        type="number"
                                        min="1"
                                        max="100"
                                        value={settings.minVolumeForTrade}
                                        onChange={(e) => handleNumberChange(e, 'minVolumeForTrade')}
                                        className="h-8"
                                    />
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    İşlem için gereken minimum 24s hacmi (milyon $)
                                </p>
                            </div>
                        </div>
                    </TabsContent>

                    <TabsContent value="security">
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <Label htmlFor="emergencyStopLoss" className="text-sm">
                                    Acil Durum Stop Loss
                                </Label>
                                <div className="flex items-center">
                                    <input
                                        id="emergencyStopLoss"
                                        type="checkbox"
                                        checked={settings.emergencyStopLossEnabled}
                                        onChange={(e) => updateSetting('emergencyStopLossEnabled', e.target.checked)}
                                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                </div>
                            </div>
                            <div>
                                <Label htmlFor="emergencyStopLossPercent" className="text-sm">
                                    Acil Stop Loss Yüzdesi (%)
                                </Label>
                                <div className="flex items-center mt-1">
                                    <Input
                                        id="emergencyStopLossPercent"
                                        type="number"
                                        min="1"
                                        max="50"
                                        step="0.5"
                                        value={settings.emergencyStopLossPercent}
                                        onChange={(e) => handleNumberChange(e, 'emergencyStopLossPercent')}
                                        className="h-8"
                                        disabled={!settings.emergencyStopLossEnabled}
                                    />
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    Toplam zarar bu yüzdeye ulaşırsa tüm işlemler kapatılır
                                </p>
                            </div>

                            <Separator />

                            <div>
                                <Label htmlFor="tradingTimeoutMinutes" className="text-sm">
                                    İşlem Zaman Aşımı (Dakika)
                                </Label>
                                <div className="flex items-center mt-1">
                                    <Input
                                        id="tradingTimeoutMinutes"
                                        type="number"
                                        min="10"
                                        max="1440"
                                        value={settings.tradingTimeoutMinutes}
                                        onChange={(e) => handleNumberChange(e, 'tradingTimeoutMinutes')}
                                        className="h-8"
                                    />
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    Açık işlemler için maksimum süre (dakika)
                                </p>
                            </div>
                        </div>
                    </TabsContent>
                </Tabs>
            </CardContent>
        </Card>
    );
} 