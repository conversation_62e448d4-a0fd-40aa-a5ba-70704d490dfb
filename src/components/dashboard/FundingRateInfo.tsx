
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { FundingRateInfo as FundingRateInfoType } from "@/types/trading";
import { Clock, TrendingDown, TrendingUp } from "lucide-react";

interface FundingRateDisplayProps {
  fundingRates: Record<string, FundingRateInfoType>;
  minutesUntilNextFunding: number;
}

const formatTimeRemaining = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);
  
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};

const FundingRateInfo: React.FC<FundingRateDisplayProps> = ({ 
  fundingRates, 
  minutesUntilNextFunding 
}) => {
  const topSymbols = ['BTCUSDT', 'ETHUSDT'];
  
  return (
    <div className="flex items-center space-x-2">
      <TooltipProvider>
        <div className="flex items-center gap-1">
          <Clock className="h-4 w-4" />
          <span className="text-sm">Funding:</span>
          <span className="text-sm font-bold">{formatTimeRemaining(minutesUntilNextFunding)}</span>
        </div>
        
        {topSymbols.map(symbol => {
          const rate = fundingRates[symbol];
          if (!rate) return null;
          
          // Ensure fundingRate is a number
          const fundingRateNum = typeof rate.fundingRate === 'string' 
            ? parseFloat(rate.fundingRate) 
            : rate.fundingRate;
          
          const isNegative = fundingRateNum < 0;
          const formattedRate = `${isNegative ? '' : '+'}${fundingRateNum.toFixed(4)}%`;
          
          return (
            <Tooltip key={symbol}>
              <TooltipTrigger asChild>
                <Badge 
                  variant={isNegative ? "destructive" : "default"}
                  className="flex items-center gap-1 cursor-default"
                >
                  {symbol.replace('USDT', '')}
                  {isNegative ? 
                    <TrendingDown className="h-3 w-3" /> : 
                    <TrendingUp className="h-3 w-3" />
                  }
                  {formattedRate}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <div>
                  <p className="font-semibold">{symbol} Funding Rate</p>
                  <p className="text-sm">Rate: {formattedRate}</p>
                  <p className="text-xs">
                    {isNegative ? 
                      'Shorts pay longs' : 
                      'Longs pay shorts'}
                  </p>
                </div>
              </TooltipContent>
            </Tooltip>
          );
        })}
      </TooltipProvider>
    </div>
  );
};

export default FundingRateInfo;
