import React from 'react';
import { Badge } from "@/components/ui/badge";

interface MarketDataProps {
    marketData: any[];
    autoTradingEnabled: boolean;
    tradingDirection: 'long' | 'short';
    accountBalance: number;
    movingAverages: Record<string, { average: number; history: { price: number; timestamp: number }[] }>;
}

const MarketData: React.FC<MarketDataProps> = ({
    marketData,
    autoTradingEnabled,
    tradingDirection,
    accountBalance,
    movingAverages
}) => {
    const calculateSignalStatus = () => {
        if (!marketData || !Array.isArray(marketData) || !autoTradingEnabled) {
            return { totalSymbols: 0, signalsDetected: 0, strongSignals: 0 };
        }

        let signalsDetected = 0;
        let strongSignals = 0;

        marketData.forEach(ticker => {
            const symbol = ticker.s;
            const maData = movingAverages[symbol];

            if (maData && maData.history && maData.history.length >= 3) {
                const recentPrices = maData.history.slice(-3).map(h => h.price);
                const [price1, price2, price3] = recentPrices;
                const avgPrice = maData.average;

                let hasSignal = false;
                let signalStrength = 0;

                if (price1 <= avgPrice && price2 > avgPrice && price3 > avgPrice) {
                    const momentum = (price3 - price1) / price1;
                    if (momentum > 0.001) {
                        hasSignal = true;
                        signalStrength = momentum * 100;
                    }
                }

                if (price1 >= avgPrice && price2 < avgPrice && price3 < avgPrice) {
                    const momentum = Math.abs((price3 - price1) / price1);
                    if (momentum > 0.001) {
                        hasSignal = true;
                        signalStrength = momentum * 100;
                    }
                }

                if (hasSignal) {
                    signalsDetected++;
                    if (signalStrength >= 0.5) {
                        strongSignals++;
                    }
                }
            }
        });

        return {
            totalSymbols: marketData.length,
            signalsDetected,
            strongSignals
        };
    };

    const signalStatus = calculateSignalStatus();

    return (
        <div className="space-y-4">
            {autoTradingEnabled && (
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                        <h3 className="text-sm font-semibold text-blue-800 flex items-center gap-2">
                            🎯 Teknik Sinyal Durumu
                            <Badge variant={signalStatus.strongSignals > 0 ? "default" : "secondary"}>
                                {tradingDirection.toUpperCase()} Modunda
                            </Badge>
                        </h3>
                        <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${autoTradingEnabled ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
                            <span className="text-xs text-blue-600">
                                {autoTradingEnabled ? 'Aktif Tarama' : 'Pasif'}
                            </span>
                        </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 text-center">
                        <div className="bg-white rounded-lg p-3 border border-blue-100">
                            <div className="text-lg font-bold text-blue-600">{signalStatus.totalSymbols}</div>
                            <div className="text-xs text-blue-500">Toplam Sembol</div>
                        </div>
                        <div className="bg-white rounded-lg p-3 border border-green-100">
                            <div className="text-lg font-bold text-green-600">{signalStatus.signalsDetected}</div>
                            <div className="text-xs text-green-500">Tespit Edilen Sinyal</div>
                        </div>
                        <div className="bg-white rounded-lg p-3 border border-purple-100">
                            <div className="text-lg font-bold text-purple-600">{signalStatus.strongSignals}</div>
                            <div className="text-xs text-purple-500">Güçlü Sinyal</div>
                        </div>
                    </div>

                    <div className="mt-3 text-xs text-blue-600 text-center">
                        {signalStatus.strongSignals > 0
                            ? `${signalStatus.strongSignals} güçlü ${tradingDirection} sinyali mevcut!`
                            : `${tradingDirection.toUpperCase()} sinyalleri bekleniyor...`}
                    </div>
                </div>
            )}
        </div>
    );
};

export default MarketData; 
