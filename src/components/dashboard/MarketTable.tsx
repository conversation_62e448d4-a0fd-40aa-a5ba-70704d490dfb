
import React, { useState, useEffect } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ArrowUp, ArrowDown } from 'lucide-react';
import { Badge } from "@/components/ui/badge";

export type CoinData = {
  s?: string; // symbol
  c?: string; // current price
  P?: string; // price change percent
  q?: string; // volume
  h?: string; // high
  l?: string; // low
  symbol?: string;
  price?: number;
  priceChangePercent?: number;
  volume?: number;
  high24h?: number;
  low24h?: number;
  isAlerted?: boolean;
};

interface MovingAverages {
  [symbol: string]: {
    average: number;
    history: any[];
  };
}

type MarketTableProps = {
  data: CoinData[];
  movingAverages?: MovingAverages;
  alertThreshold?: number;
  alertedCoins?: Set<string>;
  onRowClick?: (symbol: string) => void;
};

const MarketTable: React.FC<MarketTableProps> = ({ 
  data, 
  movingAverages = {}, 
  alertThreshold = 0, 
  alertedCoins = new Set(),
  onRowClick 
}) => {
  const [sortField, setSortField] = useState<string>('symbol');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [sortedData, setSortedData] = useState<CoinData[]>([]);
  const [priceChanges, setPriceChanges] = useState<Record<string, 'up' | 'down' | null>>({});

  // Format and transform data
  const processedData = React.useMemo(() => {
    if (!data || !Array.isArray(data)) return [];
    
    return data.map(item => {
      if (!item) return null;
      
      const symbol = item.s || item.symbol || '';
      const price = typeof item.c === 'string' ? parseFloat(item.c) : (item.price || 0);
      const priceChangePercent = typeof item.P === 'string' ? parseFloat(item.P) : (item.priceChangePercent || 0);
      const volume = typeof item.q === 'string' ? parseFloat(item.q) : (item.volume || 0);
      const high24h = typeof item.h === 'string' ? parseFloat(item.h) : (item.high24h || 0);
      const low24h = typeof item.l === 'string' ? parseFloat(item.l) : (item.low24h || 0);
      
      return {
        symbol,
        price,
        priceChangePercent,
        volume,
        high24h,
        low24h,
        isAlerted: alertedCoins.has(symbol)
      };
    }).filter(Boolean);
  }, [data, alertedCoins]);

  // Handle sorting
  const handleSort = (field: string) => {
    const newDirection = field === sortField && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortField(field);
    setSortDirection(newDirection);
  };

  // Format price with appropriate precision based on price value
  const formatPrice = (price: number): string => {
    if (price < 0.0001) return price.toFixed(8);
    if (price < 0.01) return price.toFixed(6);
    if (price < 1) return price.toFixed(5);
    if (price < 10) return price.toFixed(4);
    if (price < 1000) return price.toFixed(2);
    return price.toLocaleString();
  };

  // Track price changes to animate
  useEffect(() => {
    const newPriceChanges: Record<string, 'up' | 'down' | null> = {};
    
    processedData.forEach(coin => {
      if (!coin || !coin.symbol) return;
      
      const previousCoin = sortedData.find(c => c && c.symbol === coin.symbol);
      if (previousCoin && previousCoin.price !== coin.price) {
        newPriceChanges[coin.symbol] = coin.price > (previousCoin.price || 0) ? 'up' : 'down';
      }
    });
    
    setPriceChanges(newPriceChanges);
    
    // Clear price change indicators after animation completes
    const timer = setTimeout(() => {
      setPriceChanges({});
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [processedData, sortedData]);

  // Sort data when sort parameters or data changes
  useEffect(() => {
    if (!processedData || processedData.length === 0) {
      setSortedData([]);
      return;
    }
    
    const sorted = [...processedData].sort((a, b) => {
      if (!a || !b) return 0;
      
      const aValue = a[sortField as keyof CoinData];
      const bValue = b[sortField as keyof CoinData];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue) 
          : bValue.localeCompare(aValue);
      }
      
      if (aValue === undefined || bValue === undefined) return 0;
      
      return sortDirection === 'asc' 
        ? Number(aValue) - Number(bValue) 
        : Number(bValue) - Number(aValue);
    });
    
    setSortedData(sorted);
  }, [processedData, sortField, sortDirection]);

  return (
    <div className="w-full overflow-auto rounded-lg border border-border animate-scale-in">
      <Table>
        <TableHeader className="bg-muted/30">
          <TableRow>
            <TableHead 
              className="w-[120px] cursor-pointer hover:bg-muted/50" 
              onClick={() => handleSort('symbol')}
            >
              Symbol {sortField === 'symbol' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
            <TableHead 
              className="text-right cursor-pointer hover:bg-muted/50" 
              onClick={() => handleSort('price')}
            >
              Price {sortField === 'price' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
            <TableHead 
              className="text-right cursor-pointer hover:bg-muted/50" 
              onClick={() => handleSort('priceChangePercent')}
            >
              24h Change {sortField === 'priceChangePercent' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
            <TableHead 
              className="text-right cursor-pointer hover:bg-muted/50 hidden md:table-cell" 
              onClick={() => handleSort('volume')}
            >
              24h Volume {sortField === 'volume' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
            <TableHead 
              className="text-right hidden lg:table-cell cursor-pointer hover:bg-muted/50" 
              onClick={() => handleSort('high24h')}
            >
              24h High {sortField === 'high24h' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
            <TableHead 
              className="text-right hidden lg:table-cell cursor-pointer hover:bg-muted/50" 
              onClick={() => handleSort('low24h')}
            >
              24h Low {sortField === 'low24h' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
            <TableHead 
              className="text-right"
            >
              Status
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedData.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                Loading market data...
              </TableCell>
            </TableRow>
          ) : (
            sortedData.map((coin) => {
              if (!coin || !coin.symbol) return null;
              return (
                <TableRow 
                  key={coin.symbol} 
                  className={`hoverable-row ${coin.isAlerted ? 'bg-primary/5' : ''}`}
                  onClick={() => onRowClick && coin.symbol && onRowClick(coin.symbol)}
                >
                  <TableCell className="font-medium">{coin.symbol}</TableCell>
                  <TableCell className={`text-right ${
                    priceChanges[coin.symbol] === 'up' ? 'price-up' : 
                    priceChanges[coin.symbol] === 'down' ? 'price-down' : ''
                  }`}>
                    ${coin.price ? formatPrice(coin.price) : '0.000000'}
                  </TableCell>
                  <TableCell className={`text-right ${(coin.priceChangePercent || 0) >= 0 ? 'text-success' : 'text-danger'}`}>
                    <div className="flex items-center justify-end space-x-1">
                      {(coin.priceChangePercent || 0) >= 0 ? (
                        <ArrowUp className="h-3 w-3" />
                      ) : (
                        <ArrowDown className="h-3 w-3" />
                      )}
                      <span>{Math.abs(coin.priceChangePercent || 0).toFixed(2)}%</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right hidden md:table-cell">
                    ${(coin.volume || 0).toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right hidden lg:table-cell">
                    ${coin.high24h ? formatPrice(coin.high24h) : '0.000000'}
                  </TableCell>
                  <TableCell className="text-right hidden lg:table-cell">
                    ${coin.low24h ? formatPrice(coin.low24h) : '0.000000'}
                  </TableCell>
                  <TableCell className="text-right">
                    {coin.isAlerted && (
                      <Badge variant="default" className="bg-primary animate-pulse ml-auto">
                        Alert
                      </Badge>
                    )}
                  </TableCell>
                </TableRow>
              );
            }).filter(Boolean)
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default MarketTable;
