import React from 'react';
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Volume2, VolumeX } from "lucide-react";
import { format } from 'date-fns';
import AccountConnection from "@/components/trading/AccountConnection";

interface DashboardHeaderProps {
  title: string;
  soundEnabled: boolean;
  onToggleSound: () => void;
  currentTime: Date;
  connectionStatus: string;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  title,
  soundEnabled,
  onToggleSound,
  currentTime,
  connectionStatus
}) => {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
      <div className="flex items-center gap-4">
        <h1 className="text-3xl font-bold tracking-tighter">
          <span className="text-blue-600">P</span>
          <span className="text-green-500">a</span>
          <span className="text-purple-500">r</span>
          <span className="text-orange-500">a</span>
          <span className="text-gray-600">B</span>
          <span className="text-red-500">O</span>
          <span className="text-indigo-500">T</span>
        </h1>
      </div>

      <div className="flex flex-wrap items-center mt-4 md:mt-0 space-x-4">
        <div className="text-sm font-medium">
          {format(currentTime, 'HH:mm:ss')}
        </div>

        <AccountConnection />

        <Badge variant={connectionStatus === 'open' ? "default" : "destructive"} className="ml-2">
          {connectionStatus === 'open' ? "Bağlı" : "Bağlanıyor..."}
        </Badge>
      </div>
    </div>
  );
};

export default DashboardHeader;
