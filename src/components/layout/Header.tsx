import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>, Cable } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import AccountConnection from "@/components/trading/AccountConnection";

const Header: React.FC = () => {
  return (
    <header className="w-full h-16 border-b border-border backdrop-blur-md bg-background/80 fixed top-0 left-0 right-0 z-50 animate-fade-in">
      <div className="container h-full mx-auto px-4 flex items-center justify-between">
        <Link to="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
            <span className="text-white font-bold">B</span>
          </div>
          <span className="font-medium text-lg tracking-tight">BinanceTrader</span>
        </Link>

        <nav className="hidden md:flex items-center space-x-1">
          <Link to="/dashboard" className="px-4 py-2 rounded-md hover:bg-secondary transition-colors">
            Dashboard
          </Link>
          <Link to="/trading-test" className="px-4 py-2 rounded-md hover:bg-secondary transition-colors">
            Trade Test
          </Link>
          <Link to="/connection-test" className="px-4 py-2 rounded-md hover:bg-secondary transition-colors flex items-center">
            <Cable className="h-4 w-4 mr-1" />
            Bağlantı Testi
          </Link>
          <Link to="/settings" className="px-4 py-2 rounded-md hover:bg-secondary transition-colors">
            Settings
          </Link>
        </nav>

        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                  <Bell className="h-5 w-5" />
                  <span className="absolute top-1 right-1 w-2 h-2 bg-primary rounded-full"></span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Bildirimler</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" asChild>
                  <Link to="/settings">
                    <Settings className="h-5 w-5" />
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Ayarlar</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <AccountConnection />
        </div>
      </div>
    </header>
  );
};

export default Header;
