import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Settings } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";

export interface ConfigSettings {
  movingAveragePoints: number;
  alertPercentage: number;
  aiAnalysisInterval: number;
  maxOpenPositions: number;
  // DÜZENLEME: Günlük işlem limiti tamamen kaldırıldı
  // maxDailyTrades: number;
  // DÜZENLEME: Güvenlik ayarları iyileştirildi
  emergencyStopLoss: number;
  minVolumeForTrade: number;
  // slippageProtection: boolean; // Kaldırıldı
  // maxSlippagePercent: number; // Kaldırıldı
  // restrictLowVolumeCoins: boolean; // Kaldırıldı
}

interface ConfigPanelProps {
  settings: ConfigSettings;
  onSettingsChange: (settings: ConfigSettings) => void;
}

const ConfigPanel: React.FC<ConfigPanelProps> = ({ settings, onSettingsChange }) => {
  const { toast } = useToast();
  const [lastChangedSetting, setLastChangedSetting] = useState<string>("");

  const handleChange = (key: keyof ConfigSettings, value: any) => {
    // Değişikliği kaydet
    const newSettings = { ...settings, [key]: value };
    onSettingsChange(newSettings);

    // Son değiştirilen ayarı kaydet
    setLastChangedSetting(key);

    // Kullanıcıya bildirim göster
    toast({
      title: "Ayar Güncellendi",
      description: `${getSettingDisplayName(key)}: ${value}`,
      variant: "default",
    });
  };

  // Ayar anahtarlarını kullanıcı dostu isimlere çevir
  const getSettingDisplayName = (key: string): string => {
    const displayNames: Record<string, string> = {
      movingAveragePoints: "Hareketli Ortalama Noktaları",
      alertPercentage: "Alarm Eşik Yüzdesi",
      aiAnalysisInterval: "AI Analiz Sıklığı",
      maxOpenPositions: "Maksimum Açık Pozisyon",
      emergencyStopLoss: "Acil Stop Loss",
      minVolumeForTrade: "Minimum İşlem Hacmi",
    };

    return displayNames[key] || key;
  };

  return (
    <Card className="animate-scale-in">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          İşlem Ayarları
        </CardTitle>
        <CardDescription>Analiz ve işlem parametrelerini düzenleyin</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="movingAveragePoints">
            Hareketli Ortalama Veri Noktaları: {settings.movingAveragePoints}
          </Label>
          <Slider
            id="movingAveragePoints"
            min={5}
            max={50}
            step={1}
            value={[settings.movingAveragePoints]}
            onValueChange={(value) => handleChange('movingAveragePoints', value[0])}
          />
          <p className="text-xs text-muted-foreground">
            Hareketli ortalama için kullanılan veri noktalarının sayısı
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="alertPercentage">
            Alarm Eşik Yüzdesi: {settings.alertPercentage}%
          </Label>
          <Slider
            id="alertPercentage"
            min={0.1}
            max={5}
            step={0.1}
            value={[settings.alertPercentage]}
            onValueChange={(value) => handleChange('alertPercentage', value[0])}
          />
          <p className="text-xs text-muted-foreground">
            Hareketli ortalamanın üzerindeki yüzde alarm tetikleyici (varsayılan: 0.3%)
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="aiAnalysisInterval">
            AI Analiz Kontrolü (dakika): {settings.aiAnalysisInterval}
          </Label>
          <Slider
            id="aiAnalysisInterval"
            min={5}
            max={60}
            step={5}
            value={[settings.aiAnalysisInterval]}
            onValueChange={(value) => handleChange('aiAnalysisInterval', value[0])}
          />
          <p className="text-xs text-muted-foreground">
            AI optimizer'ın kaç dakikada bir analiz yapacağı
          </p>
        </div>

        <Separator />

        {/* DÜZENLEME: Güvenlik Önlemleri - Günlük işlem limiti kaldırıldı */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-red-600">🛡️ Güvenlik Önlemleri</h3>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="emergencyStopLoss">
                Acil Durum Stop Loss: {settings.emergencyStopLoss}%
              </Label>
              <p className="text-xs text-muted-foreground">
                Normal stop loss'un üzerinde otomatik kapatma limiti
              </p>
            </div>
            <Slider
              id="emergencyStopLoss"
              min={10}
              max={25}
              step={1}
              className="w-[120px]"
              value={[settings.emergencyStopLoss]}
              onValueChange={(value) => handleChange('emergencyStopLoss', value[0])}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="minVolumeForTrade">
                Min. İşlem Hacmi: {settings.minVolumeForTrade}M USDT
              </Label>
              <p className="text-xs text-muted-foreground">
                Bu hacmin altındaki coinlerde işlem yapılmaz
              </p>
            </div>
            <Slider
              id="minVolumeForTrade"
              min={1}
              max={50}
              step={1}
              className="w-[120px]"
              value={[settings.minVolumeForTrade]}
              onValueChange={(value) => handleChange('minVolumeForTrade', value[0])}
            />
          </div>

        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="text-sm font-medium">İşlem Kriterleri</h3>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="maxOpenPositions">
                Maksimum açık pozisyon: {settings.maxOpenPositions}
              </Label>
              <p className="text-xs text-muted-foreground">
                Bu sayıya ulaşıldığında yeni işlem açılmaz
              </p>
            </div>
            <Slider
              id="maxOpenPositions"
              min={1}
              max={10}
              step={1}
              className="w-[120px]"
              value={[settings.maxOpenPositions]}
              onValueChange={(value) => handleChange('maxOpenPositions', value[0])}
            />
          </div>

        </div>
      </CardContent>
    </Card>
  );
};

export default ConfigPanel;
