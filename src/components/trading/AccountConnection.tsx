import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Link, User, AlertCircle, RotateCw, HelpCircle, ExternalLink, AlertTriangle, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useApiConnection } from "@/contexts/ApiConnectionContext";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import useBinanceAccount from '@/hooks/useBinanceAccount';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { getBinanceConfig, saveCredentialsToStorage, hasValidCredentials } from "@/config/binanceConfig";
import binanceCredentials from '../../config/binance-credentials.json';

const AccountConnection = () => {
  const { toast } = useToast();
  const {
    isConnected,
    connectionStatus,
    connect,
    disconnect,
    userName,
    lastError
  } = useApiConnection();

  const { fetchAccountInfo } = useBinanceAccount();

  // Load API keys from config automatically
  const binanceConfig = getBinanceConfig();

  // Dosyadan API key'i al
  const defaultApiKey = binanceCredentials.apiKey || '';

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showError, setShowError] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [autoConnected, setAutoConnected] = useState(false);

  // Track saved credentials for reconnection
  const savedCredentialsRef = useRef<{ apiKey: string; apiSecret: string; displayName: string; saved: boolean } | null>(null);

  // Otomatik bağlantı useEffect'ini değiştir ve bağlantı durumu kontrollerini düzelt
  useEffect(() => {
    // Otomatik bağlantı girişimini takip etmek için bir session değişkeni kullan
    const autoConnectAttempted = sessionStorage.getItem('autoConnectAttempted');
    const shouldAutoConnect = !autoConnectAttempted && hasValidCredentials() && !isConnected && !autoConnected && binanceConfig.autoConnect;

    if (shouldAutoConnect) {
      console.log('🚀 AUTO-CONNECTING with config credentials (ONE TIME ONLY)...');
      sessionStorage.setItem('autoConnectAttempted', 'true');
      setAutoConnected(true);

      // Save credentials to localStorage first
      saveCredentialsToStorage();

      // Auto-connect after a short delay
      setTimeout(() => {
        handleAutoConnect();
      }, 1000);
    }
  }, [isConnected, autoConnected, binanceConfig.autoConnect]); // Dependency dizisi kısa tutuldu

  // Connection status değişimlerini izleyen useEffect'i düzelt
  // Sürekli çalışan bir etki olmamasını sağla
  useEffect(() => {
    if (connectionStatus === 'authenticated' && !savedCredentialsRef.current?.saved) {
      console.log('�� CONNECTION SUCCESS - Saving credentials (ONE TIME)');
      // Save credentials to localStorage for account info fetching
      if (savedCredentialsRef.current) {
        const { apiKey, apiSecret } = savedCredentialsRef.current;
        localStorage.setItem('binance_credentials', JSON.stringify({ apiKey, apiSecret }));
        console.log('💾 CREDENTIALS SAVED TO LOCALSTORAGE');

        // İkinci kez aynı işlemin yapılmaması için kaydedildiğini işaretle
        savedCredentialsRef.current.saved = true;

        // Fetch account information after successful connection
        setTimeout(() => {
          fetchAccountInfo();
        }, 3000); // Increased delay to prevent rate limiting

        // Bağlantı başarılı mesajını yalnızca bir kez göster
        toast({
          title: "Bağlantı Başarılı!",
          description: "Binance Futures API'ye bağlantı kuruldu.",
        });
      }
    }

    if (connectionStatus === 'error' && lastError && !showError) {
      setShowError(true);
      // Auto-hide error after 8 seconds
      const timer = setTimeout(() => {
        setShowError(false);
      }, 8000);
      return () => clearTimeout(timer);
    }
  }, [connectionStatus, lastError, fetchAccountInfo, showError]);

  // Auto-connect function - Simplified and improved
  const handleAutoConnect = async () => {
    console.log('🔄 AUTO-CONNECT: Starting automatic connection...');

    setIsSubmitting(true);

    try {
      // Use credentials from config
      const credentials = {
        apiKey: binanceConfig.apiKey,
        apiSecret: binanceConfig.apiSecret
      };

      // Store credentials for reconnection
      savedCredentialsRef.current = {
        apiKey: credentials.apiKey,
        apiSecret: credentials.apiSecret,
        displayName: 'Trader (Auto)',
        saved: false // İlk kez kaydediliyor
      };

      // Establish WebSocket connection directly
      console.log('🔌 Establishing WebSocket connection...');
      connect(credentials, 'Trader (Auto)');

      // Don't show toast here, will show on successful connection
    } catch (error) {
      console.error('❌ AUTO-CONNECT: Exception:', error);
      toast({
        title: "Otomatik Bağlantı Hatası",
        description: `Bağlantı kurulamadı: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Basitleştirilmiş bağlantı fonksiyonu - sadece JSON dosyasından bilgileri al
  const handleConnect = async () => {
    if (!defaultApiKey) {
      toast({
        title: "API Anahtarı Bulunamadı",
        description: "binance-credentials.json dosyasında API anahtarı bulunamadı.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    setShowError(false);

    console.log('🚀 WebSocket bağlantısı başlatılıyor...');

    try {
      // JSON dosyasından API bilgilerini al
      const credentials = {
        apiKey: binanceCredentials.apiKey,
        apiSecret: binanceCredentials.apiSecret
      };

      // Store credentials for future reconnects
      savedCredentialsRef.current = {
        apiKey: credentials.apiKey,
        apiSecret: credentials.apiSecret,
        displayName: 'Trader (JSON)',
        saved: false
      };

      console.log('🔑 WebSocket bağlantısı kuruluyor...');
      await connect(credentials, 'Trader (JSON)');

      // Fetch initial account data
      fetchAccountInfo().catch(console.error);
    } catch (error) {
      console.error('❌ Bağlantı hatası:', error);

      toast({
        title: "Bağlantı Hatası",
        description: error?.message || "WebSocket bağlantısı kurulamadı.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDisconnect = () => {
    disconnect();
    setShowError(false);
    savedCredentialsRef.current = null;

    // Clear saved credentials from localStorage
    localStorage.removeItem('binance_credentials');
    console.log('🗑️ WebSocket bağlantısı kapatıldı');
  };

  const handleReconnect = () => {
    if (!savedCredentialsRef.current) {
      toast({
        title: "Bağlantı Hatası",
        description: "Yeniden bağlanmak için önce bağlantı kurmuş olmalısınız.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    // Check internet connection before attempting to reconnect
    if (!navigator.onLine) {
      toast({
        title: "Bağlantı Hatası",
        description: "İnternet bağlantısı yok. Lütfen bağlantınızı kontrol edin.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    const { apiKey, apiSecret, displayName } = savedCredentialsRef.current;

    // Attempt to reconnect
    connect({ apiKey, apiSecret: '' }, displayName);

    setTimeout(() => {
      setIsSubmitting(false);
    }, 3000);
  };

  const getStatusIndicator = () => {
    switch (connectionStatus) {
      case 'authenticated':
      case 'subscribed':
        return <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-1"></span>;
      case 'connecting':
        return <span className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse mr-1"></span>;
      case 'error':
        return <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-1"></span>;
      default:
        return <span className="w-2 h-2 bg-slate-400 rounded-full mr-1"></span>;
    }
  };

  // Network status indicator
  const getNetworkStatus = () => {
    if (!isOnline) {
      return (
        <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">
          <AlertCircle size={14} className="mr-1" />
          Çevrimdışı
        </Badge>
      );
    }
    return null;
  };

  const renderHelpDialog = () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="ml-2" data-testid="help-button">
          <HelpCircle className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Binance API Bağlantı Kılavuzu
          </DialogTitle>
          <DialogDescription>
            Gerçek hesabınıza bağlanmak için API anahtarlarınızı nasıl oluşturacağınızı öğrenin
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* IP Restriction Solution - Prominent Section */}
          <div className="bg-gradient-to-r from-red-50 to-orange-50 border-2 border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <div className="bg-red-100 rounded-full p-2">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-red-800 mb-2">
                  ⚠️ IP Kısıtlaması Sorunu Çözümü
                </h3>
                <div className="bg-white rounded-lg p-3 mb-3">
                  <p className="text-sm font-medium text-gray-900 mb-1">Sunucu IP Adresi:</p>
                  <code className="text-lg font-mono bg-gray-100 px-2 py-1 rounded text-red-600">
                    ************
                  </code>
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-red-700">
                    <strong>Bu IP adresini Binance API ayarlarınıza eklemeniz gerekiyor:</strong>
                  </p>
                  <ol className="text-sm text-red-700 space-y-1 list-decimal list-inside ml-2">
                    <li>Binance hesabınıza giriş yapın</li>
                    <li>API Management → API Keys sayfasına gidin</li>
                    <li>Kullandığınız API anahtarınızı bulun ve "Edit" tıklayın</li>
                    <li>"Restrict access to trusted IPs only" bölümünde:</li>
                    <li className="ml-4">• <code className="bg-gray-100 px-1 rounded">************</code> adresini ekleyin</li>
                    <li>Değişiklikleri kaydedin</li>
                    <li><strong>5-10 dakika bekleyin</strong> (IP değişikliği için gerekli)</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>

          {/* Step 1 */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">1</span>
              API Anahtarı Oluşturma
            </h3>
            <div className="pl-8 space-y-2">
              <p className="text-sm text-muted-foreground">
                1. <a href="https://www.binance.com/en/my/settings/api-management" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline inline-flex items-center gap-1">
                  Binance API Yönetim Sayfası <ExternalLink className="h-3 w-3" />
                </a> adresine gidin
              </p>
              <p className="text-sm text-muted-foreground">2. "Create API" butonuna tıklayın</p>
              <p className="text-sm text-muted-foreground">3. Bir API anahtarı adı girin (örn: "Trading Bot")</p>
              <p className="text-sm text-muted-foreground">4. API anahtarını oluşturun</p>
            </div>
          </div>

          {/* Step 2 */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">2</span>
              Futures Trading İzni
            </h3>
            <div className="pl-8 space-y-2">
              <div className="flex items-start gap-2 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Önemli:</p>
                  <p className="text-sm text-yellow-700">
                    API anahtarınız için "Enable Futures" iznini etkinleştirmeniz gerekiyor
                  </p>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">1. API anahtarınızın yanındaki "Edit" butonuna tıklayın</p>
              <p className="text-sm text-muted-foreground">2. "Enable Futures" seçeneğini işaretleyin</p>
              <p className="text-sm text-muted-foreground">3. Güvenlik doğrulamasını tamamlayın</p>
            </div>
          </div>

          {/* Step 3 - Updated for IP Restriction */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">3</span>
              IP Adresi Kısıtlaması (ÖNEMLİ!)
            </h3>
            <div className="pl-8 space-y-2">
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-red-800 mb-1">Sunucu IP Adresi Gerekli</p>
                    <p className="text-sm text-red-700">
                      Sunucunuzun IP adresi: <code className="bg-white px-1 rounded font-mono">************</code>
                    </p>
                    <p className="text-sm text-red-700 mt-1">
                      Bu IP adresini mutlaka API anahtarınızın whitelist'ine eklemeniz gerekiyor.
                    </p>
                  </div>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">1. API anahtarınızın yanındaki "Edit" butonuna tıklayın</p>
              <p className="text-sm text-muted-foreground">2. "Restrict access to trusted IPs only" bölümünü bulun</p>
              <p className="text-sm text-muted-foreground">3. <code className="bg-slate-100 px-1 rounded">************</code> IP adresini ekleyin</p>
              <p className="text-sm text-muted-foreground">4. Değişiklikleri kaydedin ve 5-10 dakika bekleyin</p>
            </div>
          </div>

          {/* Troubleshooting - Enhanced for IP issues */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-red-600">🔧 Sorun Giderme</h3>
            <div className="space-y-3">

              <div className="border border-red-200 rounded-lg p-3 bg-red-50">
                <h4 className="font-medium text-sm mb-2 text-red-800">🚫 "IP Kısıtlaması" Hatası (En Sık Görülen)</h4>
                <ul className="text-sm text-red-700 space-y-1 list-disc list-inside">
                  <li><strong>Sunucu IP'si:</strong> ************</li>
                  <li>Bu IP'yi API anahtarınızın whitelist'ine eklemeniz şart</li>
                  <li>IP eklendikten sonra 5-10 dakika bekleyin</li>
                  <li>Hâlâ sorun varsa API anahtarını yeniden oluşturun</li>
                </ul>
              </div>

              <div className="border rounded-lg p-3">
                <h4 className="font-medium text-sm mb-2">❌ "API Key geçersiz" Hatası</h4>
                <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                  <li>API Key ve Secret'ı doğru kopyaladığınızdan emin olun</li>
                  <li>Boşluk karakteri olmadığını kontrol edin</li>
                  <li>API anahtarının aktif olduğunu doğrulayın</li>
                </ul>
              </div>

              <div className="border rounded-lg p-3">
                <h4 className="font-medium text-sm mb-2">❌ "Futures Trading izni gerekli" Hatası</h4>
                <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                  <li>API anahtarınız için "Enable Futures" iznini etkinleştirin</li>
                  <li>Futures hesabınızın aktif olduğunu kontrol edin</li>
                  <li>Binance Futures sözleşmesini kabul ettiğinizden emin olun</li>
                </ul>
              </div>

            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-red-800 mb-1">🔐 Güvenlik Uyarısı</h4>
                <ul className="text-sm text-red-700 space-y-1">
                  <li>• API anahtarlarınızı asla kimseyle paylaşmayın</li>
                  <li>• Sadece gerekli izinleri verin</li>
                  <li>• Düzenli olarak API anahtarlarınızı yenileyin</li>
                  <li>• Şüpheli aktivite durumunda hemen API anahtarını devre dışı bırakın</li>
                </ul>
              </div>
            </div>
          </div>

        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="flex flex-col gap-2">
      {showError && connectionStatus === 'error' && lastError && (
        <Alert variant="destructive" className="mb-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{lastError}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center gap-2">
        {isConnected ? (
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={`flex items-center gap-1 px-3 py-1 ${connectionStatus === 'authenticated' || connectionStatus === 'subscribed' ? 'bg-green-50 border-green-300' : ''}`}>
              {getStatusIndicator()}
              <User size={14} />
              <span>{userName}</span>
              <span className="text-xs text-blue-600 ml-1">(JSON)</span>
            </Badge>
            <div className="flex items-center gap-2">
              {connectionStatus === 'error' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReconnect}
                  className="text-amber-600 hover:text-amber-700"
                  disabled={!isOnline || isSubmitting}
                >
                  <RotateCw size={14} className={`mr-1 ${isSubmitting ? 'animate-spin' : ''}`} />
                  Yeniden Bağlan
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleDisconnect}
                className="text-destructive hover:text-destructive"
              >
                Bağlantıyı Kes
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              className="gap-2"
              variant={isSubmitting ? "outline" : "default"}
              onClick={handleConnect}
              disabled={isSubmitting || !isOnline}
            >
              <Link className="h-4 w-4" />
              {isSubmitting ? "Bağlanıyor..." : "Bağlan"}
            </Button>

            {/* Yardım butonu */}
            {renderHelpDialog()}

            {!defaultApiKey && (
              <span className="text-xs text-red-600">
                API anahtarı yok
              </span>
            )}
          </div>
        )}
        {!isConnected && getNetworkStatus()}
      </div>
    </div>
  );
};

export default AccountConnection;
