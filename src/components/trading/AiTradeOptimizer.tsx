import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { TradeStats } from '@/types/trading';

interface AiTradeOptimizerProps {
    isEnabled: boolean;
    onToggleEnabled: () => void;
    tradeStats: TradeStats;
    tradingDirection: 'long' | 'short';
    profitTarget: number;
    stopLoss: number;
    tradeUnit: number;
    leverage: number;
    onApplyRecommendations: (recommendations: {
        tradingDirection: 'long' | 'short';
        profitTarget: number;
        stopLoss: number;
        tradeUnit: number;
        leverage: number;
        reason: string;
    }) => void;
    lastAnalysisTime: Date | null;
    autoTradingEnabled: boolean;
}

const AiTradeOptimizer: React.FC<AiTradeOptimizerProps> = ({
    isEnabled,
    onToggleEnabled,
    tradeStats,
    tradingDirection,
    profitTarget,
    stopLoss,
    tradeUnit,
    leverage,
    onApplyRecommendations,
    lastAnalysisTime,
    autoTradingEnabled
}) => {
    return (
        <Card className="col-span-1">
            <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                    <CardTitle className="text-md">AI Optimizasyon</CardTitle>
                    <Switch
                        checked={isEnabled}
                        onCheckedChange={onToggleEnabled}
                        disabled={autoTradingEnabled}
                    />
                </div>
            </CardHeader>
            <CardContent className="text-sm">
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                    AI Optimizasyon şu anda devre dışı veya yüklenmedi.
                    Bu modül, trading stratejilerinizi optimize etmek için kullanılabilir.
                </p>
                <div className="flex items-center mt-4">
                    <Label className="text-xs text-gray-500">
                        Son Analiz: {lastAnalysisTime ? new Date(lastAnalysisTime).toLocaleTimeString() : 'Hiç yapılmadı'}
                    </Label>
                </div>
            </CardContent>
        </Card>
    );
};

export default AiTradeOptimizer; 