import React, { useEffect, useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useSignalDetection, SignalDetectionResult } from "@/hooks/useSignalDetection";
import { canOpenTrade, checkRateLimit } from '@/utils/tradingCalculations';
import { Trade } from "@/types/trading";
import {
    openShortPosition,
    checkTradingFilters,
    AdvancedTradingConfig,
    getManagedPositions
} from '@/services/advancedTradingService';

interface AutoTradingLogicProps {
    autoTradingEnabled: boolean;
    tradingDirection: 'long' | 'short';
    stopNewTrades: boolean;
    marketData: any[] | null;
    movingAverages: Record<string, { average: number; history: number[] }>;
    configSettings: {
        movingAveragePoints: number;
        alertPercentage: number;
        maxOpenPositions: number;
        minVolumeForTrade: number;
    };
    tradingRestrictions: {
        restrictNewCoins: boolean;
        maxTotalLossPercent: number;
        avoidNegativeFundingForShort: boolean;
        fundingTimeThresholdMinutes: number;
        minTradeInterval: number;
    };
    newCoinsMap?: Record<string, Date>;
    hasNegativeFunding: boolean;
    isNearFundingTime: boolean;
    cumulativeProfitLoss: number;
    initialBalance: number;
    trades: Trade[];
    lastTradeTimeMap: Record<string, Date>;
    openTradeForSymbol: (symbol: string, currentPrice: number) => void;
    // Yeni advanced trading için ek props
    tradingConfig?: {
        tradeAmount: number;
        leverage: number;
        takeProfitPercent: number;
        stopLossPercent: number;
    };
    useAdvancedTrading?: boolean;
}

const AutoTradingLogic: React.FC<AutoTradingLogicProps> = ({
    autoTradingEnabled,
    tradingDirection,
    stopNewTrades,
    marketData,
    movingAverages,
    configSettings,
    tradingRestrictions,
    newCoinsMap,
    hasNegativeFunding,
    isNearFundingTime,
    cumulativeProfitLoss,
    initialBalance,
    trades,
    lastTradeTimeMap,
    openTradeForSymbol,
    tradingConfig,
    useAdvancedTrading = false
}) => {
    const { toast } = useToast();
    const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());
    const [configSettingsChanged, setConfigSettingsChanged] = useState<boolean>(false);
    const [signals, setSignals] = useState<SignalDetectionResult[]>([]);
    const [tradableCoins, setTradableCoins] = useState<string[]>([]);

    // 🚨 RATE LIMITING: Auto trading çok sık çalışmasın
    const lastAutoTradeTimeRef = useRef<number>(0);
    const actualTradeTimeRef = useRef<number>(0); // Gerçek işlem zamanı
    const lastProcessedSignalsRef = useRef<Map<string, number>>(new Map()); // İşlenmiş sinyaller (timestamp ile)

    // useSignalDetection hook'unu doğru şekilde kullan
    const { detectSignals, detectedSignals } = useSignalDetection(
        {
            movingAveragePoints: configSettings.movingAveragePoints,
            alertPercentage: configSettings.alertPercentage
        },
        movingAverages,
        tradingDirection
    );

    // Sinyal istatistikleri hesapla
    const calculateSignalStats = () => {
        if (!marketData) {
            return {
                totalCoins: 0,
                analyzableCoins: 0,
                signalDetectedCoins: 0,
                criteriaMatchingCoins: 0
            };
        }

        const analyzableCoins = marketData.filter(ticker => {
            const symbol = ticker.s;
            const maData = movingAverages[symbol];
            return maData && maData.average && maData.history.length >= configSettings.movingAveragePoints;
        }).length;

        return {
            totalCoins: marketData.length,
            analyzableCoins,
            signalDetectedCoins: signals.length,
            criteriaMatchingCoins: tradableCoins.length
        };
    };

    const signalStats = calculateSignalStats();

    // Criteriar eligible coins hesapla
    const criteriaEligibleCoins = new Set<string>(tradableCoins);

    // Sinyal tespiti ve işlem kararları için useEffect
    useEffect(() => {
        if (!autoTradingEnabled || stopNewTrades || !marketData) return;

        const openTrades = trades.filter(t => t.status === 'open');
        const currentOpenTradesCount = openTrades.length;
        let tradableCoinsList: string[] = [];

        // 🚨 SMART RATE LIMITING: 
        const now = Date.now();
        const timeSinceLastCheck = now - lastAutoTradeTimeRef.current;
        const timeSinceLastActualTrade = now - actualTradeTimeRef.current;

        // Normal kontrol için 3 saniye, gerçek işlem sonrası 15 saniye bekle
        const requiredInterval = timeSinceLastActualTrade < 15000 ? 15000 : 3000;

        if (timeSinceLastCheck < requiredInterval) {
            const waitTimeMs = timeSinceLastActualTrade < 15000 ?
                (15000 - timeSinceLastActualTrade) :
                (3000 - timeSinceLastCheck);
            console.log(`🔄 Smart rate limit: ${Math.floor(waitTimeMs)}ms daha beklemeli`);
            return;
        }
        lastAutoTradeTimeRef.current = now;

        // Sinyal tespiti - doğru hook kullanımı
        const detectionResults = detectSignals(marketData);

        if (!detectionResults || detectionResults.length === 0) {
            setSignals([]);
            setTradableCoins([]);
            // Sinyal yoksa eski sinyalleri koruyoruz (temizlemiyoruz)
            return;
        }

        // 🚨 DUPLICATE SIGNAL PREVENTION: Aynı sinyalleri tekrar işleme (fiyat tabanlı)
        const newSignals = detectionResults.filter(signal => {
            const signalKey = `${signal.symbol}-${signal.priceAboveMA.toFixed(1)}`; // Daha geniş tolerance için 1 basamak

            // Son 5 dakikada bu sinyali gördük mü?
            const existingSignalTime = lastProcessedSignalsRef.current.get(signalKey);
            if (existingSignalTime) {
                const timeDiff = now - existingSignalTime;
                if (timeDiff < 300000) { // 5 dakika = 300000ms (daha kısa süre)
                    console.log(`🔄 ${signal.symbol} sinyali son 5 dakikada işlendi, atlanıyor`);
                    return false; // Bu sinyal çok yakın zamanda işlendi
                }
            }

            return true;
        });

        if (newSignals.length === 0) {
            console.log(`🔄 Tüm sinyaller son 5 dakikada işlenmiş, atlanıyor`);
            return;
        }

        console.log(`🔍 ${detectionResults.length} sinyal tespit edildi, ${newSignals.length} yeni sinyal`);

        // İşlenmiş sinyalleri güncelle (timestamp ile)
        newSignals.forEach(signal => {
            const signalKey = `${signal.symbol}-${signal.priceAboveMA.toFixed(1)}`;
            lastProcessedSignalsRef.current.set(signalKey, now);
            console.log(`📝 Sinyal kaydedildi: ${signalKey} - ${new Date(now).toLocaleTimeString()}`);
        });

        // 50'den fazla sinyal varsa eski olanları sil
        if (lastProcessedSignalsRef.current.size > 50) {
            const signalsArray = Array.from(lastProcessedSignalsRef.current.entries());
            const newMap = new Map(signalsArray.slice(-30)); // Son 30'unu sakla
            lastProcessedSignalsRef.current = newMap;
        }

        // Her coin için trading kontrolü yap - sadece yeni sinyalleri kontrol et
        for (const signal of newSignals) {
            const symbol = signal.symbol;

            // 🚨 DUPLICATE PREVENTION: Eğer bu coin için zaten işlem var mı?
            const hasExistingPosition = openTrades.some(t =>
                t.symbol === symbol &&
                t.direction === tradingDirection
            );

            if (hasExistingPosition) {
                console.log(`🔵 ${symbol} için zaten ${tradingDirection} pozisyon var, atlanıyor`);
                continue;
            }

            // 🚨 RATE LIMITING: Son işlem zamanı kontrolü (sembol bazında)
            const lastTradeTime = lastTradeTimeMap[symbol];
            if (lastTradeTime) {
                const timeSinceLastTrade = (now - lastTradeTime.getTime()) / 1000;
                if (timeSinceLastTrade < tradingRestrictions.minTradeInterval) {
                    console.log(`🚫 ${symbol} için rate limit: ${Math.floor(timeSinceLastTrade)}s geçti, minimum ${tradingRestrictions.minTradeInterval}s gerekli`);
                    continue;
                }
            }

            // Maksimum açık pozisyon kontrolü
            if (currentOpenTradesCount >= configSettings.maxOpenPositions) {
                console.log(`🚫 Maksimum pozisyon limitine ulaşıldı (${currentOpenTradesCount}/${configSettings.maxOpenPositions})`);
                break; // Daha fazla coin kontrol etmeye gerek yok
            }

            // Trading kısıtlamalarını kontrol et
            const ticker = marketData.find(t => t.s === symbol);
            if (!ticker) continue;

            const currentPrice = parseFloat(ticker.c || '0');
            const volume24h = parseFloat(ticker.q || '0');

            // Düşük hacim kontrolü
            const minVolumeMillions = configSettings.minVolumeForTrade || 5;
            const minVolumeUSDT = minVolumeMillions * 1000000;

            if (volume24h < minVolumeUSDT) {
                console.log(`🚫 ${symbol} için düşük hacim: $${(volume24h / 1000000).toFixed(2)}M - Minimum ${minVolumeMillions}M gerekli`);
                continue;
            }

            // Son işlem zamanı kontrolü (rate limiting)
            const tradeRestrictionCheck = canOpenTrade(
                symbol,
                tradingDirection,
                configSettings,
                tradingRestrictions,
                lastTradeTime,
                undefined // fundingRates parametresi
            );

            if (!tradeRestrictionCheck.allowed) {
                console.log(`🚫 ${symbol} için işlem kısıtlandı: ${tradeRestrictionCheck.reason}`);
                continue;
            }

            // Tüm koşulları geçti, işlem açılabilir
            tradableCoinsList.push(symbol);

            // 🚨 İŞLEM SAYISI SINIRI: Aynı anda maksimum 1 işlem aç (daha muhafazakar)
            if (tradableCoinsList.length >= 1) {
                console.log(`⚠️ Tek seferde maksimum 1 işlem açılabilir, güvenlik önlemi`);
                break;
            }
        }

        setSignals(detectionResults);
        setTradableCoins(tradableCoinsList);

        // 🚨 CONTROLLED TRADE OPENING: Aynı anda sadece 1 işlem aç
        if (tradableCoinsList.length > 0) {
            const selectedSymbol = tradableCoinsList[0]; // İlk symbolu seç
            const ticker = marketData.find(t => t.s === selectedSymbol);

            if (ticker) {
                const currentPrice = parseFloat(ticker.c || '0');
                console.log(`🎯 Auto trading seçimi: ${selectedSymbol} - Fiyat: ${currentPrice}`);

                // Gerçek işlem zamanını güncelle
                actualTradeTimeRef.current = now;

                // Advanced Trading kullanılıyor mu?
                if (useAdvancedTrading && tradingConfig && tradingDirection === 'short') {
                    // Advanced Trading işlemi - rate limiting ile korunuyor
                    setTimeout(async () => {
                        try {
                            // Advanced Trading Config oluştur
                            const advancedConfig: AdvancedTradingConfig = {
                                tradeAmount: tradingConfig.tradeAmount,
                                leverage: tradingConfig.leverage,
                                takeProfitPercent: tradingConfig.takeProfitPercent,
                                stopLossPercent: tradingConfig.stopLossPercent,
                                maxOpenPositions: configSettings.maxOpenPositions,
                                minVolumeForTrade: configSettings.minVolumeForTrade
                            };

                            // Advanced Trading filtreleri kontrol et
                            const filterCheck = checkTradingFilters(
                                selectedSymbol,
                                ticker,
                                currentOpenTradesCount,
                                advancedConfig
                            );

                            if (!filterCheck.allowed) {
                                console.log(`🚫 ${selectedSymbol} Advanced Trading filtresinde başarısız: ${filterCheck.reason}`);
                                return;
                            }

                            // ISOLATED modda SHORT pozisyon aç
                            const result = await openShortPosition(selectedSymbol, currentPrice, advancedConfig);

                            if (result.success) {
                                console.log(`✅ ${selectedSymbol} Advanced SHORT pozisyon açıldı - Order ID: ${result.orderId}`);

                                // Toast bildirim
                                toast({
                                    title: "Pozisyon Açıldı",
                                    description: `${selectedSymbol} için ISOLATED modda SHORT pozisyon açıldı (${advancedConfig.leverage}x kaldıraç)`,
                                });
                            } else {
                                console.error(`❌ ${selectedSymbol} Advanced SHORT pozisyon açılamadı: ${result.error}`);

                                toast({
                                    title: "Pozisyon Açılamadı",
                                    description: `${selectedSymbol}: ${result.error}`,
                                    variant: "destructive",
                                });
                            }
                        } catch (error) {
                            console.error(`❌ ${selectedSymbol} Advanced Trading hatası:`, error);

                            toast({
                                title: "İşlem Hatası",
                                description: `${selectedSymbol} için advanced trading hatası: ${error}`,
                                variant: "destructive",
                            });
                        }
                    }, 2000 + Math.random() * 3000); // 2-5 saniye arası random delay
                } else {
                    // Normal trading mantığı - rate limiting ile korunuyor
                    setTimeout(() => {
                        console.log(`🔄 [OTOMATİK İŞLEM] ${selectedSymbol} için işlem açılıyor...`);
                        openTradeForSymbol(selectedSymbol, currentPrice);
                    }, 2000 + Math.random() * 3000); // 2-5 saniye arası random delay
                }
            }
        }

        // Her güncellemede zaman damgasını güncelle
        setLastUpdateTime(new Date());

        // Settings değişikliği
        if (configSettingsChanged) {
            toast({
                title: "Kriter Ayarları Güncellendi",
                description: `${tradableCoinsList.length} coin tüm kriterleri karşılıyor.`,
                variant: "default",
            });
            setConfigSettingsChanged(false);
        }
    }, [
        autoTradingEnabled,
        stopNewTrades,
        marketData,
        movingAverages,
        configSettings,
        tradingRestrictions,
        newCoinsMap,
        hasNegativeFunding,
        isNearFundingTime,
        cumulativeProfitLoss,
        initialBalance,
        trades,
        lastTradeTimeMap,
        openTradeForSymbol,
        tradingDirection,
        toast,
        configSettingsChanged,
        detectSignals,
        useAdvancedTrading,
        tradingConfig
    ]);

    // Settings değiştiğinde configSettingsChanged'i true yap
    useEffect(() => {
        setConfigSettingsChanged(true);
    }, [configSettings]);

    // Hiçbir UI render etme, sadece mantığı çalıştır
    if (!autoTradingEnabled) return null;

    return (
        <Card className="animate-fade-in mt-4">
            <CardHeader className="pb-2 pt-3">
                <CardTitle className="flex items-center justify-between text-md">
                    <span className="flex items-center gap-2">
                        🎯 Otomatik İşlem Sistemi
                        <Badge variant={tradingDirection === 'long' ? "default" : "destructive"}>
                            {tradingDirection.toUpperCase()} Modunda
                        </Badge>
                    </span>
                    <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
                        <span className="text-xs text-muted-foreground">Aktif Tarama</span>
                    </div>
                </CardTitle>
                <CardDescription>
                    Son güncelleme: {lastUpdateTime.toLocaleTimeString()}
                </CardDescription>
            </CardHeader>
            <CardContent className="py-2">
                <div className="grid grid-cols-4 gap-3 text-center">
                    <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
                        <div className="text-lg font-bold text-blue-600">
                            {signalStats.totalCoins}
                        </div>
                        <div className="text-xs text-blue-500">Toplam Sembol</div>
                    </div>
                    <div className="bg-green-50 rounded-lg p-3 border border-green-100">
                        <div className="text-lg font-bold text-green-600">
                            {signalStats.analyzableCoins}
                        </div>
                        <div className="text-xs text-green-500">Analiz Edilebilir</div>
                    </div>
                    <div className="bg-purple-50 rounded-lg p-3 border border-purple-100">
                        <div className="text-lg font-bold text-purple-600">
                            {signalStats.signalDetectedCoins}
                        </div>
                        <div className="text-xs text-purple-500">Sinyal Tespit</div>
                    </div>
                    <div className="bg-orange-50 rounded-lg p-3 border border-orange-100">
                        <div className="text-lg font-bold text-orange-600">
                            {signalStats.criteriaMatchingCoins}
                        </div>
                        <div className="text-xs text-orange-500">Kriterleri Sağlayan</div>
                    </div>
                </div>

                {criteriaEligibleCoins.size > 0 && (
                    <div className="mt-3 p-2 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium mb-1">Kriterleri Karşılayan Coinler:</div>
                        <div className="flex flex-wrap gap-1">
                            {Array.from(criteriaEligibleCoins).map((symbol: string) => (
                                <Badge key={symbol} variant="outline" className="bg-green-50">
                                    {symbol}
                                </Badge>
                            ))}
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default AutoTradingLogic; 
