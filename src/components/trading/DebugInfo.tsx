
import React from 'react';

interface DebugInfoProps {
  onSubmitExists: boolean;
  isFormDisabled: boolean;
  validationResult: any;
  isPerpetual: boolean;
  currentPrice: number;
  amount: string;
}

export const DebugInfo: React.FC<DebugInfoProps> = ({
  onSubmitExists,
  isFormDisabled,
  validationResult,
  isPerpetual,
  currentPrice,
  amount
}) => {
  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded">
      <div>Debug: onSubmit={onSubmitExists ? 'Tanımlı' : 'Tanımsız'}</div>
      <div>disabled={isFormDisabled ? 'Evet' : 'Hayır'}</div>
      <div>validation={validationResult?.isValid ? 'Geçerli' : 'G<PERSON>çersiz'}</div>
      <div>perpetual={isPerpetual ? 'Evet' : 'Hayır'}</div>
      <div>currentPrice={currentPrice}</div>
      <div>amount={amount} (parsed: {parseFloat(amount || '0')})</div>
    </div>
  );
};
