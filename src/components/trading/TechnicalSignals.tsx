import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { AlertCircle, TrendingUp, Activity } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface TechnicalSignalsProps {
    isActive: boolean;
    direction: 'long' | 'short';
    criteriaMetCoins: string[];
    alertPercentage: number;
    movingAveragePoints: number;
    maxPositions: number;
    leverage?: number;
}

const TechnicalSignals: React.FC<TechnicalSignalsProps> = ({
    isActive,
    direction,
    criteriaMetCoins,
    alertPercentage,
    movingAveragePoints,
    maxPositions,
    leverage = 20
}) => {
    return (
        <Card className="border-blue-400/20">
            <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Activity className="h-4 w-4 text-blue-500" />
                        Teknik <PERSON>yaller
                    </div>
                    <Badge variant={isActive ? "default" : "outline"} className={isActive ? "bg-green-500" : ""}>
                        {isActive ? "Aktif" : "Pasif"}
                    </Badge>
                </CardTitle>
            </CardHeader>
            <CardContent className="pb-3">
                <div className="grid gap-2">
                    <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                            <TrendingUp className="h-4 w-4 text-blue-500" />
                            <span>İşlem Stratejisi:</span>
                        </div>
                        <Badge variant={direction === 'short' ? "destructive" : "default"} className={direction === 'short' ? "bg-red-500" : "bg-green-500"}>
                            {direction === 'long' ? "LONG (ALIŞ)" : "SHORT (SATIŞ)"}
                        </Badge>
                    </div>

                    <div className="text-sm grid gap-1">
                        <div className="flex justify-between">
                            <span>Sinyal Koşulu:</span>
                            <span className="font-medium">MA{movingAveragePoints} + %{alertPercentage}</span>
                        </div>
                        <div className="flex justify-between">
                            <span>Kullanılan Kaldıraç:</span>
                            <span className="font-medium">{leverage}x</span>
                        </div>
                        <div className="flex justify-between">
                            <span>Maksimum Pozisyon:</span>
                            <span className="font-medium">{maxPositions}</span>
                        </div>
                        <div className="flex justify-between">
                            <span>Sinyal Tespit Edilen:</span>
                            <span className="font-medium text-amber-500">{criteriaMetCoins.length} coin</span>
                        </div>
                    </div>

                    {criteriaMetCoins.length > 0 && (
                        <div className="mt-2">
                            <p className="text-xs text-muted-foreground mb-1">Sinyal Tespit Edilen Coinler:</p>
                            <div className="flex flex-wrap gap-1">
                                {criteriaMetCoins.slice(0, 6).map(coin => (
                                    <Badge key={coin} variant="outline" className="text-xs">
                                        {coin}
                                    </Badge>
                                ))}
                                {criteriaMetCoins.length > 6 && (
                                    <Badge variant="outline" className="text-xs">
                                        +{criteriaMetCoins.length - 6} daha
                                    </Badge>
                                )}
                            </div>
                        </div>
                    )}

                    {!isActive && (
                        <div className="flex items-center gap-2 mt-1 text-xs text-amber-500">
                            <AlertCircle className="h-3 w-3" />
                            <span>Otomatik işlem başlatmak için "İşlemi Başlat" butonuna tıklayın</span>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
};

export default TechnicalSignals; 
