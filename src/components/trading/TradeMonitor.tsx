import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Play, Pause, Radar } from "lucide-react";
import { cn } from "@/lib/utils";
import { Trade } from '@/types/trading';
import { calculateStopLossPrice, calculateTakeProfitPrice } from '@/utils/tradingCalculations';

interface TradeMonitorProps {
    trades: Trade[];
    closeTrade: (tradeId: string, additionalInfo?: {
        closeReason?: 'stop_loss' | 'take_profit' | 'emergency_stop_loss' | null,
        profitLossPercent?: number,
        isAutomatic?: boolean,
        fastPriceMovement?: boolean
    }) => Promise<void>;
    stopLoss: number;
    profitTarget: number;
    leverage: number;
    getTradePriceForSymbol: (symbol: string) => number;
    monitoringFrequency?: number;
    credentials?: {
        apiKey: string;
        apiSecret?: string;
    };
    onUpdateTrade?: (updatedTrade: Trade) => void;
    onUpdateBalance?: (balances: any[]) => void;
}

const TradeMonitor: React.FC<TradeMonitorProps> = ({
    trades,
    closeTrade,
    stopLoss,
    profitTarget,
    leverage,
    getTradePriceForSymbol,
    monitoringFrequency = 1000,
    credentials,
    onUpdateTrade,
    onUpdateBalance
}) => {
    const [monitoringStatus, setMonitoringStatus] = useState<'active' | 'inactive'>('inactive');
    const [lastMonitoringTime, setLastMonitoringTime] = useState<Date | null>(null);
    const [tradeStatuses, setTradeStatuses] = useState<Record<string, any>>({});

    const monitoringIntervalRef = useRef<NodeJS.Timeout | null>(null);

    const safeLog = (message: string, ...args: any[]) => {
        try {
            console.log(message, ...args);
        } catch (error) {
            console.warn('Log error:', error);
        }
    };

    const monitorOpenTrades = useCallback(() => {
        const openTrades = trades.filter(trade => trade.status === 'open');
        const updatedStatuses: Record<string, any> = {};

        if (openTrades.length === 0) {
            return;
        }

        safeLog(`🔍 İşlem monitörü: ${openTrades.length} açık işlem kontrol ediliyor...`);

        openTrades.forEach(trade => {
            const currentPrice = getTradePriceForSymbol(trade.symbol);

            if (!currentPrice) {
                console.warn(`⚠️ ${trade.symbol} için fiyat bilgisi alınamadı!`);
                return;
            }

            let profitLossPercent = 0;

            // Kar/zarar yüzdesini hesapla
            if (trade.direction === 'long') {
                profitLossPercent = ((currentPrice - trade.entryPrice) / trade.entryPrice) * 100 * leverage;
            } else {
                profitLossPercent = ((trade.entryPrice - currentPrice) / trade.entryPrice) * 100 * leverage;
            }

            const stopLossPrice = calculateStopLossPrice(trade.entryPrice, trade.direction, stopLoss, leverage);
            const takeProfitPrice = calculateTakeProfitPrice(trade.entryPrice, trade.direction, profitTarget, leverage);

            safeLog(`📊 ${trade.symbol} izleniyor: P/L: ${profitLossPercent.toFixed(2)}%`);

            updatedStatuses[trade.id] = {
                currentPrice,
                profitLossPercent,
                stopLossPrice,
                takeProfitPrice,
            };

            // Stop Loss kontrolü
            if ((trade.direction === 'long' && currentPrice <= stopLossPrice) ||
                (trade.direction === 'short' && currentPrice >= stopLossPrice)) {
                safeLog(`🔴 ${trade.symbol} için stop loss tetiklendi!`);

                closeTrade(trade.id, {
                    closeReason: 'stop_loss',
                    profitLossPercent,
                    isAutomatic: true
                }).catch(error => {
                    console.error(`❌ Stop loss işleminde hata: ${trade.symbol}`, error);
                });
                return;
            }

            // Take Profit kontrolü
            if (profitLossPercent >= profitTarget) {
                safeLog(`✅ ${trade.symbol} için take profit tetiklendi!`);

                closeTrade(trade.id, {
                    closeReason: 'take_profit',
                    profitLossPercent,
                    isAutomatic: true
                }).catch(error => {
                    console.error(`❌ Take profit işleminde hata: ${trade.symbol}`, error);
                });
                return;
            }
        });

        if (Object.keys(updatedStatuses).length > 0) {
            setTradeStatuses(prev => ({
                ...prev,
                ...updatedStatuses
            }));
        }
    }, [trades, getTradePriceForSymbol, stopLoss, profitTarget, leverage, closeTrade]);

    const startMonitoring = () => {
        if (monitoringIntervalRef.current) {
            clearInterval(monitoringIntervalRef.current);
        }

        setMonitoringStatus('active');
        safeLog(`🔄 İşlem izleme başlatılıyor, Frekans: ${monitoringFrequency}ms`);

        monitoringIntervalRef.current = setInterval(() => {
            monitorOpenTrades();
            setLastMonitoringTime(new Date());
        }, monitoringFrequency);
    };

    const stopMonitoring = () => {
        if (monitoringIntervalRef.current) {
            clearInterval(monitoringIntervalRef.current);
            monitoringIntervalRef.current = null;
        }
        setMonitoringStatus('inactive');
    };

    useEffect(() => {
        return () => {
            if (monitoringIntervalRef.current) {
                clearInterval(monitoringIntervalRef.current);
            }
        };
    }, []);

    const openTradesCount = trades.filter(trade => trade.status === 'open').length;

    return (
        <Card className="shadow-sm">
            <CardHeader className="pb-1 pt-2">
                <CardTitle className="text-sm flex items-center space-x-2">
                    <Radar className="h-4 w-4 text-blue-500" />
                    <span>İşlem Monitörü</span>
                    <Badge
                        variant={monitoringStatus === 'active' ? 'default' : 'outline'}
                        className={cn(monitoringStatus === 'active' ? 'bg-green-500 animate-pulse' : '')}
                    >
                        {monitoringStatus === 'active' ? 'Aktif' : 'Pasif'}
                    </Badge>
                </CardTitle>
                <CardDescription className="text-xs">
                    {lastMonitoringTime
                        ? `Son kontrol: ${lastMonitoringTime.toLocaleTimeString()}`
                        : 'Henüz kontrol edilmedi'
                    }
                </CardDescription>
            </CardHeader>
            <CardContent className="pt-1">
                <div className="flex items-center justify-between mb-2">
                    <div className="text-xs text-muted-foreground">
                        Açık İşlemler: {openTradesCount}
                    </div>
                    <Button
                        size="sm"
                        variant={monitoringStatus === 'active' ? 'destructive' : 'default'}
                        onClick={monitoringStatus === 'active' ? stopMonitoring : startMonitoring}
                        className="h-7 px-3"
                    >
                        {monitoringStatus === 'active' ? (
                            <>
                                <Pause className="h-3 w-3 mr-1" />
                                Durdur
                            </>
                        ) : (
                            <>
                                <Play className="h-3 w-3 mr-1" />
                                Başlat
                            </>
                        )}
                    </Button>
                </div>

                {openTradesCount > 0 && (
                    <div className="text-xs space-y-1">
                        {trades.filter(trade => trade.status === 'open').map(trade => {
                            const status = tradeStatuses[trade.id];
                            return (
                                <div key={trade.id} className="flex justify-between items-center p-1 bg-muted rounded">
                                    <span className="font-mono">{trade.symbol}</span>
                                    <span className={cn(
                                        "font-mono",
                                        status?.profitLossPercent > 0 ? "text-green-600" : "text-red-600"
                                    )}>
                                        {status?.profitLossPercent ? `${status.profitLossPercent.toFixed(2)}%` : '--'}
                                    </span>
                                </div>
                            );
                        })}
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default TradeMonitor; 
