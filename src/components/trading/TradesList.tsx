import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, ArrowUp, ArrowDown, Clock, Coins } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { calculateTradeDetails } from '@/utils/tradingCalculations';

type Trade = {
  id: string;
  symbol: string;
  entryPrice: number;
  quantity: number;
  direction: 'long' | 'short';
  openTime: Date;
  status: 'open' | 'closed';
  profitLoss?: number;
  exitPrice?: number;
  exitTime?: Date;
  leverage?: number;
  investmentAmount?: number;
};

interface TradesListProps {
  trades: Trade[];
  marketData: any[] | null;
  onCloseTrade: (id: string) => void;
}

const TradesList: React.FC<TradesListProps> = ({ trades, marketData, onCloseTrade }) => {
  // Format price with appropriate precision based on price value
  const formatPrice = (price: number): string => {
    if (price < 0.0001) return price.toFixed(8);
    if (price < 0.01) return price.toFixed(6);
    if (price < 1) return price.toFixed(5);
    if (price < 10) return price.toFixed(4);
    if (price < 1000) return price.toFixed(2);
    return price.toLocaleString();
  };
  
  const calculateCurrentPnl = (trade: Trade): { percentage: number; amount: number } => {
    if (!marketData || !Array.isArray(marketData)) return { percentage: 0, amount: 0 };
    
    const ticker = marketData.find(t => t.s === trade.symbol);
    if (!ticker || !ticker.c) return { percentage: 0, amount: 0 };
    
    const currentPrice = parseFloat(ticker.c);
    const leverage = trade.leverage || 1;
    const investmentAmount = trade.investmentAmount || 0;
    
    // Düzeltilen kısım: pozisyon yönüne göre kâr/zarar hesaplaması
    let percentageGain = 0;
    if (trade.direction === 'long') {
      // Long pozisyonda: (güncel fiyat - giriş fiyatı) / giriş fiyatı * 100 * kaldıraç
      percentageGain = ((currentPrice - trade.entryPrice) / trade.entryPrice) * 100 * leverage;
    } else {
      // Short pozisyonda: (giriş fiyatı - güncel fiyat) / giriş fiyatı * 100 * kaldıraç
      percentageGain = ((trade.entryPrice - currentPrice) / trade.entryPrice) * 100 * leverage;
    }
    
    const amountGain = (investmentAmount * percentageGain) / 100;
    return { percentage: percentageGain, amount: amountGain };
  };

  return (
    <Card className="animate-fade-in">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Açık İşlemler</span>
          <Badge>{trades.length} İşlem</Badge>
        </CardTitle>
        <CardDescription>
          Aktif olarak devam eden işlemleriniz
        </CardDescription>
      </CardHeader>
      <CardContent>
        {trades.length === 0 ? (
          <p className="text-center py-4 text-muted-foreground">
            Henüz açık işlem bulunmuyor.
          </p>
        ) : (
          <div className="overflow-auto">
            <table className="w-full">
              <thead className="bg-muted/30 text-left">
                <tr>
                  <th className="px-4 py-2 text-xs font-medium">Sembol</th>
                  <th className="px-4 py-2 text-xs font-medium">Yön</th>
                  <th className="px-4 py-2 text-xs font-medium">Giriş</th>
                  <th className="px-4 py-2 text-xs font-medium">Tutar ($)</th>
                  <th className="px-4 py-2 text-xs font-medium">
                    <span className="flex items-center gap-1">
                      <Coins className="h-3 w-3" />
                      Miktar
                    </span>
                  </th>
                  <th className="px-4 py-2 text-xs font-medium">Kaldıraç</th>
                  <th className="px-4 py-2 text-xs font-medium">Kar/Zarar</th>
                  <th className="px-4 py-2 text-xs font-medium">Süre</th>
                  <th className="px-4 py-2 text-xs font-medium text-right">İşlemler</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                {trades.map(trade => {
                  const pnl = calculateCurrentPnl(trade);
                  const duration = formatDistanceToNow(trade.openTime, { addSuffix: false });
                  
                  // Calculate coin quantity and details
                  const tradeDetails = calculateTradeDetails(
                    trade.investmentAmount || 0,
                    trade.leverage || 1,
                    trade.entryPrice,
                    trade.symbol
                  );
                  
                  return (
                    <tr key={trade.id} className="hoverable-row">
                      <td className="px-4 py-2 text-sm">{trade.symbol}</td>
                      <td className="px-4 py-2">
                        <Badge variant={trade.direction === 'long' ? "default" : "destructive"} className="capitalize">
                          {trade.direction === 'long' ? (
                            <span className="flex items-center gap-1">
                              <ArrowUp className="h-3 w-3" />
                              Long
                            </span>
                          ) : (
                            <span className="flex items-center gap-1">
                              <ArrowDown className="h-3 w-3" />
                              Short
                            </span>
                          )}
                        </Badge>
                      </td>
                      <td className="px-4 py-2 text-sm">${formatPrice(trade.entryPrice)}</td>
                      <td className="px-4 py-2 text-sm">${trade.investmentAmount?.toFixed(2)}</td>
                      <td className="px-4 py-2 text-sm">
                        <div className="text-xs">
                          <div className="font-medium">{tradeDetails.formattedQuantity}</div>
                          <div className="text-muted-foreground">{tradeDetails.coinSymbol}</div>
                        </div>
                      </td>
                      <td className="px-4 py-2 text-sm">{trade.leverage}x</td>
                      <td className="px-4 py-2">
                        <div className={`text-sm font-medium ${
                          pnl.percentage >= 0 ? 'text-green-500' : 'text-red-500'
                        }`}>
                          <div>{pnl.percentage >= 0 ? '+' : ''}{pnl.percentage.toFixed(2)}%</div>
                          <div className="text-xs">
                            {pnl.amount >= 0 ? '+' : ''}{pnl.amount.toFixed(2)}$
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-2">
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          {duration}
                        </div>
                      </td>
                      <td className="px-4 py-2 text-right">
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button 
                              variant="outline" 
                              size="sm"
                              className="text-destructive hover:bg-destructive/10 hover:text-destructive"
                            >
                              <X className="h-4 w-4" />
                              <span className="sr-only">İşlemi Kapat</span>
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>İşlemi Kapatmayı Onayla</AlertDialogTitle>
                              <AlertDialogDescription>
                                {trade.symbol} için açtığınız {trade.direction} pozisyonunu kapatmak istediğinize emin misiniz?
                                <div className="mt-2 space-y-1">
                                  <div>Miktar: {tradeDetails.formattedQuantity} {tradeDetails.coinSymbol}</div>
                                  <div className="font-semibold">
                                    Kar/Zarar: {pnl.percentage >= 0 ? '+' : ''}{pnl.percentage.toFixed(2)}% (${pnl.amount.toFixed(2)})
                                  </div>
                                </div>
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>İptal</AlertDialogCancel>
                              <AlertDialogAction 
                                onClick={() => onCloseTrade(trade.id)}
                                className="bg-destructive hover:bg-destructive/90"
                              >
                                İşlemi Kapat
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TradesList;
