
import React from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertTriangle } from 'lucide-react';

interface TradingAmountInputProps {
  amount: string;
  onAmountChange: (amount: string) => void;
  totalTradeValue: number;
  minAmount: number;
  // DÜZENLEME: maxDailyTrades property tamamen kaldırıldı
  validationResult: any;
  tradeDetails: any;
  currentPrice: number;
}

export const TradingAmountInput: React.FC<TradingAmountInputProps> = ({
  amount,
  onAmountChange,
  totalTradeValue,
  minAmount,
  // DÜZENLEME: maxDailyTrades parametresi kaldırıldı
  validationResult,
  tradeDetails,
  currentPrice
}) => {
  return (
    <div className="space-y-2">
      <Label>İşlem Tutarı (USDT)</Label>
      <Input
        type="number"
        value={amount}
        onChange={(e) => onAmountChange(e.target.value)}
        min={minAmount}
        step="1"
        className={validationResult && !validationResult.isValid ? 'border-red-500' : ''}
      />
      <div className="text-sm text-muted-foreground">
        <div>Toplam İşlem Değeri: ${totalTradeValue.toFixed(2)}</div>
        {tradeDetails && (
          <div className="text-xs mt-1 p-2 bg-blue-50 rounded border-l-2 border-blue-200">
            <div className="font-medium text-blue-800">
              Alınacak Miktar: {tradeDetails.formattedQuantity} {tradeDetails.coinSymbol}
            </div>
            <div className="text-blue-600">
              Birim Fiyat: ${currentPrice.toFixed(6)}
            </div>
          </div>
        )}
        {minAmount > 0 && (
          <div className="text-xs">
            Minimum: ${minAmount.toFixed(2)} ({Math.round(totalTradeValue / parseFloat(amount || '1'))}x kaldıraçla)
          </div>
        )}
        <div className="text-xs text-green-600 mt-1 bg-green-50 p-1 rounded">
          ✅ Günlük İşlem Limiti Kaldırıldı - Sınırsız işlem yapabilirsiniz!
        </div>
      </div>
      {validationResult && !validationResult.isValid && (
        <div className="text-xs text-red-500 flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          {validationResult.reason}
        </div>
      )}
      {validationResult && validationResult.isValid && validationResult.reason && (
        <div className="text-xs text-yellow-600">
          ⚠️ {validationResult.reason}
        </div>
      )}
    </div>
  );
};
