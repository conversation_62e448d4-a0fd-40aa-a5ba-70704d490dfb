
import React from 'react';
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { ArrowUpRight, ArrowDownRight } from 'lucide-react';

interface TradingButtonsProps {
  direction: 'long' | 'short';
  onDirectionChange: (direction: 'long' | 'short') => void;
  onSubmit: () => void;
  isDisabled: boolean;
  symbol: string;
  leverage: number;
  amount: string;
  formattedPrice: string;
  totalTradeValue: number;
  validationResult: any;
}

export const TradingButtons: React.FC<TradingButtonsProps> = ({
  direction,
  onDirectionChange,
  onSubmit,
  isDisabled,
  symbol,
  leverage,
  amount,
  formattedPrice,
  totalTradeValue,
  validationResult
}) => {
  const handleLongClick = () => {
    console.log('📈 Long butonu tıklandı');
    onDirectionChange('long');
  };

  const handleShortClick = () => {
    console.log('📉 Short butonu tıklandı');
    onDirectionChange('short');
  };

  const handleLongConfirm = () => {
    console.log('✅ Long işlemi onaylandı');
    onSubmit();
  };

  const handleShortConfirm = () => {
    console.log('✅ Short işlemi onaylandı');
    onSubmit();
  };

  return (
    <div className="flex gap-2">
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant={direction === 'long' ? "default" : "outline"}
            onClick={handleLongClick}
            className="flex-1"
            disabled={isDisabled}
          >
            <ArrowUpRight className="h-4 w-4 mr-2" />
            Long
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>İşlem Onayı</AlertDialogTitle>
            <AlertDialogDescription>
              {symbol} için {leverage}x kaldıraçlı ${amount} tutarında LONG pozisyon açmak istediğinize emin misiniz?
              <div className="mt-2">
                <div>Giriş Fiyatı: ${formattedPrice}</div>
                <div>Toplam İşlem Değeri: ${totalTradeValue.toFixed(2)}</div>
                {validationResult?.adjustedAmount && (
                  <div className="text-yellow-600 text-sm mt-1">
                    ⚠️ Tutar ${validationResult.adjustedAmount.toFixed(2)} olarak ayarlanacak
                  </div>
                )}
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction onClick={handleLongConfirm}>
              İşlemi Başlat
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant={direction === 'short' ? "destructive" : "outline"}
            onClick={handleShortClick}
            className="flex-1"
            disabled={isDisabled}
          >
            <ArrowDownRight className="h-4 w-4 mr-2" />
            Short
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>İşlem Onayı</AlertDialogTitle>
            <AlertDialogDescription>
              {symbol} için {leverage}x kaldıraçlı ${amount} tutarında SHORT pozisyon açmak istediğinize emin misiniz?
              <div className="mt-2">
                <div>Giriş Fiyatı: ${formattedPrice}</div>
                <div>Toplam İşlem Değeri: ${totalTradeValue.toFixed(2)}</div>
                {validationResult?.adjustedAmount && (
                  <div className="text-yellow-600 text-sm mt-1">
                    ⚠️ Tutar ${validationResult.adjustedAmount.toFixed(2)} olarak ayarlanacak
                  </div>
                )}
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleShortConfirm}
              className="bg-destructive hover:bg-destructive/90"
            >
              İşlemi Başlat
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
