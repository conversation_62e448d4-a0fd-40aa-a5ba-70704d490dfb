import React, { useRef, useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertCircle, ArrowUpRight, ArrowDownRight } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Slider } from "@/components/ui/slider";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Trade } from "@/types/trading";
import { useToast } from "@/hooks/use-toast";

interface TradingPanelProps {
  isAutoTradingEnabled: boolean;
  trades: Trade[];
  tradingStatus: 'inactive' | 'connecting' | 'active' | 'error';
  onToggleAutoTrading: () => void;
  tradingDirection: 'long' | 'short';
  setTradingDirection: (direction: 'long' | 'short') => void;
  tradeUnit: number;
  setTradeUnit: (amount: number) => void;
  profitTarget: number;
  setProfitTarget: (target: number) => void;
  stopLoss: number;
  setStopLoss: (stop: number) => void;
  leverage: number;
  setLeverage: (leverage: number) => void;
  stopNewTrades: boolean;
  setStopNewTrades: (stop: boolean) => void;
}

interface ExtendedTradingPanelProps extends TradingPanelProps {
  emergencyStopLossPercent?: number;
  emergencyStopLossEnabled?: boolean;
}

const TradingPanel: React.FC<ExtendedTradingPanelProps> = ({
  isAutoTradingEnabled,
  trades,
  tradingStatus,
  onToggleAutoTrading,
  tradingDirection,
  setTradingDirection,
  tradeUnit,
  setTradeUnit,
  profitTarget,
  setProfitTarget,
  stopLoss,
  setStopLoss,
  leverage,
  setLeverage,
  stopNewTrades,
  setStopNewTrades,
  emergencyStopLossPercent = 15,
  emergencyStopLossEnabled = true,
}) => {
  const [showWarningDialog, setShowWarningDialog] = useState(false);
  const [stopLossOrdersEnabled, setStopLossOrdersEnabled] = useState(true);
  const warningTriggerRef = useRef(null);

  const getStatusColor = (status: 'inactive' | 'connecting' | 'active' | 'error') => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'connecting': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-muted';
    }
  };

  const getStatusText = (status: 'inactive' | 'connecting' | 'active' | 'error') => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'connecting': return 'Bağlanıyor';
      case 'error': return 'Hata';
      default: return 'Pasif';
    }
  };

  const handleToggleAutoTrading = () => {
    if (isAutoTradingEnabled && trades.some(t => t.status === 'open')) {
      setShowWarningDialog(true);
      return;
    }
    onToggleAutoTrading();
  };

  return (
    <Card className="animate-scale-in">
      <CardHeader className="pb-2 pt-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-md">İşlem Ayarları</CardTitle>
            <CardDescription>
              İşlem parametreleri ve otomatik işlem durumu
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 py-2">
        <div className="space-y-2">
          <Label>İşlem Yönü</Label>
          <div className="flex gap-2">
            <Button
              variant={tradingDirection === 'short' ? "destructive" : "outline"}
              size="sm"
              onClick={() => setTradingDirection('short')}
              className="flex items-center gap-1 flex-1"
              disabled={isAutoTradingEnabled}
            >
              <ArrowDownRight className="h-3.5 w-3.5" />
              Short
            </Button>
          </div>
          {isAutoTradingEnabled && (
            <p className="text-xs text-muted-foreground mt-1">
              Otomatik işlem modunda işlem yönü değiştirilemez
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label>İşlem Tutarı (USDT)</Label>
          <Input
            type="number"
            value={tradeUnit}
            onChange={(e) => setTradeUnit(Number(e.target.value))}
            min={0.1}
            step={0.1}
            disabled={isAutoTradingEnabled}
            placeholder="Min: $0.1"
          />
          {isAutoTradingEnabled && (
            <p className="text-xs text-muted-foreground mt-1">
              Otomatik işlem modunda tutar değiştirilemez
            </p>
          )}
          <p className="text-xs text-muted-foreground">
            Minimum: $0.1, Hassasiyet: $0.1
          </p>
        </div>

        <div className="space-y-2">
          <Label>Kâr Hedefi (%)</Label>
          <div className="flex items-center gap-2">
            <Slider
              value={[profitTarget]}
              onValueChange={(value) => setProfitTarget(value[0])}
              min={0.5}
              max={10}
              step={0.5}
              disabled={isAutoTradingEnabled}
            />
            <span className="font-mono bg-muted p-1 rounded min-w-[40px] text-center">
              {profitTarget}%
            </span>
          </div>
          {isAutoTradingEnabled && (
            <p className="text-xs text-muted-foreground mt-1">
              Otomatik işlem modunda kâr hedefi değiştirilemez
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Stop Loss (%)</Label>
          <div className="flex items-center gap-2">
            <Slider
              value={[stopLoss]}
              onValueChange={(value) => setStopLoss(value[0])}
              min={0.5}
              max={5}
              step={0.5}
              disabled={isAutoTradingEnabled}
            />
            <span className="font-mono bg-muted p-1 rounded min-w-[40px] text-center">
              {stopLoss}%
            </span>
          </div>
          <div className="flex items-center gap-2 mt-2">
            <input
              type="checkbox"
              id="stopLossEnabled"
              checked={stopLossOrdersEnabled}
              onChange={(e) => setStopLossOrdersEnabled(e.target.checked)}
              disabled={isAutoTradingEnabled}
              className="rounded"
            />
            <Label htmlFor="stopLossEnabled" className="text-sm">
              Stop-loss emirlerini otomatik yerleştir
            </Label>
          </div>
          {isAutoTradingEnabled && (
            <p className="text-xs text-muted-foreground mt-1">
              Otomatik işlem modunda stop loss değiştirilemez
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Kaldıraç</Label>
          <div className="flex gap-2">
            {[1, 5, 10, 20, 25].map((leverageOption) => (
              <Button
                key={leverageOption}
                variant={leverage === leverageOption ? "default" : "outline"}
                size="sm"
                onClick={() => setLeverage(leverageOption)}
                className="flex-1"
                disabled={isAutoTradingEnabled}
              >
                {leverageOption}x
              </Button>
            ))}
          </div>
          <p className="text-xs text-muted-foreground">
            Varsayılan kaldıraç: 20x
          </p>
          {isAutoTradingEnabled && (
            <p className="text-xs text-muted-foreground mt-1">
              Otomatik işlem modunda kaldıraç ayarı devre dışıdır
            </p>
          )}
        </div>

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <span ref={warningTriggerRef} className="hidden" />
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Dikkat: Açık İşlemler Var</AlertDialogTitle>
              <AlertDialogDescription>
                Otomatik işlem modunu durdurmak, mevcut açık işlemlerin kapatılmasına neden olacaktır.
                Devam etmek istiyor musunuz?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>İptal</AlertDialogCancel>
              <AlertDialogAction onClick={() => {
                onToggleAutoTrading();
                setShowWarningDialog(false);
              }}>
                Devam Et
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
};

export default TradingPanel;
