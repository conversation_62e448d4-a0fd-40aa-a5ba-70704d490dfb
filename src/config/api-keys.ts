// Binance API anahtarları
import binanceCredentials from './binance-credentials.json';

// HMAC-SHA256 için kullanılan API anahtarları (REST API için)
export const HMAC_API_KEY = process.env.REACT_APP_BINANCE_API_KEY || binanceCredentials.apiKey || '';
export const HMAC_API_SECRET = process.env.REACT_APP_BINANCE_API_SECRET || binanceCredentials.apiSecret || '';

// Ed25519 için kullanılan API anahtarları (WebSocket için)
export const ED25519_API_KEY = process.env.REACT_APP_BINANCE_ED25519_API_KEY || binanceCredentials.ed25519ApiKey || '';
export const PRIVATE_KEY_HEX = binanceCredentials.privateKeyHex || "5bcf9f5c3d43153a32bdfe5790d38a241fd092c67afd49cae933540eec5f4fe0";

// <PERSON>lay erişim için API_KEY'i dışa aktaralım (varsayılan olarak HMAC API kullanılacak)
export const API_KEY = HMAC_API_KEY;
export const API_SECRET = HMAC_API_SECRET;

// NOT: Gerçek bir uygulamada bu değerler .env dosyasından alınmalı ve GitHub'a yüklenmemelidir.
// Bu örnek sadece geliştirme amaçlıdır. 

// Default export ekleyelim
const apiKeys = {
    hmacApiKey: HMAC_API_KEY,
    hmacApiSecret: HMAC_API_SECRET,
    ed25519ApiKey: ED25519_API_KEY,
    privateKeyHex: PRIVATE_KEY_HEX
};

export default apiKeys; 
