// Binance API Konfigürasyon Dosyası

interface BinanceCredentials {
    apiKey: string;
    apiSecret: string;
}

interface BinanceConfig extends BinanceCredentials {
    autoConnect: boolean;
}

// Varsayılan konfigürasyon
const defaultConfig: BinanceConfig = {
    apiKey: '',
    apiSecret: '',
    autoConnect: false,
};

/**
 * Local storage'dan API bilgilerini yükler
 */
export function loadCredentialsFromStorage(): BinanceCredentials {
    try {
        const storedData = localStorage.getItem('binance_credentials');
        if (storedData) {
            const parsed = JSON.parse(storedData);
            return {
                apiKey: parsed.apiKey || '',
                apiSecret: parsed.apiSecret || ''
            };
        }
    } catch (error) {
        console.error('Kimlik bilgileri yüklenirken hata:', error);
    }

    return {
        apiKey: '',
        apiSecret: ''
    };
}

/**
 * API bilgilerini local storage'a kaydeder
 */
export function saveCredentialsToStorage(): void {
    try {
        const config = getBinanceConfig();
        if (config.apiKey && config.apiSecret) {
            localStorage.setItem('binance_credentials', JSON.stringify({
                apiKey: config.apiKey,
                apiSecret: config.apiSecret
            }));
            console.log('💾 API bilgileri localStorage\'a kaydedildi');
        }
    } catch (error) {
        console.error('Kimlik bilgileri kaydedilirken hata:', error);
    }
}

/**
 * Geçerli API bilgilerinin var olup olmadığını kontrol eder
 */
export function hasValidCredentials(): boolean {
    const config = getBinanceConfig();
    return !!(config.apiKey && config.apiSecret && config.apiKey.length > 10 && config.apiSecret.length > 10);
}

/**
 * Binance konfigürasyonunu alır
 * Önce çevre değişkenlerini, sonra localStorage'ı kontrol eder
 */
export function getBinanceConfig(): BinanceConfig {
    // Environment'dan yükle (Production ortamında)
    const envConfig = {
        apiKey: process.env.VITE_BINANCE_API_KEY || '',
        apiSecret: process.env.VITE_BINANCE_API_SECRET || '',
        autoConnect: process.env.VITE_AUTO_CONNECT === 'true',
    };

    // Env variables varsa onları kullan
    if (envConfig.apiKey && envConfig.apiSecret) {
        return envConfig;
    }

    // Local Storage'dan kimlik bilgilerini yükle
    const storedCredentials = loadCredentialsFromStorage();

    // Config nesnesini oluştur ve değerleri birleştir
    return {
        ...defaultConfig,
        ...storedCredentials,
        autoConnect: localStorage.getItem('autoConnect') === 'true',
    };
}

export default {
    getBinanceConfig,
    saveCredentialsToStorage,
    hasValidCredentials,
    loadCredentialsFromStorage
}; 
