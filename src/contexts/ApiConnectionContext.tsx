import * as React from 'react';
import { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { useBinanceConnection, BinanceConnectionStatus } from '@/hooks/useBinanceConnection';
import { BinanceAuthCredentials } from '@/services/binanceService';
import { useToast } from '@/hooks/use-toast';

export interface ApiConnectionContextType {
  connect: (credentials: BinanceAuthCredentials, userName?: string) => void;
  disconnect: () => void;
  isConnected: boolean;
  connectionStatus: BinanceConnectionStatus;
  lastError: string | null;
  userName: string;
  setUserName: (name: string) => void;
  accountBalance: number;
  setAccountBalance: (value: number) => void;
  initialBalance: number;
  setInitialBalance: (value: number) => void;
  isRealTrading: boolean;
}

const ApiConnectionContext = createContext<ApiConnectionContextType | null>(null);

export function ApiConnectionProvider({ children }: { children: ReactNode }): JSX.Element {
  const [accountBalance, setAccountBalance] = useState(100);
  const [initialBalance, setInitialBalance] = useState(100);
  const [isRealTrading, setIsRealTrading] = useState(false);
  const { toast } = useToast();

  const {
    connect: wsConnect,
    disconnect: wsDisconnect,
    isConnected,
    connectionStatus,
    lastError,
    userName,
    setUserName,
  } = useBinanceConnection({ apiKey: '', autoConnect: false });

  const connect = (credentials: BinanceAuthCredentials, displayName?: string) => {
    wsConnect(credentials, displayName);
  };

  const disconnect = () => {
    wsDisconnect();
  };

  // Auto-connect with saved credentials on mount
  React.useEffect(() => {
    // Otomatik bağlanma girişimini takip etmek için bir session değişkeni kullan
    const autoConnectAttempted = sessionStorage.getItem('autoConnectAttempted');

    if (!autoConnectAttempted) {
      sessionStorage.setItem('autoConnectAttempted', 'true');

      const saved = localStorage.getItem('binance_credentials');
      if (saved) {
        try {
          const creds = JSON.parse(saved) as BinanceAuthCredentials;
          wsConnect(creds);
          console.log('🔌 Auto-connecting with saved credentials (ONE TIME)');
        } catch (e) {
          console.error('❌ Auto-connect failed: invalid saved credentials', e);
        }
      }
    } else {
      console.log('🔄 Auto-connect already attempted this session, skipping');
    }
  }, []);

  // Update real trading status
  useEffect(() => {
    setIsRealTrading(isConnected);
  }, [isConnected]);

  const value = {
    connect,
    disconnect,
    isConnected,
    connectionStatus,
    lastError,
    userName,
    setUserName,
    accountBalance,
    setAccountBalance,
    initialBalance,
    setInitialBalance,
    isRealTrading,
  };

  return (
    <ApiConnectionContext.Provider value={value}>
      {children}
    </ApiConnectionContext.Provider>
  );
}

export const useApiConnection = () => {
  const context = useContext(ApiConnectionContext);
  if (context === null) {
    throw new Error('useApiConnection must be used within an ApiConnectionProvider');
  }
  return context;
};
