import { useState, useCallback, useRef, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";
import { Trade } from '@/types/trading';
import { validateTradeParameters } from '@/utils/tradingRules';
import { calculateCommissionFee, canOpenTrade } from '@/utils/tradingCalculations';
import {
    BinanceAuthCredentials,
    RealOrderParams,
    BinanceOrderResponse,
    BinancePosition
} from '@/services/binanceService';
import { getWebSocketAPI } from '@/services/binanceWebSocketService';

export interface RealTradingConfig {
    credentials: BinanceAuthCredentials;
    leverage: number;
    maxOpenPositions: number;
    minVolumeForTrade: number;
    emergencyStopLoss: number;
    maxLeverage: number;
    restrictLowVolumeCoins: boolean;
    stopLossPercent?: number;
    takeProfitPercent?: number;
}

export interface RealTradeParams {
    symbol: string;
    direction: 'long' | 'short';
    amount: number;
    currentPrice: number;
    volume24h: number;
    isTPOrSL?: boolean;
}

const useRealTrading = (config: RealTradingConfig) => {
    const [isPlacingOrder, setIsPlacingOrder] = useState(false);
    const [positions, setPositions] = useState<BinancePosition[]>([]);
    const { toast } = useToast();

    // 🚨 DUPLICATE TRADE PREVENTION - ACTIVE ORDERS TRACKING
    const activeOrdersRef = useRef<Set<string>>(new Set());
    const lastOrderTimeRef = useRef<Record<string, number>>({});

    // WebSocket API instance'ını al (yeniden kullanılabilir)
    const wsAPI = getWebSocketAPI(config.credentials);

    // 🔒 DUPLICATE TRADE PREVENTION FUNCTION - GELİŞTİRİLMİŞ VERSİYON
    const isDuplicateTradeAttempt = (symbol: string, isTPOrSL: boolean = false): boolean => {
        // TP/SL işlemleri için duplicate kontrolünü atla
        if (isTPOrSL) {
            console.log(`🎯 TP/SL EMRİ: ${symbol} - Duplicate kontrolü atlanıyor`);
            return false;
        }

        const now = Date.now();
        const lastOrderTime = lastOrderTimeRef.current[symbol] || 0;
        const timeDiff = now - lastOrderTime;

        console.log(`🔍 DUPLICATE CHECK for ${symbol}:`, {
            isActive: activeOrdersRef.current.has(symbol),
            isProcessing: timeDiff < 3000,
            lastOrderTime: lastOrderTime ? new Date(lastOrderTime).toISOString() : 'never',
            timeDiff: `${timeDiff}ms`,
            activeOrders: Array.from(activeOrdersRef.current),
            minInterval: '3000ms'
        });

        // Aynı symbol için 3 saniye içinde ikinci emir engelle (daha kısa süre)
        if (timeDiff < 3000) {
            console.log(`🚫 DUPLICATE PREVENTION: ${symbol} için ${timeDiff}ms önce emir verildi, engellendi`);
            return true;
        }

        // Aktif emir kontrol et - Sadece aynı sembol için engelle
        if (activeOrdersRef.current.has(symbol)) {
            console.log(`🚫 DUPLICATE PREVENTION: ${symbol} için işlem süreci devam ediyor`);
            return true;
        }

        // Açık pozisyonları kontrol et - Aynı sembol için açık pozisyon varsa engelle
        const openPositions = positionsRef.current.filter(p =>
            p.symbol === symbol &&
            p.positionAmt !== 0 &&
            !p.isClosing
        );

        if (openPositions.length > 0) {
            console.log(`🚫 DUPLICATE PREVENTION: ${symbol} için zaten açık pozisyon var:`, openPositions);
            return true;
        }

        return false;
    };

    // Aktif emirleri belirli aralıklarla temizleme - 5 dakika sonra otomatik temizle
    useEffect(() => {
        const cleanupInterval = setInterval(() => {
            const now = Date.now();
            // 5 dakikadan eski tüm emirleri temizle
            const oldOrders = Object.entries(lastOrderTimeRef.current)
                .filter(([_, timestamp]) => now - timestamp > 5 * 60 * 1000)
                .map(([symbol]) => symbol);

            if (oldOrders.length > 0) {
                console.log(`🧹 Eski emirler temizleniyor (5 dakikadan eski):`, oldOrders);

                for (const symbol of oldOrders) {
                    activeOrdersRef.current.delete(symbol);
                    delete lastOrderTimeRef.current[symbol];
                }
            }
        }, 30000); // 30 saniyede bir kontrol et

        return () => clearInterval(cleanupInterval);
    }, []);

    const placeRealTrade = useCallback(async (params: RealTradeParams) => {
        console.log('🚀 GERÇEK İŞLEM AÇILIYOR:', params);

        // 🔒 DUPLICATE TRADE PREVENTION - TP/SL emirleri için ayrı kontrol
        if (isDuplicateTradeAttempt(params.symbol, params.isTPOrSL)) {
            toast({
                title: "Duplicate İşlem Engellendi",
                description: `${params.symbol} için zaten aktif bir işlem var veya çok yakın zamanda işlem yapıldı.`,
                variant: "destructive",
            });
            return {
                success: false,
                error: "Duplicate trade attempt blocked",
                data: null
            };
        }

        // Açık pozisyonları tekrar kontrol et (güvenlik için)
        if (!params.isTPOrSL) {
            // Mevcut pozisyonları güncelle
            await refreshPositions();

            // Aynı sembol için açık pozisyon var mı kontrol et
            const existingPosition = positionsRef.current.find(p =>
                p.symbol === params.symbol &&
                p.positionAmt !== 0 &&
                !p.isClosing
            );

            if (existingPosition) {
                console.log(`🚫 DUPLICATE CHECK (ikinci kontrol): ${params.symbol} için zaten açık pozisyon var:`, existingPosition);
                toast({
                    title: "Duplicate İşlem Engellendi",
                    description: `${params.symbol} için zaten açık bir pozisyon bulunuyor.`,
                    variant: "destructive",
                });
                return {
                    success: false,
                    error: "Duplicate position exists",
                    data: null
                };
            }
        }

        // TP/SL emirleri için özel log
        if (params.isTPOrSL) {
            console.log(`🎯 TP/SL EMRİ: ${params.symbol} - Duplicate kontrolü atlandı`);
        }

        // Maximum pozisyon sayısı kontrolü - Her sembol için ayrı kontrol edilecek
        const currentOpenPositions = Array.from(activeOrdersRef.current).filter(s => s === params.symbol).length;

        if (!params.isTPOrSL && currentOpenPositions >= config.maxOpenPositions) {
            toast({
                title: "Maximum İşlem Limiti",
                description: `${params.symbol} için maksimum açık işlem sayısına ulaştınız (${config.maxOpenPositions})`,
                variant: "destructive",
            });
            return {
                success: false,
                error: `Maximum open positions limit reached for ${params.symbol}`,
                data: null
            };
        }

        setIsPlacingOrder(true);

        try {
            // İşlemi aktif olarak işaretle - Sadece normal işlemler için
            if (!params.isTPOrSL) {
                console.log(`🔒 Marking ${params.symbol} as active before validation (normal trade)`);
                activeOrdersRef.current.add(params.symbol);
                lastOrderTimeRef.current[params.symbol] = Date.now();
            } else {
                console.log(`🚫 TP/SL işlemi - Aktif işlem listesine eklenmedi: ${params.symbol}`);
            }

            // Trade parametrelerini doğrula
            const validation = validateTradeParameters(params.symbol, params.amount, config.leverage, params.currentPrice);
            if (!validation.isValid) {
                throw new Error(validation.reason || 'Geçersiz işlem parametreleri');
            }

            // 24 saatlik hacim kontrolü - TP/SL için daha esnek olabilir
            const volume24h = params.volume24h;
            const volumeInMillions = volume24h / 1000000;

            // Hacim kontrolü (TP/SL için atla veya daha esnek kural uygula)
            if (!params.isTPOrSL && volumeInMillions < config.minVolumeForTrade) {
                throw new Error(`İşlem hacmi (${volumeInMillions.toFixed(2)}M$) minimum değerin altında (${config.minVolumeForTrade}M$)`);
            }

            // Pozisyon miktarını hesapla (leverage dahil)
            const totalPositionValue = params.amount * config.leverage;
            const quantity = parseFloat((totalPositionValue / params.currentPrice).toFixed(8));

            console.log('📊 İşlem hesaplamaları:', {
                symbol: params.symbol,
                direction: params.direction,
                amount: params.amount,
                leverage: config.leverage,
                currentPrice: params.currentPrice,
                quantity,
                isTPOrSL: params.isTPOrSL || false
            });

            // WebSocket bağlantısı kontrolü
            if (!wsAPI.isConnected || !wsAPI.isAuthenticated) {
                console.log('🔄 WebSocket bağlantısı kontrol ediliyor...');

                try {
                    // WebSocket connection timeout ile dene
                    const connectionPromise = wsAPI.connect();
                    const timeoutPromise = new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('WebSocket connection timeout')), 10000)
                    );

                    await Promise.race([connectionPromise, timeoutPromise]);

                    // Yeniden kontrol
                    if (!wsAPI.isConnected || !wsAPI.isAuthenticated) {
                        console.warn('⚠️ WebSocket kimlik doğrulama başarısız, REST API kullanılacak');
                        // Doğrudan REST API kullanarak işleme devam et - hata fırlatma
                    }

                    console.log('✅ WebSocket bağlantısı başarılı');
                } catch (wsError) {
                    console.warn('⚠️ WebSocket bağlantısı başarısız, REST API kullanılacak:', wsError);
                    // Doğrudan REST API kullanarak işleme devam et - hata fırlatma
                }
            }

            // Kaldıraç ayarla (WebSocket API)
            try {
                await wsAPI.setLeverage(params.symbol, config.leverage);
                console.log(`✅ Kaldıraç ayarlandı: ${params.symbol} = ${config.leverage}x`);
            } catch (leverageError) {
                console.warn('⚠️ Kaldıraç ayarlanamadı (zaten ayarlı olabilir):', leverageError);
                // Kaldıraç hatası işlemi engellemez, devam et
            }

            // WebSocket API ile market emri ver
            let orderResult: BinanceOrderResponse;

            if (params.direction === 'short') {
                // SHORT pozisyon aç
                orderResult = await wsAPI.openShortPosition(params.symbol, quantity.toString(), config.leverage);
                console.log(`✅ SHORT pozisyon açıldı (WebSocket) - ${params.isTPOrSL ? 'TP/SL' : 'Normal'}:`, orderResult);
            } else {
                // LONG pozisyon aç
                orderResult = await wsAPI.openLongPosition(params.symbol, quantity.toString(), config.leverage);
                console.log(`✅ LONG pozisyon açıldı (WebSocket) - ${params.isTPOrSL ? 'TP/SL' : 'Normal'}:`, orderResult);
            }

            // İşlem başarılı oldu - Aktif listeden temizleme işlemi burada yapılsın
            if (!params.isTPOrSL) {
                console.log(`🧹 İşlem başarılı - ${params.symbol} aktif listeden kaldırılıyor`);
                activeOrdersRef.current.delete(params.symbol);
            }

            // Başarılı sonuç döndür
            console.log(`✅ Trade completed successfully for ${params.symbol} - ${params.isTPOrSL ? 'TP/SL' : 'Normal'}`);

            // Normal işlem başarılı ise ve TP/SL değilse, TP/SL emirlerini otomatik ekle
            if (!params.isTPOrSL && config.takeProfitPercent && config.stopLossPercent) {
                try {
                    console.log(`🎯 ${params.symbol} için otomatik TP/SL emirleri ekleniyor...`);

                    // Take Profit emri
                    const tpPrice = params.direction === 'long'
                        ? params.currentPrice * (1 + config.takeProfitPercent / 100 / config.leverage)
                        : params.currentPrice * (1 - config.takeProfitPercent / 100 / config.leverage);

                    // Stop Loss emri
                    const slPrice = params.direction === 'long'
                        ? params.currentPrice * (1 - config.stopLossPercent / 100 / config.leverage)
                        : params.currentPrice * (1 + config.stopLossPercent / 100 / config.leverage);

                    console.log(`📊 TP/SL fiyatları hesaplandı:`, {
                        symbol: params.symbol,
                        direction: params.direction,
                        currentPrice: params.currentPrice,
                        tpPrice: tpPrice.toFixed(8),
                        slPrice: slPrice.toFixed(8)
                    });

                    // TP emrini REST API ile gönder
                    const tpParams: RealTradeParams = {
                        ...params,
                        isTPOrSL: true
                    };

                    // SL emrini REST API ile gönder
                    const slParams: RealTradeParams = {
                        ...params,
                        isTPOrSL: true
                    };

                    // Asenkron olarak gönder
                    setTimeout(async () => {
                        try {
                            // TP ve SL emirlerini burada özel fonksiyonlarla gönder
                            console.log(`🎯 ${params.symbol} için TP/SL emirleri gönderiliyor...`);

                            // TP ve SL için işlem yönünü belirle (LONG için SELL, SHORT için BUY)
                            const closeSide = params.direction === 'long' ? 'SELL' : 'BUY';

                            // Quantity hesapla
                            const totalPositionValue = params.amount * config.leverage;
                            const quantity = parseFloat((totalPositionValue / params.currentPrice).toFixed(8));

                            // Take Profit emrini gönder
                            const tpResult = await wsAPI.placeTakeProfitOrder(
                                params.symbol,
                                closeSide,
                                quantity.toString(),
                                tpPrice.toFixed(8)
                            );

                            console.log(`✅ Take Profit emri başarıyla gönderildi:`, tpResult);

                            // Stop Loss emrini gönder
                            const slResult = await wsAPI.placeStopLossOrder(
                                params.symbol,
                                closeSide,
                                quantity.toString(),
                                slPrice.toFixed(8)
                            );

                            console.log(`✅ Stop Loss emri başarıyla gönderildi:`, slResult);

                            // Ekranda bildir
                            toast({
                                title: "TP/SL Emirleri Eklendi",
                                description: `${params.symbol} için otomatik TP/SL emirleri başarıyla eklendi.`,
                                variant: "default",
                            });
                        } catch (error) {
                            console.error(`❌ TP/SL emirleri gönderilirken hata: ${error instanceof Error ? error.message : error}`);

                            toast({
                                title: "TP/SL Emirleri Eklenemedi",
                                description: `Hata: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
                                variant: "destructive",
                            });
                        }
                    }, 500); // Asıl işlemden 500ms sonra gönder
                } catch (tpslError) {
                    console.error(`❌ TP/SL emirleri oluşturma hatası: ${tpslError}`);
                }
            }

            return {
                success: true,
                orderId: orderResult.orderId,
                executedQty: orderResult.executedQty,
                executedPrice: orderResult.avgPrice || orderResult.price,
                data: orderResult
            };

        } catch (error) {
            console.error('❌ GERÇEK İŞLEM HATASI:', error);

            // Başarısız işlem için hem aktif listeyi hem timestamp'i temizle - Sadece normal işlemler için
            if (!params.isTPOrSL) {
                console.log(`🧹 Clearing failed order from active list for ${params.symbol} (normal trade)`);
                activeOrdersRef.current.delete(params.symbol);

                if (lastOrderTimeRef.current[params.symbol]) {
                    console.log(`🧹 Clearing failed order timestamp for ${params.symbol} (normal trade)`);
                    delete lastOrderTimeRef.current[params.symbol];
                }
            }

            toast({
                title: "İşlem Hatası",
                description: `Hata: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
                variant: "destructive",
            });

            return {
                success: false,
                error: error instanceof Error ? error.message : JSON.stringify(error),
                data: null
            };
        } finally {
            setIsPlacingOrder(false);
        }
    }, [wsAPI, toast, config.leverage, config.minVolumeForTrade, config.takeProfitPercent, config.stopLossPercent]);

    const closeRealTrade = useCallback(async (trade: Trade) => {
        console.log('🔴 GERÇEK İŞLEM KAPATILIYOR:', trade);

        setIsPlacingOrder(true);

        try {
            // WebSocket bağlantısı kontrolü
            if (!wsAPI.isConnected || !wsAPI.isAuthenticated) {
                // Bağlantıyı yeniden dene
                await wsAPI.connect();

                if (!wsAPI.isConnected || !wsAPI.isAuthenticated) {
                    throw new Error('WebSocket bağlantısı kurulamadı');
                }
            }

            // Pozisyon yönünü belirle
            const positionSide = trade.direction === 'long' ? 'LONG' : 'SHORT';

            // WebSocket ile pozisyonu kapat
            const closeResult = await wsAPI.closePosition(trade.symbol, positionSide);
            console.log('✅ WebSocket ile pozisyon kapatıldı:', closeResult);

            return {
                success: true,
                orderId: closeResult.orderId,
                data: closeResult
            };

        } catch (error) {
            console.error('❌ POZISYON KAPATMA HATASI:', error);

            toast({
                title: "Pozisyon Kapatma Hatası",
                description: `Hata: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
                variant: "destructive",
            });

            return {
                success: false,
                error: error instanceof Error ? error.message : JSON.stringify(error)
            };
        } finally {
            setIsPlacingOrder(false);
        }
    }, [wsAPI, toast]);

    const refreshPositions = useCallback(async () => {
        try {
            // WebSocket API ile pozisyonları al
            if (wsAPI.isConnected && wsAPI.isAuthenticated) {
                const currentPositions = await wsAPI.getPositions();
                setPositions(currentPositions);
                console.log('✅ Pozisyonlar güncellendi (WebSocket):', currentPositions.length, 'pozisyon');
            }
        } catch (error) {
            console.error('❌ Pozisyonlar güncellenirken hata:', error);
            toast({
                title: "Pozisyon Güncelleme Hatası",
                description: `Hata: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
                variant: "destructive",
            });
        }
    }, [wsAPI, toast]);

    const validateRealTrade = useCallback((params: RealTradeParams) => {
        const validation = validateTradeParameters(params.symbol, params.amount, config.leverage, params.currentPrice);
        return validation;
    }, [config.leverage]);

    // 🧹 Cleanup function for active orders
    const clearActiveOrders = useCallback(() => {
        console.log('🧹 Clearing all active orders:', {
            activeOrders: Array.from(activeOrdersRef.current),
            lastOrderTimes: Object.keys(lastOrderTimeRef.current)
        });
        activeOrdersRef.current.clear();
        lastOrderTimeRef.current = {};
        console.log('✅ All active orders cleared');
    }, []);

    return {
        placeRealTrade,
        closeRealTrade,
        refreshPositions,
        validateRealTrade,
        isPlacingOrder,
        positions,
        clearActiveOrders
    };
};

export default useRealTrading;
