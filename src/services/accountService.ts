import { HMAC_API_KEY, HMAC_SECRET_KEY } from '../config/api';
import { createSignature } from '../utils/signature';
import { AccountInfo, AccountInfoCallback, AssetInfo, PositionInfo } from '../types/accountTypes';
import { onWsEvent } from './websocket';
import { setMode } from '../store/modeStore';

// Mevcut hesap bilgileri
let currentAccountInfo: AccountInfo | null = null;

// Hesap bilgilerini al
export async function getAccountInfo(
    callback: (accountInfo: AccountInfo | null, error?: any) => void
): Promise<void> {
    try {
        console.log('💰 Hesap bilgileri alınıyor...');

        // Zaman damgası oluştur
        const timestamp = Date.now();

        // İmza oluştur
        const queryString = `timestamp=${timestamp}`;
        const signature = createSignature(queryString, HMAC_SECRET_KEY);

        // Hesap bilgilerini al
        const response = await fetch(`https://fapi.binance.com/fapi/v2/account?${queryString}&signature=${signature}`, {
            method: 'GET',
            headers: {
                'X-MBX-APIKEY': HMAC_API_KEY,
            },
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Hesap bilgileri alınamadı:', errorText);
            callback(null, errorText);

            return;
        }

        const accountData = await response.json();
        console.log('✅ Hesap bilgileri başarıyla alındı');

        // REAL modu aktifleştir
        setMode('REAL');

        // Hesap bilgilerini güncelle
        currentAccountInfo = accountData;

        callback(accountData);
    } catch (error) {
        console.error('❌ Hesap bilgileri alınırken hata:', error);
        callback(null, error);

        // Hata durumunda demo hesap oluştur
        console.log('⚠️ Hata nedeniyle demo modu aktifleştiriliyor...');
        setMode('MANUAL');
    }
}

// Hesap güncellemelerini dinle
export function listenToAccountUpdates(callback: (data: any) => void): () => void {
    return onWsEvent('accountUpdate', callback);
}

// WebSocket üzerinden gelen hesap güncellemelerini işle
export function processAccountUpdate(data: any): void {
    console.log('🔄 Hesap güncellemesi alındı:', data);

    if (!currentAccountInfo) {
        console.warn('⚠️ Hesap bilgileri henüz alınmadı, güncelleme atlanıyor');
        return;
    }

    // Hesap güncellemelerini işle
    if (data.a && data.a.B) {
        // Bakiye güncellemeleri
        data.a.B.forEach((balanceUpdate: any) => {
            const asset = balanceUpdate.a;
            const walletBalance = parseFloat(balanceUpdate.wb);

            // Mevcut asset'i bul veya yeni oluştur
            const assetIndex = currentAccountInfo!.assets.findIndex(a => a.asset === asset);

            if (assetIndex >= 0) {
                // Mevcut asset'i güncelle
                currentAccountInfo!.assets[assetIndex].walletBalance = walletBalance.toString();
                currentAccountInfo!.assets[assetIndex].availableBalance = walletBalance.toString();
                currentAccountInfo!.assets[assetIndex].updateTime = Date.now();
            } else {
                // Yeni asset ekle
                const newAsset: AssetInfo = {
                    asset,
                    walletBalance: walletBalance.toString(),
                    unrealizedProfit: "0",
                    marginBalance: walletBalance.toString(),
                    maintMargin: "0",
                    initialMargin: "0",
                    positionInitialMargin: "0",
                    openOrderInitialMargin: "0",
                    crossWalletBalance: walletBalance.toString(),
                    crossUnPnl: "0",
                    availableBalance: walletBalance.toString(),
                    maxWithdrawAmount: walletBalance.toString(),
                    marginAvailable: true,
                    updateTime: Date.now()
                };
                currentAccountInfo!.assets.push(newAsset);
            }
        });

        // Toplam bakiyeyi güncelle
        updateTotalBalances();
    }

    // Pozisyon güncellemeleri
    if (data.a && data.a.P) {
        data.a.P.forEach((posUpdate: any) => {
            const symbol = posUpdate.s;
            const positionAmt = parseFloat(posUpdate.pa);
            const entryPrice = parseFloat(posUpdate.ep);
            const unrealizedProfit = parseFloat(posUpdate.up);

            // Mevcut pozisyonu bul veya yeni oluştur
            const posIndex = currentAccountInfo!.positions.findIndex(p => p.symbol === symbol);

            if (posIndex >= 0) {
                // Mevcut pozisyonu güncelle
                currentAccountInfo!.positions[posIndex].positionAmt = positionAmt.toString();
                currentAccountInfo!.positions[posIndex].entryPrice = entryPrice.toString();
                currentAccountInfo!.positions[posIndex].unrealizedProfit = unrealizedProfit.toString();
                currentAccountInfo!.positions[posIndex].updateTime = Date.now();
            } else if (positionAmt !== 0) {
                // Yeni pozisyon ekle (sadece 0 olmayan pozisyonları)
                const newPosition: PositionInfo = {
                    symbol,
                    positionSide: 'BOTH',
                    positionAmt: positionAmt.toString(),
                    entryPrice: entryPrice.toString(),
                    unrealizedProfit: unrealizedProfit.toString(),
                    leverage: "1",
                    isolated: false,
                    maxNotional: "0",
                    notional: (positionAmt * entryPrice).toString(),
                    isolatedWallet: "0",
                    initialMargin: "0",
                    maintMargin: "0",
                    positionInitialMargin: "0",
                    openOrderInitialMargin: "0",
                    bidNotional: "0",
                    askNotional: "0",
                    updateTime: Date.now()
                };
                currentAccountInfo!.positions.push(newPosition);
            }
        });

        // Toplam unrealized PnL'yi güncelle
        updateTotalUnrealizedProfit();
    }
}

// Toplam bakiyeleri güncelle
function updateTotalBalances(): void {
    if (!currentAccountInfo) return;

    let totalWalletBalance = 0;
    let totalAvailableBalance = 0;

    currentAccountInfo.assets.forEach(asset => {
        if (asset.asset === 'USDT') {
            totalWalletBalance += parseFloat(asset.walletBalance);
            totalAvailableBalance += parseFloat(asset.availableBalance);
        }
    });

    currentAccountInfo.totalWalletBalance = totalWalletBalance.toString();
    currentAccountInfo.availableBalance = totalAvailableBalance.toString();
    currentAccountInfo.updateTime = Date.now();

    console.log('💰 Güncellenmiş bakiye bilgileri:', {
        totalWalletBalance,
        availableBalance: totalAvailableBalance
    });
}

// Toplam unrealized profit'i güncelle
function updateTotalUnrealizedProfit(): void {
    if (!currentAccountInfo) return;

    let totalUnrealizedProfit = 0;

    currentAccountInfo.positions.forEach(position => {
        totalUnrealizedProfit += parseFloat(position.unrealizedProfit);
    });

    currentAccountInfo.totalUnrealizedProfit = totalUnrealizedProfit.toString();
    currentAccountInfo.updateTime = Date.now();

    console.log('💰 Güncellenmiş unrealized profit:', totalUnrealizedProfit);
}

// Güncel hesap bilgilerini al
export function getCurrentAccountInfo(): AccountInfo | null {
    return currentAccountInfo;
} 
