import { BinanceWebSocketAPI } from './binanceWebSocketService';
import binanceApi from './binanceApi';
import type { BinanceRestAPI } from './binanceApi';
import { calculateTakeProfitPrice, calculateStopLossPrice, calculateTradeQuantity } from '../utils/tradingCalculations';
import { getMode, isRealMode } from '../store/modeStore';
import credentials from '../config/binance-credentials.json';

// Debug bilgisi
console.log('🔧 Advanced Trading Service yükleniyor...');

// Trading konfigürasyonu
export interface AdvancedTradingConfig {
    tradeAmount: number; // İşlem miktarı (USDT)
    leverage: number; // Kaldıraç
    takeProfitPercent: number; // Kâr alma yüzdesi
    stopLossPercent: number; // Zarar durdurma yüzdesi
    maxOpenPositions: number; // Maksimum açık pozisyon sayısı
    minVolumeForTrade: number; // Minimum işlem hacmi (milyon USDT)
}

// Pozisyon yönetimi için tip
export interface ManagedPosition {
    symbol: string;
    orderId: number;
    side: 'LONG' | 'SHORT';
    quantity: number;
    entryPrice: number;
    takeProfitOrderId?: number;
    stopLossOrderId?: number;
    isIsolated: boolean;
    leverage: number;
    createdAt: Date;
}

// Servis durumu
let wsAPI: BinanceWebSocketAPI | null = null;
let restAPI: BinanceRestAPI | null = null;
let managedPositions: Map<string, ManagedPosition> = new Map();
let tradingConfig: AdvancedTradingConfig | null = null;

/**
 * Gelişmiş trading servisini başlat
 */
export async function initAdvancedTradingService(
    credentials: { apiKey: string; secretKey: string },
    config: AdvancedTradingConfig
): Promise<boolean> {
    try {
        console.log('🚀 Advanced Trading Service başlatılıyor...');

        // Konfigürasyonu kaydet
        tradingConfig = config;

        // WebSocket API'yi başlat (sadece veri almak için)
        wsAPI = new BinanceWebSocketAPI({
            apiKey: credentials.apiKey,
            apiSecret: credentials.secretKey
        });
        const connected = await wsAPI.connect();

        if (!connected) {
            console.error('❌ WebSocket bağlantısı kurulamadı');
            return false;
        }

        // REST API'yi başlat (işlem yapmak için)
        restAPI = binanceApi.getBinanceRestAPI({
            apiKey: credentials.apiKey,
            apiSecret: credentials.secretKey
        });

        console.log('✅ Advanced Trading Service başlatıldı');
        return true;

    } catch (error) {
        console.error('❌ Advanced Trading Service başlatılırken hata:', error);
        return false;
    }
}

/**
 * Short pozisyon aç (ISOLATED modda)
 */
export async function openShortPosition(
    symbol: string,
    currentPrice: number,
    config: AdvancedTradingConfig
): Promise<{ success: boolean; orderId?: number; error?: string }> {
    try {
        if (!restAPI) {
            return { success: false, error: 'REST API başlatılmadı' };
        }

        console.log(`📉 ${symbol} için SHORT pozisyon açılıyor...`);

        // 1. ISOLATED mod ayarla
        try {
            await restAPI.setMarginType(symbol, 'ISOLATED');
            console.log(`✅ ${symbol} ISOLATED moda ayarlandı`);
        } catch (error) {
            // Eğer zaten ISOLATED modda ise hata verebilir, bu normal
            console.log(`⚠️ ${symbol} margin tipi ayarlanamadı (muhtemelen zaten ISOLATED):`, error);
        }

        // 2. Kaldıraç ayarla
        try {
            await restAPI.setLeverage(symbol, config.leverage);
            console.log(`✅ ${symbol} kaldıraç ${config.leverage}x olarak ayarlandı`);
        } catch (error) {
            console.error(`❌ ${symbol} kaldıraç ayarlanamadı:`, error);
            return { success: false, error: `Kaldıraç ayarlama hatası: ${error}` };
        }

        // 3. Pozisyon büyüklüğünü hesapla
        const quantity = calculateTradeQuantity(symbol, currentPrice, config.tradeAmount, config.leverage);

        if (quantity <= 0) {
            console.error(`❌ ${symbol} için geçersiz miktar hesaplandı: ${quantity}`);
            return { success: false, error: 'Geçersiz işlem miktarı' };
        }

        console.log(`📊 Pozisyon detayları:`, {
            symbol,
            currentPrice,
            tradeAmount: config.tradeAmount,
            leverage: config.leverage,
            totalPositionValue: config.tradeAmount * config.leverage,
            quantity
        });

        // 4. SHORT market emri oluştur
        const orderResult = await restAPI.placeMarketOrder(
            symbol,
            'SELL',
            quantity.toString()
        );

        if (!orderResult || orderResult.status !== 'FILLED') {
            console.error(`❌ ${symbol} SHORT emri oluşturulamadı:`, orderResult);
            return { success: false, error: 'Emir oluşturulamadı' };
        }

        const orderId = orderResult.orderId;
        const entryPrice = parseFloat(orderResult.avgPrice || currentPrice.toString());

        console.log(`✅ ${symbol} SHORT pozisyon açıldı - ID: ${orderId}, Giriş: $${entryPrice}`);

        // 5. Take Profit ve Stop Loss emirlerini ayarla
        const tpSlResult = await setupTakeProfitAndStopLoss(
            symbol,
            'SHORT',
            quantity,
            entryPrice,
            config
        );

        // 6. Pozisyonu yönetilen pozisyonlara ekle
        const managedPosition: ManagedPosition = {
            symbol,
            orderId,
            side: 'SHORT',
            quantity,
            entryPrice,
            takeProfitOrderId: tpSlResult.takeProfitOrderId,
            stopLossOrderId: tpSlResult.stopLossOrderId,
            isIsolated: true,
            leverage: config.leverage,
            createdAt: new Date()
        };

        managedPositions.set(`${symbol}_SHORT`, managedPosition);

        // 7. Hesap bilgilerini güncelle
        await updateAccountInfo();

        return { success: true, orderId };

    } catch (error) {
        console.error(`❌ ${symbol} SHORT pozisyon açılırken hata:`, error);
        return { success: false, error: `Pozisyon açma hatası: ${error}` };
    }
}

/**
 * Take Profit ve Stop Loss emirlerini ayarla
 */
async function setupTakeProfitAndStopLoss(
    symbol: string,
    side: 'LONG' | 'SHORT',
    quantity: number,
    entryPrice: number,
    config: AdvancedTradingConfig
): Promise<{ takeProfitOrderId?: number; stopLossOrderId?: number }> {
    try {
        if (!restAPI) {
            return {};
        }

        console.log(`🎯 ${symbol} için TP/SL emirleri ayarlanıyor...`);

        // Take Profit fiyatını hesapla
        const takeProfitPrice = calculateTakeProfitPrice(
            entryPrice,
            side === 'LONG' ? 'long' : 'short',
            config.takeProfitPercent,
            config.leverage
        );

        // Stop Loss fiyatını hesapla
        const stopLossPrice = calculateStopLossPrice(
            entryPrice,
            side === 'LONG' ? 'long' : 'short',
            config.stopLossPercent,
            config.leverage
        );

        console.log(`📊 TP/SL seviyeler:`, {
            symbol,
            side,
            entryPrice,
            takeProfitPrice,
            stopLossPrice,
            tpPercent: config.takeProfitPercent,
            slPercent: config.stopLossPercent
        });

        let takeProfitOrderId: number | undefined;
        let stopLossOrderId: number | undefined;

        // Take Profit emri oluştur
        try {
            const tpOrder = await restAPI.placeLimitOrder(
                symbol,
                side === 'LONG' ? 'SELL' : 'BUY',
                quantity.toString(),
                takeProfitPrice.toString(),
                'GTC',
                true // reduceOnly
            );

            if (tpOrder && tpOrder.orderId) {
                takeProfitOrderId = tpOrder.orderId;
                console.log(`✅ Take Profit emri oluşturuldu - ID: ${takeProfitOrderId}, Fiyat: $${takeProfitPrice}`);
            }
        } catch (error) {
            console.error(`❌ Take Profit emri oluşturulamadı:`, error);
        }

        // Stop Loss emri oluştur
        try {
            const slOrder = await restAPI.placeStopLossOrder(
                symbol,
                side === 'LONG' ? 'SELL' : 'BUY',
                quantity.toString(),
                stopLossPrice.toString()
            );

            if (slOrder && slOrder.orderId) {
                stopLossOrderId = slOrder.orderId;
                console.log(`✅ Stop Loss emri oluşturuldu - ID: ${stopLossOrderId}, Fiyat: $${stopLossPrice}`);
            }
        } catch (error) {
            console.error(`❌ Stop Loss emri oluşturulamadı:`, error);
        }

        return { takeProfitOrderId, stopLossOrderId };

    } catch (error) {
        console.error(`❌ TP/SL emirleri ayarlanırken hata:`, error);
        return {};
    }
}

/**
 * Hesap bilgilerini güncelle
 */
async function updateAccountInfo(): Promise<void> {
    try {
        if (!restAPI) return;

        console.log('💰 Hesap bilgileri güncelleniyor...');

        // Bakiye ve pozisyon bilgilerini getir
        const accountInfo = await restAPI.getAccountInfo();
        const positions = await restAPI.getPositions();

        // İşlem için kullanılabilir hesap bakiyesini hesapla
        const availableBalance = parseFloat(accountInfo.availableBalance || '0');

        console.log(`💵 Kullanılabilir bakiye: $${availableBalance}`);
        console.log(`📈 Açık pozisyonlar: ${positions.length}`);

        // Mevcut açık pozisyonları managedPositions'a ekle (henüz eklenmediyse)
        for (const pos of positions) {
            if (!pos.symbol) continue;

            const positionSize = parseFloat(pos.positionAmt || pos.positionAmount || '0');
            if (Math.abs(positionSize) <= 0) continue;

            const side = positionSize > 0 ? 'LONG' : 'SHORT';
            const key = `${pos.symbol}_${side}`;

            // Eğer bu pozisyon zaten yönetiliyorsa, atla
            if (managedPositions.has(key)) continue;

            console.log(`⚠️ Mevcut ${pos.symbol} ${side} pozisyonu keşfedildi:`, pos);

            managedPositions.set(key, {
                symbol: pos.symbol,
                orderId: 0, // Bilinmiyor
                side: side as 'LONG' | 'SHORT',
                quantity: Math.abs(positionSize),
                entryPrice: parseFloat(pos.entryPrice || '0'),
                isIsolated: true,
                leverage: parseInt(pos.leverage.toString() || '1'),
                createdAt: new Date()
            });
        }

    } catch (error) {
        console.error('❌ Hesap bilgileri güncellenirken hata:', error);
    }
}

/**
 * Pozisyonu kapat
 */
export async function closePosition(symbol: string, side: 'LONG' | 'SHORT'): Promise<boolean> {
    try {
        if (!restAPI) {
            console.error('❌ REST API başlatılmadı');
            return false;
        }

        console.log(`🔄 ${symbol} ${side} pozisyonu kapatılıyor...`);

        // Pozisyonu bul
        const key = `${symbol}_${side}`;
        const position = managedPositions.get(key);

        if (!position) {
            console.error(`❌ ${symbol} ${side} pozisyonu bulunamadı`);
            return false;
        }

        // TP/SL emirlerini iptal et
        if (position.takeProfitOrderId) {
            try {
                await restAPI.cancelOrder(symbol, position.takeProfitOrderId);
                console.log(`✅ Take Profit emri iptal edildi - ID: ${position.takeProfitOrderId}`);
            } catch (error) {
                console.warn(`⚠️ Take Profit emri iptal edilemedi:`, error);
            }
        }

        if (position.stopLossOrderId) {
            try {
                await restAPI.cancelOrder(symbol, position.stopLossOrderId);
                console.log(`✅ Stop Loss emri iptal edildi - ID: ${position.stopLossOrderId}`);
            } catch (error) {
                console.warn(`⚠️ Stop Loss emri iptal edilemedi:`, error);
            }
        }

        // Pozisyonu kapat
        await restAPI.closePosition(symbol, side);
        console.log(`✅ ${symbol} ${side} pozisyonu kapatıldı`);

        // Yönetilen pozisyonlardan kaldır
        managedPositions.delete(key);

        // Hesap bilgilerini güncelle
        await updateAccountInfo();

        return true;

    } catch (error) {
        console.error(`❌ ${symbol} ${side} pozisyonu kapatılırken hata:`, error);
        return false;
    }
}

/**
 * Tüm yönetilen pozisyonları döndür
 */
export function getManagedPositions(): ManagedPosition[] {
    return Array.from(managedPositions.values());
}

/**
 * Trading filtrelerini kontrol et
 */
export function checkTradingFilters(
    symbol: string,
    marketData: any,
    openPositionsCount: number,
    config: AdvancedTradingConfig
): { allowed: boolean; reason?: string } {
    try {
        // 1. Zaten maksimum pozisyon sayısına ulaşıldı mı?
        if (openPositionsCount >= config.maxOpenPositions) {
            return {
                allowed: false,
                reason: `Maksimum pozisyon sayısına ulaşıldı (${config.maxOpenPositions})`
            };
        }

        // 2. Bu sembol için zaten bir pozisyon var mı?
        const hasLongPosition = managedPositions.has(`${symbol}_LONG`);
        const hasShortPosition = managedPositions.has(`${symbol}_SHORT`);

        if (hasLongPosition || hasShortPosition) {
            return {
                allowed: false,
                reason: `${symbol} için zaten bir pozisyon açık`
            };
        }

        // 3. Minimum işlem hacmi filtresi
        const volume24h = parseFloat(marketData?.quoteVolume || marketData?.q || '0');
        const volumeInMillions = volume24h / 1000000;

        if (volumeInMillions < config.minVolumeForTrade) {
            return {
                allowed: false,
                reason: `24s işlem hacmi çok düşük: $${volumeInMillions.toFixed(2)}M < $${config.minVolumeForTrade}M`
            };
        }

        return { allowed: true };

    } catch (error) {
        console.error('❌ Trading filtreleri kontrol edilirken hata:', error);
        return { allowed: false, reason: `Filtre hatası: ${error}` };
    }
}

/**
 * Servisi kapat
 */
export async function shutdownAdvancedTradingService(): Promise<void> {
    try {
        console.log('🛑 Advanced Trading Service kapatılıyor...');

        // Açık tüm pozisyonları kapat
        const positions = getManagedPositions();
        for (const pos of positions) {
            try {
                await closePosition(pos.symbol, pos.side);
            } catch (error) {
                console.error(`❌ ${pos.symbol} ${pos.side} pozisyonu kapatılırken hata:`, error);
            }
        }

        // WebSocket bağlantısını kapat
        if (wsAPI) {
            wsAPI.disconnect();
            wsAPI = null;
        }

        // REST API instance'ı temizle
        restAPI = null;

        // Yönetilen pozisyonları temizle
        managedPositions.clear();

        console.log('✅ Advanced Trading Service kapatıldı');

    } catch (error) {
        console.error('❌ Advanced Trading Service kapatılırken hata:', error);
    }
} 
