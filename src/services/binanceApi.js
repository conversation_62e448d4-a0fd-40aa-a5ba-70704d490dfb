import { toast } from 'sonner';
import credentials from '@/config/binance-credentials.json'; // API anahtarlarını import et

// WebSocket bağlantıları
let userDataWs = null;
let tradeWs = null;

// İşlem sonuçları ve ücretleri için depolama
const tradeHistory = [];
const feeHistory = [];

// Hesap bakiyesi için son durum
let lastBalanceUpdate = null;

// API anahtarları (gerçek kullanımda bir config'den veya environment variables'dan alınmalıdır)
const BASE_URL = 'https://parabot.fun/api'; // Backend proxy URL (Güncellendi)

// WebSocket durumu
const wsStatus = {
    userDataConnected: false,
    tradeConnected: false,
    authenticated: false
};

// Hata kontrolü ile HTTP istekleri yapmak için yardımcı fonksiyon
async function fetchWithErrorHandling(url, options = {}) {
    try {
        console.log('[API] fetchWithErrorHandling - Requesting URL:', url, 'Options:', options);
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });

        if (!response.ok) {
            const responseText = await response.text();
            console.error(`[API] fetchWithErrorHandling - Error: Status ${response.status}`, responseText);
            let errorData;
            try {
                errorData = JSON.parse(responseText);
            } catch (e) {
                // Eğer JSON parse edilemezse, text response'u mesaj olarak kullan
                throw new Error(responseText || `HTTP error ${response.status}`);
            }
            throw new Error(errorData.message || errorData.msg || `HTTP error ${response.status} - ${responseText}`);
        }

        return await response.json();
    } catch (error) {
        console.error('[API] fetchWithErrorHandling - fetch failed:', error);
        toast.error(`API hatası: ${error.message}`);
        throw error;
    }
}

// Kullanıcı verileri WebSocket bağlantısını kur
export async function connectUserDataStream() {
    if (userDataWs && userDataWs.readyState === WebSocket.OPEN) {
        console.log('Kullanıcı veri akışı zaten bağlı');
        return;
    }

    try {
        // Önce sunucudan listenKey al
        const { listenKey } = await fetchWithErrorHandling(`${BASE_URL}/userDataStream`);

        if (!listenKey) {
            throw new Error('Listen key alınamadı');
        }

        // WebSocket bağlantısını kur
        userDataWs = new WebSocket(`wss://fstream.binance.com/ws/${listenKey}`);

        userDataWs.onopen = () => {
            console.log('Kullanıcı veri akışı bağlantısı kuruldu');
            wsStatus.userDataConnected = true;
            toast.success('Hesap verilerine bağlandı');

            // Her 30 dakikada bir listenKey'i yenile
            setInterval(async () => {
                try {
                    await fetchWithErrorHandling(`${BASE_URL}/userDataStream/keepalive`, {
                        method: 'PUT',
                        body: JSON.stringify({ listenKey })
                    });
                    console.log('Listen key yenilendi');
                } catch (error) {
                    console.error('Listen key yenilenirken hata:', error);
                }
            }, 30 * 60 * 1000); // 30 dakika
        };

        userDataWs.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);

                // Bakiye değişikliklerini işle
                if (data.e === 'ACCOUNT_UPDATE') {
                    console.log('Hesap güncellemesi alındı:', data);

                    // Bakiye güncellemelerini işle
                    if (data.a && data.a.B) {
                        const balances = data.a.B;
                        // Bakiyeyi işle ve UI'ı güncelle
                        updateBalanceData(balances);
                    }

                    // Pozisyon güncellemelerini işle
                    if (data.a && data.a.P) {
                        const positions = data.a.P;
                        // Pozisyonları işle ve UI'ı güncelle
                        updatePositionData(positions);
                    }
                }

                // Emir güncellemelerini işle
                if (data.e === 'ORDER_TRADE_UPDATE') {
                    console.log('Emir güncellemesi alındı:', data);

                    const order = data.o;
                    // Emir durumunu işle
                    processOrderUpdate(order);

                    // İşlem ücretlerini kaydet
                    if (order.n) { // commission amount
                        feeHistory.push({
                            symbol: order.s,
                            commission: order.n,
                            commissionAsset: order.N,
                            timestamp: new Date().toISOString()
                        });
                    }
                }
            } catch (error) {
                console.error('WebSocket mesajı işlenirken hata:', error);
            }
        };

        userDataWs.onerror = (error) => {
            console.error('Kullanıcı veri akışı hatası:', error);
            wsStatus.userDataConnected = false;
            toast.error('Hesap veri bağlantısında hata oluştu');
        };

        userDataWs.onclose = () => {
            console.log('Kullanıcı veri akışı bağlantısı kapatıldı');
            wsStatus.userDataConnected = false;
            toast.warning('Hesap veri bağlantısı kesildi');

            // Bağlantıyı yeniden kurmayı dene
            setTimeout(() => {
                connectUserDataStream();
            }, 5000);
        };

        return userDataWs;
    } catch (error) {
        console.error('Kullanıcı veri akışı bağlantısı kurulamadı:', error);
        toast.error(`Veri akışı bağlantı hatası: ${error.message}`);
        throw error;
    }
}

// Ticaret işlemleri için WebSocket bağlantısını kur
export async function connectTradeWebSocket() {
    if (tradeWs && tradeWs.readyState === WebSocket.OPEN) {
        console.log('Ticaret WebSocket zaten bağlı');
        return;
    }

    try {
        tradeWs = new WebSocket('wss://fstream.binance.com/ws-fapi/v1');

        tradeWs.onopen = async () => {
            console.log('Ticaret WebSocket bağlantısı kuruldu');
            wsStatus.tradeConnected = true;

            // WebSocket'i kimlik doğrulama ile oturum açma
            await authenticateWebSocket();

            // Ping/Pong ile bağlantıyı canlı tutma
            setInterval(() => {
                if (tradeWs.readyState === WebSocket.OPEN) {
                    tradeWs.send(JSON.stringify({ id: Date.now(), method: 'ping' }));
                }
            }, 30000); // 30 saniyede bir ping gönder
        };

        tradeWs.onmessage = (event) => {
            try {
                const response = JSON.parse(event.data);
                console.log('Ticaret WebSocket yanıtı:', response);

                // Yanıt işleme
                processWebSocketResponse(response);
            } catch (error) {
                console.error('WebSocket yanıtı işlenirken hata:', error);
            }
        };

        tradeWs.onerror = (error) => {
            console.error('Ticaret WebSocket hatası:', error);
            wsStatus.tradeConnected = false;
            wsStatus.authenticated = false;
            toast.error('Ticaret bağlantısında hata oluştu');
        };

        tradeWs.onclose = () => {
            console.log('Ticaret WebSocket bağlantısı kapatıldı');
            wsStatus.tradeConnected = false;
            wsStatus.authenticated = false;
            toast.warning('Ticaret bağlantısı kesildi');

            // Bağlantıyı yeniden kurmayı dene
            setTimeout(() => {
                connectTradeWebSocket();
            }, 5000);
        };

        return tradeWs;
    } catch (error) {
        console.error('Ticaret WebSocket bağlantısı kurulamadı:', error);
        toast.error(`Ticaret bağlantı hatası: ${error.message}`);
        throw error;
    }
}

// WebSocket oturumunu kimlik doğrulama ile açma
async function authenticateWebSocket() {
    try {
        console.log('WebSocket oturumu kimlik doğrulaması yapılıyor...');

        // Sunucudan imzalı oturum açma verileri al
        const authData = await fetchWithErrorHandling(`${BASE_URL}/wsAuth`);

        if (!authData || !authData.signature) {
            throw new Error('Kimlik doğrulama verileri alınamadı');
        }

        // Kimlik doğrulama isteği gönder
        const authRequest = {
            id: `auth-${Date.now()}`,
            method: 'session.logon',
            params: authData
        };

        tradeWs.send(JSON.stringify(authRequest));
        console.log('Kimlik doğrulama isteği gönderildi');

        // Not: Yanıt, message event handler'da işlenecek
        return true;
    } catch (error) {
        console.error('WebSocket kimlik doğrulama hatası:', error);
        toast.error(`Kimlik doğrulama hatası: ${error.message}`);
        return false;
    }
}

// WebSocket yanıtlarını işleme
function processWebSocketResponse(response) {
    // Kimlik doğrulama yanıtını işle
    if (response.id && response.id.startsWith('auth-')) {
        if (response.status === 200) {
            wsStatus.authenticated = true;
            console.log('WebSocket oturumu başarıyla kimlik doğrulandı');
            toast.success('Ticaret API bağlantısı kuruldu');
        } else {
            console.error('Kimlik doğrulama başarısız:', response.error);
            toast.error(`Kimlik doğrulama başarısız: ${response.error?.msg || 'Bilinmeyen hata'}`);
        }
        return;
    }

    // Emir yanıtlarını işle
    if (response.id && response.id.startsWith('order-')) {
        if (response.status === 200) {
            console.log('Emir başarıyla işlendi:', response);
            toast.success(`Emir başarıyla işlendi: ${response.result?.orderId || ''}`);

            // İşlem sonucunu kaydet
            if (response.result) {
                const result = response.result;
                const tradeRecord = {
                    orderId: result.orderId,
                    symbol: result.symbol,
                    side: result.side,
                    type: result.type,
                    quantity: result.origQty,
                    price: result.price,
                    status: result.status,
                    timestamp: new Date().toISOString(),
                    clientOrderId: result.clientOrderId
                };

                tradeHistory.push(tradeRecord);
            }
        } else {
            console.error('Emir hatası:', response.error);
            toast.error(`Emir hatası: ${response.error?.msg || 'Bilinmeyen hata'}`);
        }
    }

    // İptal yanıtlarını işle
    if (response.id && response.id.startsWith('cancel-')) {
        if (response.status === 200) {
            console.log('Emir başarıyla iptal edildi:', response);
            toast.success('Emir başarıyla iptal edildi');
        } else {
            console.error('Emir iptal hatası:', response.error);
            toast.error(`Emir iptal hatası: ${response.error?.msg || 'Bilinmeyen hata'}`);
        }
    }

    // Ping yanıtını işle
    if (response.id && response.result && response.result === 'pong') {
        console.log('Pong yanıtı alındı');
    }
}

// Bakiye verilerini işleme
function updateBalanceData(balances) {
    if (!balances || !Array.isArray(balances)) return;

    const balanceData = {
        timestamp: new Date().toISOString(),
        assets: {}
    };

    balances.forEach(balance => {
        balanceData.assets[balance.a] = {
            asset: balance.a,
            walletBalance: balance.wb,
            crossWalletBalance: balance.cw,
            balanceChange: balance.bc
        };
    });

    lastBalanceUpdate = balanceData;

    // Olayı bildir, UI güncellenebilir
    const event = new CustomEvent('balance-update', { detail: balanceData });
    window.dispatchEvent(event);
}

// Pozisyon verilerini işleme
function updatePositionData(positions) {
    if (!positions || !Array.isArray(positions)) return;

    const positionData = {
        timestamp: new Date().toISOString(),
        positions: positions.map(pos => ({
            symbol: pos.s,
            positionAmount: pos.pa,
            entryPrice: pos.ep,
            accumulatedRealized: pos.cr,
            unrealizedPnL: pos.up,
            marginType: pos.mt,
            isolatedWallet: pos.iw,
            positionSide: pos.ps
        }))
    };

    // Olayı bildir, UI güncellenebilir
    const event = new CustomEvent('position-update', { detail: positionData });
    window.dispatchEvent(event);
}

// Emir güncellemelerini işleme
function processOrderUpdate(order) {
    // İşlem geçmişine ekle
    if (order.x === 'TRADE') {
        const tradeRecord = {
            symbol: order.s,
            orderId: order.i,
            side: order.S,
            type: order.o,
            price: order.p,
            avgPrice: order.ap,
            quantity: order.q,
            status: order.X,
            timestamp: new Date(order.T).toISOString()
        };

        tradeHistory.push(tradeRecord);

        // İşlem bildirimi göster
        toast.success(`İşlem gerçekleşti: ${order.s} ${order.S} ${order.q}`);

        // Olayı bildir, UI güncellenebilir
        const event = new CustomEvent('trade-update', { detail: tradeRecord });
        window.dispatchEvent(event);
    }
}

// Hesap bakiyesini almak için API isteği
export async function getAccountBalance() {
    try {
        const data = await fetchWithErrorHandling(`${BASE_URL}/account`);
        return data;
    } catch (error) {
        console.error('Hesap bakiyesi alınamadı:', error);
        throw error;
    }
}

// Açık pozisyonları almak için API isteği
export async function getOpenPositions() {
    try {
        const data = await fetchWithErrorHandling(`${BASE_URL}/positions`);
        return data;
    } catch (error) {
        console.error('Açık pozisyonlar alınamadı:', error);
        throw error;
    }
}

// WebSocket üzerinden LIMIT emir yerleştirme
export async function placeLimitOrder(symbol, side, quantity, price, reduceOnly = false) {
    try {
        if (!wsStatus.authenticated || !tradeWs || tradeWs.readyState !== WebSocket.OPEN) {
            console.error('WebSocket bağlantısı yok veya kimlik doğrulanmamış');
            await connectTradeWebSocket();
            return null;
        }

        const orderId = `order-${Date.now()}`;

        // Emir parametreleri için sunucudan imzalı veri al
        const orderParams = await fetchWithErrorHandling(`${BASE_URL}/signOrder`, {
            method: 'POST',
            body: JSON.stringify({
                symbol,
                side,
                type: 'LIMIT',
                timeInForce: 'GTC',
                quantity,
                price,
                reduceOnly
            })
        });

        if (!orderParams) {
            throw new Error('Emir parametreleri alınamadı');
        }

        // Emir isteği oluştur
        const orderRequest = {
            id: orderId,
            method: 'order.place',
            params: orderParams
        };

        // İsteği gönder
        tradeWs.send(JSON.stringify(orderRequest));
        console.log(`LIMIT emir isteği gönderildi: ${symbol} ${side} ${quantity} adet, fiyat: ${price}`);
        toast.info(`LIMIT emir gönderiliyor: ${symbol} ${side} ${quantity}`);

        return orderId;
    } catch (error) {
        console.error('LIMIT emir yerleştirilirken hata:', error);
        toast.error(`Emir hatası: ${error.message}`);
        return null;
    }
}

// WebSocket üzerinden MARKET emir yerleştirme
export async function placeMarketOrder(symbol, side, quantity, reduceOnly = false) {
    try {
        if (!wsStatus.authenticated || !tradeWs || tradeWs.readyState !== WebSocket.OPEN) {
            console.error('WebSocket bağlantısı yok veya kimlik doğrulanmamış');
            await connectTradeWebSocket();
            return null;
        }

        const orderId = `order-${Date.now()}`;

        // Emir parametreleri için sunucudan imzalı veri al
        const orderParams = await fetchWithErrorHandling(`${BASE_URL}/signOrder`, {
            method: 'POST',
            body: JSON.stringify({
                symbol,
                side,
                type: 'MARKET',
                quantity,
                reduceOnly
            })
        });

        if (!orderParams) {
            throw new Error('Emir parametreleri alınamadı');
        }

        // Emir isteği oluştur
        const orderRequest = {
            id: orderId,
            method: 'order.place',
            params: orderParams
        };

        // İsteği gönder
        tradeWs.send(JSON.stringify(orderRequest));
        console.log(`MARKET emir isteği gönderildi: ${symbol} ${side} ${quantity} adet`);
        toast.info(`MARKET emir gönderiliyor: ${symbol} ${side} ${quantity}`);

        return orderId;
    } catch (error) {
        console.error('MARKET emir yerleştirilirken hata:', error);
        toast.error(`Emir hatası: ${error.message}`);
        return null;
    }
}

// WebSocket üzerinden emir iptali
export async function cancelOrder(symbol, orderId) {
    try {
        if (!wsStatus.authenticated || !tradeWs || tradeWs.readyState !== WebSocket.OPEN) {
            console.error('WebSocket bağlantısı yok veya kimlik doğrulanmamış');
            await connectTradeWebSocket();
            return false;
        }

        const requestId = `cancel-${Date.now()}`;

        // İptal parametreleri için sunucudan imzalı veri al
        const cancelParams = await fetchWithErrorHandling(`${BASE_URL}/signCancel`, {
            method: 'POST',
            body: JSON.stringify({
                symbol,
                orderId
            })
        });

        if (!cancelParams) {
            throw new Error('İptal parametreleri alınamadı');
        }

        // İptal isteği oluştur
        const cancelRequest = {
            id: requestId,
            method: 'order.cancel',
            params: cancelParams
        };

        // İsteği gönder
        tradeWs.send(JSON.stringify(cancelRequest));
        console.log(`Emir iptal isteği gönderildi: ${symbol} orderId: ${orderId}`);
        toast.info(`Emir iptal ediliyor: ${orderId}`);

        return requestId;
    } catch (error) {
        console.error('Emir iptal edilirken hata:', error);
        toast.error(`İptal hatası: ${error.message}`);
        return null;
    }
}

// İşlem geçmişini ve ücretleri JSON olarak indirme
export function downloadTradeHistory() {
    try {
        const data = {
            trades: tradeHistory,
            fees: feeHistory,
            timestamp: new Date().toISOString(),
            totalTrades: tradeHistory.length
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `trade-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        return true;
    } catch (error) {
        console.error('İşlem geçmişi indirilirken hata:', error);
        toast.error(`İndirme hatası: ${error.message}`);
        return false;
    }
}

// İşlem istatistiklerini hesaplama
export function calculateTradeStats() {
    if (tradeHistory.length === 0) {
        return {
            totalTrades: 0,
            successfulTrades: 0,
            failedTrades: 0,
            totalVolume: 0,
            symbols: {}
        };
    }

    const stats = {
        totalTrades: tradeHistory.length,
        successfulTrades: 0,
        failedTrades: 0,
        totalVolume: 0,
        symbols: {}
    };

    tradeHistory.forEach(trade => {
        // Başarılı işlemleri say
        if (trade.status === 'FILLED') {
            stats.successfulTrades++;

            // Sembol bazında istatistikler
            if (!stats.symbols[trade.symbol]) {
                stats.symbols[trade.symbol] = {
                    trades: 0,
                    volume: 0,
                    buy: 0,
                    sell: 0
                };
            }

            stats.symbols[trade.symbol].trades++;
            const tradeVolume = parseFloat(trade.quantity) * parseFloat(trade.price || trade.avgPrice);
            stats.symbols[trade.symbol].volume += tradeVolume;
            stats.totalVolume += tradeVolume;

            if (trade.side === 'BUY') {
                stats.symbols[trade.symbol].buy++;
            } else {
                stats.symbols[trade.symbol].sell++;
            }
        } else if (trade.status === 'REJECTED' || trade.status === 'EXPIRED') {
            stats.failedTrades++;
        }
    });

    return stats;
}

// WebSocket bağlantı durumunu kontrol et
export function getWebSocketStatus() {
    return wsStatus;
}

// İşlem geçmişi ve ücretlerini dışa aktar
export function getTradeHistory() {
    return [...tradeHistory];
}

export function getFeeHistory() {
    return [...feeHistory];
}

// Son bakiye güncellemesini al
export function getLastBalanceUpdate() {
    return lastBalanceUpdate;
}

// WebSocket bağlantılarını kapat
export function closeWebSocketConnections() {
    if (userDataWs) {
        userDataWs.close();
    }

    if (tradeWs) {
        tradeWs.close();
    }

    console.log('Tüm WebSocket bağlantıları kapatıldı');
}

// İlk bakiye yükleme için REST API fonksiyonu
export async function fetchInitialAccountBalance() {
    try {
        console.log('[API] fetchInitialAccountBalance çağrıldı. İlk bakiye REST API ile yükleniyor...');

        // /fapi/v2/account endpoint'i Ed25519 imzası ve ilgili API anahtarını gerektirir.
        const requestBody = {
            endpoint: '/fapi/v2/account', // Hedef Binance endpoint'i
            method: 'GET',
            headers: { // Bu başlıklar proxy tarafından Binance'e iletilirken kullanılacak
                'X-MBX-APIKEY': credentials.ed25519ApiKey
            },
            // Ed25519 için timestamp gibi parametreler sunucu tarafında (proxy) eklenmeli
            // ve imza da sunucu tarafında oluşturulmalıdır.
            // İstemci sadece hangi endpoint'e ve hangi anahtarla gidileceğini söyler.
            params: {
                // timestamp: Date.now() // Timestamp proxy'de eklenecekse buradan göndermeye gerek yok
            }
        };

        console.log('[API] fetchInitialAccountBalance - Proxy request body:', requestBody);

        // Proxy endpoint'imiz /api/binance/proxy olmalı
        const response = await fetchWithErrorHandling(`${BASE_URL}/binance/proxy`, {
            method: 'POST',
            body: JSON.stringify(requestBody)
        });

        console.log('[API] fetchInitialAccountBalance - Proxy yanıtı alındı:', response);

        // Proxy'den gelen yanıt yapısı { status, result, success, headers, serverIP, timestamp } şeklinde
        if (response && response.success && response.result && response.result.assets) {
            const accountData = response.result;
            const usdtAsset = accountData.assets.find(asset => asset.asset === 'USDT');

            if (usdtAsset) {
                const availableBalance = parseFloat(usdtAsset.availableBalance);
                const totalBalance = parseFloat(usdtAsset.walletBalance);

                console.log(`[API] USDT Varlığı bulundu: Available: ${availableBalance}, Total: ${totalBalance}`);
                console.log('[API] Tüm assets:', accountData.assets);

                lastBalanceUpdate = {
                    asset: 'USDT',
                    balance: totalBalance,
                    availableBalance: availableBalance,
                    assets: accountData.assets,
                    timestamp: new Date().toISOString()
                };

                console.log('[API] ✅ İlk bakiye REST API ile yüklendi ve lastBalanceUpdate güncellendi:', lastBalanceUpdate);
                const event = new CustomEvent('initial-balance-loaded', { detail: lastBalanceUpdate });
                window.dispatchEvent(event);
                console.log('[API] "initial-balance-loaded" olayı fırlatıldı.');
                return lastBalanceUpdate;
            } else {
                console.warn('[API] Yanıtta USDT varlığı bulunamadı.', accountData);
                throw new Error('USDT varlığı API yanıtında bulunamadı.');
            }
        } else {
            console.error('[API] Proxy yanıtı beklenen formatta değil veya varlık bilgisi eksik:', response);
            const errorMessage = response && response.result && (response.result.msg || response.result.message || JSON.stringify(response.result));
            throw new Error(errorMessage || 'Proxy yanıtı geçersiz veya varlık bilgisi eksik.');
        }
    } catch (error) {
        console.error('[API] İlk bakiye yüklenirken kritik hata:', error);
        toast.error(`Bakiye yükleme hatası: ${error.message}`);
        const errorEvent = new CustomEvent('initial-balance-load-failed', { detail: { message: error.message, error } });
        window.dispatchEvent(errorEvent);
        console.log('[API] "initial-balance-load-failed" olayı fırlatıldı.');
        return null;
    }
}

// Modülü dışa aktar
export default {
    connectUserDataStream,
    connectTradeWebSocket,
    fetchInitialAccountBalance,
    getAccountBalance,
    getOpenPositions,
    placeLimitOrder,
    placeMarketOrder,
    cancelOrder,
    downloadTradeHistory,
    calculateTradeStats,
    getWebSocketStatus,
    getTradeHistory,
    getFeeHistory,
    getLastBalanceUpdate,
    closeWebSocketConnections
}; 