// Binance REST API Service
import { BinanceAuthCredentials, BinanceOrderResponse, BinancePosition, RealOrderParams } from './binanceService';
import credentialsConfig from '../config/binance-credentials.json';

// REST API endpoints
const API_BASE_URL = '/api/binance'; // Proxy aracılığıyla istekler yapılacak
const FUTURES_API_PATH = '/fapi/v1';
const FUTURES_API_V2_PATH = '/fapi/v2';

/**
 * Binance REST API için service sınıfı
 * Trading işlemleri (emir verme, kapatma vb.) için kullanılır
 */
export class BinanceRestAPI {
    private credentials: BinanceAuthCredentials;
    private requestTimeoutMs: number = 30000; // 30 saniye timeout
    private lastRequestTime: number = 0;
    private rateLimitMs: number = 500; // Binance rate limit

    constructor(credentials: BinanceAuthCredentials) {
        this.credentials = credentials;
        console.log('🌐 Binance REST API servisi başlatıldı');
    }

    /**
     * Binance REST API'ye istekler yapar
     * @param endpoint API endpoint
     * @param method HTTP metodu
     * @param params İstek parametreleri
     * @returns API yanıtı
     */
    private async makeRequest<T>(
        endpoint: string,
        method: 'GET' | 'POST' | 'DELETE' | 'PUT' = 'GET',
        params: Record<string, any> = {}
    ): Promise<T> {
        // Rate limiting uygula
        const now = Date.now();
        const elapsed = now - this.lastRequestTime;
        if (elapsed < this.rateLimitMs) {
            await new Promise(resolve => setTimeout(resolve, this.rateLimitMs - elapsed));
        }
        this.lastRequestTime = Date.now();

        try {
            const url = `${API_BASE_URL}${endpoint}`;
            const headers: HeadersInit = {
                'Content-Type': 'application/json',
                'X-MBX-APIKEY': this.credentials.apiKey
            };

            const config: RequestInit = {
                method,
                headers,
                signal: AbortSignal.timeout(this.requestTimeoutMs)
            };

            // POST, PUT veya DELETE için body oluştur
            if (method !== 'GET') {
                config.body = JSON.stringify(params);
            }

            console.log(`🔄 ${method} isteği gönderiliyor: ${url}`, params);
            const response = await fetch(url, config);

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API hatası (${response.status}): ${errorText}`);
            }

            const data = await response.json();
            console.log(`✅ ${method} isteği başarılı:`, data);
            return data as T;
        } catch (error) {
            console.error('❌ API isteği başarısız:', error);
            throw error;
        }
    }

    /**
     * Hesap bilgilerini alır
     * @returns Hesap bilgileri
     */
    async getAccountInfo(): Promise<any> {
        return this.makeRequest(`${FUTURES_API_V2_PATH}/account`, 'GET');
    }

    /**
     * Hesap bakiyesini alır
     * @returns Hesap bakiyesi
     */
    async getAccountBalance(): Promise<any> {
        return this.makeRequest(`${FUTURES_API_V2_PATH}/balance`, 'GET');
    }

    /**
     * Pozisyon bilgilerini alır
     * @param symbol Symbol (opsiyonel)
     * @returns Pozisyon bilgileri
     */
    async getPositions(symbol?: string): Promise<BinancePosition[]> {
        const endpoint = symbol
            ? `${FUTURES_API_PATH}/positionRisk?symbol=${symbol}`
            : `${FUTURES_API_PATH}/positionRisk`;

        return this.makeRequest<BinancePosition[]>(endpoint, 'GET');
    }

    /**
     * Kaldıraç ayarlar
     * @param symbol Symbol
     * @param leverage Kaldıraç değeri
     * @returns İşlem sonucu
     */
    async setLeverage(symbol: string, leverage: number): Promise<any> {
        return this.makeRequest(`${FUTURES_API_PATH}/leverage`, 'POST', {
            symbol,
            leverage
        });
    }

    /**
     * Margin tipini ayarlar (ISOLATED/CROSSED)
     * @param symbol Symbol
     * @param marginType Margin tipi
     * @returns İşlem sonucu
     */
    async setMarginType(symbol: string, marginType: 'ISOLATED' | 'CROSSED'): Promise<any> {
        return this.makeRequest(`${FUTURES_API_PATH}/marginType`, 'POST', {
            symbol,
            marginType
        });
    }

    /**
     * Pozisyon modunu ayarlar (HEDGE/ONE-WAY)
     * @param dualSidePosition true=HEDGE mode, false=ONE-WAY mode
     * @returns İşlem sonucu
     */
    async setPositionMode(dualSidePosition: boolean): Promise<any> {
        return this.makeRequest(`${FUTURES_API_PATH}/positionSide/dual`, 'POST', {
            dualSidePosition: dualSidePosition.toString()
        });
    }

    /**
     * Emir verir
     * @param orderParams Emir parametreleri
     * @returns Emir sonucu
     */
    async placeOrder(orderParams: RealOrderParams): Promise<BinanceOrderResponse> {
        return this.makeRequest<BinanceOrderResponse>(`${FUTURES_API_PATH}/order`, 'POST', orderParams);
    }

    /**
     * MARKET emir verir
     * @param symbol Symbol
     * @param side İşlem yönü (BUY/SELL)
     * @param quantity Miktar
     * @param reduceOnly Sadece pozisyon kapatma (opsiyonel)
     * @returns Emir sonucu
     */
    async placeMarketOrder(
        symbol: string,
        side: 'BUY' | 'SELL',
        quantity: string,
        reduceOnly: boolean = false
    ): Promise<BinanceOrderResponse> {
        const orderParams: RealOrderParams = {
            symbol,
            side,
            type: 'MARKET',
            quantity,
            reduceOnly
        };

        return this.placeOrder(orderParams);
    }

    /**
     * LIMIT emir verir
     * @param symbol Symbol
     * @param side İşlem yönü (BUY/SELL)
     * @param quantity Miktar
     * @param price Fiyat
     * @param timeInForce Geçerlilik süresi (GTC/IOC/FOK)
     * @param reduceOnly Sadece pozisyon kapatma (opsiyonel)
     * @returns Emir sonucu
     */
    async placeLimitOrder(
        symbol: string,
        side: 'BUY' | 'SELL',
        quantity: string,
        price: string,
        timeInForce: string = 'GTC',
        reduceOnly: boolean = false
    ): Promise<BinanceOrderResponse> {
        const orderParams: RealOrderParams = {
            symbol,
            side,
            type: 'LIMIT',
            quantity,
            price,
            timeInForce,
            reduceOnly
        };

        return this.placeOrder(orderParams);
    }

    /**
     * STOP_MARKET emir verir (Stop Loss)
     * @param symbol Symbol
     * @param side İşlem yönü (BUY/SELL)
     * @param quantity Miktar
     * @param stopPrice Tetiklenme fiyatı
     * @returns Emir sonucu
     */
    async placeStopLossOrder(
        symbol: string,
        side: 'BUY' | 'SELL',
        quantity: string,
        stopPrice: string
    ): Promise<BinanceOrderResponse> {
        const orderParams: RealOrderParams = {
            symbol,
            side,
            type: 'STOP_MARKET',
            quantity,
            stopPrice,
            reduceOnly: true
        };

        return this.placeOrder(orderParams);
    }

    /**
     * TAKE_PROFIT_MARKET emir verir (Take Profit)
     * @param symbol Symbol
     * @param side İşlem yönü (BUY/SELL)
     * @param quantity Miktar
     * @param stopPrice Tetiklenme fiyatı
     * @returns Emir sonucu
     */
    async placeTakeProfitOrder(
        symbol: string,
        side: 'BUY' | 'SELL',
        quantity: string,
        stopPrice: string
    ): Promise<BinanceOrderResponse> {
        const orderParams: RealOrderParams = {
            symbol,
            side,
            type: 'TAKE_PROFIT_MARKET',
            quantity,
            stopPrice,
            reduceOnly: true
        };

        return this.placeOrder(orderParams);
    }

    /**
     * Bir emri iptal eder
     * @param symbol Symbol
     * @param orderId Emir ID (opsiyonel)
     * @param origClientOrderId Client Order ID (opsiyonel)
     * @returns İşlem sonucu
     */
    async cancelOrder(
        symbol: string,
        orderId?: number,
        origClientOrderId?: string
    ): Promise<any> {
        const params: Record<string, any> = { symbol };

        if (orderId) params.orderId = orderId;
        if (origClientOrderId) params.origClientOrderId = origClientOrderId;

        return this.makeRequest(`${FUTURES_API_PATH}/order`, 'DELETE', params);
    }

    /**
     * Tüm açık emirleri iptal eder
     * @param symbol Symbol
     * @returns İşlem sonucu
     */
    async cancelAllOpenOrders(symbol: string): Promise<any> {
        return this.makeRequest(`${FUTURES_API_PATH}/allOpenOrders`, 'DELETE', { symbol });
    }

    /**
     * Bir emrin durumunu sorgular
     * @param symbol Symbol
     * @param orderId Emir ID (opsiyonel)
     * @param origClientOrderId Client Order ID (opsiyonel)
     * @returns Emir durumu
     */
    async getOrderStatus(
        symbol: string,
        orderId?: number,
        origClientOrderId?: string
    ): Promise<any> {
        const params: Record<string, any> = { symbol };

        if (orderId) params.orderId = orderId;
        if (origClientOrderId) params.origClientOrderId = origClientOrderId;

        const queryParams = Object.entries(params)
            .map(([key, value]) => `${key}=${value}`)
            .join('&');

        return this.makeRequest(`${FUTURES_API_PATH}/order?${queryParams}`, 'GET');
    }

    /**
     * Tüm açık emirleri getirir
     * @param symbol Symbol (opsiyonel)
     * @returns Açık emirler
     */
    async getAllOpenOrders(symbol?: string): Promise<any[]> {
        const endpoint = symbol
            ? `${FUTURES_API_PATH}/openOrders?symbol=${symbol}`
            : `${FUTURES_API_PATH}/openOrders`;

        return this.makeRequest<any[]>(endpoint, 'GET');
    }

    /**
     * Pozisyonu market emri ile kapatır
     * @param symbol Symbol
     * @param positionSide Pozisyon yönü (LONG/SHORT)
     * @returns İşlem sonucu
     */
    async closePosition(
        symbol: string,
        positionSide: 'LONG' | 'SHORT'
    ): Promise<BinanceOrderResponse> {
        try {
            // Önce pozisyon bilgilerini al
            const positions = await this.getPositions(symbol);
            const position = positions.find(p =>
                p.symbol === symbol &&
                parseFloat(p.positionAmt || p.positionAmount || '0') !== 0
            );

            if (!position) {
                throw new Error(`${symbol} için açık pozisyon bulunamadı`);
            }

            const positionSize = Math.abs(
                parseFloat(position.positionAmt || position.positionAmount || '0')
            );

            // Pozisyon yönünü belirle
            const positionValue = parseFloat(position.positionAmt || position.positionAmount || '0');
            const closeSide = positionValue > 0 ? 'SELL' : 'BUY';

            // Market emri ile pozisyonu kapat
            return this.placeMarketOrder(
                symbol,
                closeSide,
                positionSize.toString(),
                true // reduceOnly = true
            );
        } catch (error) {
            console.error('❌ Pozisyon kapatma hatası:', error);
            throw error;
        }
    }

    /**
     * LONG pozisyon açar
     * @param symbol Symbol
     * @param quantity Miktar
     * @param leverage Kaldıraç (opsiyonel)
     * @returns İşlem sonucu
     */
    async openLongPosition(
        symbol: string,
        quantity: string,
        leverage?: number
    ): Promise<BinanceOrderResponse> {
        try {
            // Kaldıraç ayarla (eğer belirtilmişse)
            if (leverage) {
                await this.setLeverage(symbol, leverage);
            }

            // LONG pozisyon aç (BUY)
            return this.placeMarketOrder(symbol, 'BUY', quantity);
        } catch (error) {
            console.error('❌ LONG pozisyon açma hatası:', error);
            throw error;
        }
    }

    /**
     * SHORT pozisyon açar
     * @param symbol Symbol
     * @param quantity Miktar
     * @param leverage Kaldıraç (opsiyonel)
     * @returns İşlem sonucu
     */
    async openShortPosition(
        symbol: string,
        quantity: string,
        leverage?: number
    ): Promise<BinanceOrderResponse> {
        try {
            // Kaldıraç ayarla (eğer belirtilmişse)
            if (leverage) {
                await this.setLeverage(symbol, leverage);
            }

            // SHORT pozisyon aç (SELL)
            return this.placeMarketOrder(symbol, 'SELL', quantity);
        } catch (error) {
            console.error('❌ SHORT pozisyon açma hatası:', error);
            throw error;
        }
    }

    /**
     * Trading kurallarını getirir
     * @param symbol Symbol (opsiyonel)
     * @returns Trading kuralları
     */
    async getTradingRules(symbol?: string): Promise<any> {
        const endpoint = '/fapi/v1/exchangeInfo';
        const queryParams = symbol ? `?symbol=${symbol}` : '';
        return this.makeRequest(`${endpoint}${queryParams}`, 'GET');
    }
}

// Singleton instance
let restApiInstance: BinanceRestAPI | null = null;

/**
 * Binance REST API instance döndürür (Singleton pattern)
 * @param credentials API kimlik bilgileri
 * @returns BinanceRestAPI instance
 */
export const getBinanceRestAPI = (
    inputCredentials: BinanceAuthCredentials = {
        apiKey: credentialsConfig.apiKey || '',
        apiSecret: ''
    }
): BinanceRestAPI => {
    if (!restApiInstance || restApiInstance['credentials'].apiKey !== inputCredentials.apiKey) {
        // Kimlik bilgileri boşsa, localStorage'dan yükle
        let finalCredentials = inputCredentials;

        if (!inputCredentials.apiKey) {
            try {
                const savedCredentialsJSON = localStorage.getItem('binance_credentials');
                if (savedCredentialsJSON) {
                    const savedCreds = JSON.parse(savedCredentialsJSON);
                    if (savedCreds.apiKey) {
                        finalCredentials = savedCreds;
                        console.log('🔑 Kaydedilmiş API key kullanılıyor:', finalCredentials.apiKey.substring(0, 8) + '...');
                    }
                }
            } catch (error) {
                console.warn('⚠️ Kaydedilmiş credentials yüklenemedi:', error);
            }
        }

        console.log('🔑 API key kullanılıyor:', finalCredentials.apiKey ? finalCredentials.apiKey.substring(0, 8) + '...' : 'Boş');

        // Create new instance
        restApiInstance = new BinanceRestAPI(finalCredentials);
    }

    return restApiInstance;
};

/**
 * Hesap bakiyesi bilgisini getirir (Dashboard için)
 * @returns Hesap bakiyesi
 */
export const fetchInitialAccountBalance = async (): Promise<{
    balance: number;
    availableBalance: number;
}> => {
    try {
        // localStorage'dan kimlik bilgilerini al
        const savedCredentialsJSON = localStorage.getItem('binance_credentials');
        if (!savedCredentialsJSON) {
            throw new Error('API kimlik bilgileri bulunamadı');
        }

        const credentials: BinanceAuthCredentials = JSON.parse(savedCredentialsJSON);
        const api = getBinanceRestAPI(credentials);

        // Hesap bakiyesini getir
        const balanceData = await api.getAccountBalance();

        // USDT bakiyesini bul
        const usdtBalance = balanceData.find((b: any) => b.asset === 'USDT');

        if (!usdtBalance) {
            throw new Error('USDT bakiyesi bulunamadı');
        }

        // Toplam ve kullanılabilir bakiyeleri döndür
        return {
            balance: parseFloat(usdtBalance.balance || '0'),
            availableBalance: parseFloat(usdtBalance.availableBalance || '0')
        };
    } catch (error) {
        console.error('❌ Hesap bakiyesi getirme hatası:', error);
        // Default değerler
        return {
            balance: 0,
            availableBalance: 0
        };
    }
};

// Default export olarak API fonksiyonlarını dışa aktar
export default {
    getBinanceRestAPI,
    fetchInitialAccountBalance
};
