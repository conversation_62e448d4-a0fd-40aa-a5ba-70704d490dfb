// Historical data service for Binance API
export interface KlineData {
  openTime: number;
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
  closeTime: number;
  quoteAssetVolume: string;
  numberOfTrades: number;
  takerBuyBaseAssetVolume: string;
  takerBuyQuoteAssetVolume: string;
  ignore: string;
}

export interface HistoricalPricePoint {
  price: number;
  timestamp: number;
}

// Get historical kline data from Binance API
export const fetchHistoricalKlineData = async (
  symbol: string,
  interval: string = '1m',
  limit: number = 100
): Promise<HistoricalPricePoint[]> => {
  // Force production endpoint
  const baseUrl = 'https://api.binance.com/api/v3';

  const url = `${baseUrl}/klines?symbol=${symbol}&interval=${interval}&limit=${limit}`;

  try {
    console.log(`Fetching historical data for ${symbol} - ${limit} ${interval} candles`);

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: KlineData[] = await response.json();

    // Convert to price points using close price
    const pricePoints: HistoricalPricePoint[] = data.map(kline => ({
      price: parseFloat(kline[4]), // Close price
      timestamp: kline[6] // Close time
    }));

    console.log(`Fetched ${pricePoints.length} historical data points for ${symbol}`);
    return pricePoints;

  } catch (error) {
    console.error(`Error fetching historical data for ${symbol}:`, error);
    return [];
  }
};

// Fetch historical data for multiple symbols
export const fetchMultipleSymbolsHistoricalData = async (
  symbols: string[],
  interval: string = '1m',
  limit: number = 100
): Promise<Record<string, HistoricalPricePoint[]>> => {
  console.log(`Fetching historical data for ${symbols.length} symbols`);

  const results: Record<string, HistoricalPricePoint[]> = {};

  // Process symbols in batches to avoid rate limiting
  const batchSize = 5;
  for (let i = 0; i < symbols.length; i += batchSize) {
    const batch = symbols.slice(i, i + batchSize);

    // Process batch in parallel
    const promises = batch.map(symbol =>
      fetchHistoricalKlineData(symbol, interval, limit)
        .then(data => ({ symbol, data }))
        .catch(error => {
          console.error(`Failed to fetch data for ${symbol}:`, error);
          return { symbol, data: [] };
        })
    );

    const batchResults = await Promise.all(promises);

    // Store results
    batchResults.forEach(({ symbol, data }) => {
      results[symbol] = data;
    });

    // Add delay between batches to respect rate limits
    if (i + batchSize < symbols.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log(`Historical data fetch completed for ${Object.keys(results).length} symbols`);
  return results;
};

// Get the last N minutes of data
export const getLastNMinutesData = async (
  symbol: string,
  minutes: number
): Promise<HistoricalPricePoint[]> => {
  return fetchHistoricalKlineData(symbol, '1m', minutes);
};
