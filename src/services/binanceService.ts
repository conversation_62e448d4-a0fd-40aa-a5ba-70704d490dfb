// Constants
const BINANCE_FUTURES_WS_URL = 'wss://fstream.binance.com/ws';
const BINANCE_FUTURES_API_WS_URL = 'wss://ws-fapi.binance.com/ws-fapi/v3';
const BINANCE_FUTURES_TESTNET_WS_URL = 'wss://fstream.testnet.binance.vision';
const BINANCE_FUTURES_API_TESTNET_WS_URL = 'wss://ws-fapi.testnet.binance.vision/ws-fapi/v3';

// Base URLs for different endpoints
const BINANCE_FUTURES_REST_URL = 'https://fapi.binance.com';
const BINANCE_TESTNET_FUTURES_REST_URL = 'https://testnet.binancefuture.com';

const STREAM_NAME_24HR = '24hrTicker';
const STREAM_NAME_TRADE = 'aggTrade';
const STREAM_NAME_KLINE = 'kline';

// Types
export type CoinData = {
  symbol?: string;
  s?: string; // symbol
  c?: string; // current price
  P?: string; // price change percent
  q?: string; // volume
  h?: string; // high
  l?: string; // low
  price?: number;
  priceChangePercent?: number;
  volume?: number;
  high24h?: number;
  low24h?: number;
  isAlerted?: boolean;
};

// Real order params type for Binance Futures API
export interface RealOrderParams {
  symbol: string;
  side: 'BUY' | 'SELL';
  type: string;
  quantity: string | number;
  price?: string | number;
  timeInForce?: string;
  stopPrice?: string | number;
  closePosition?: boolean;
  reduceOnly?: boolean;
  positionSide?: 'BOTH' | 'LONG' | 'SHORT';
  workingType?: string;
  priceProtect?: boolean;
  recvWindow?: number;
}

// Binance order response type
export interface BinanceOrderResponse {
  orderId: number;
  symbol: string;
  status: string;
  clientOrderId: string;
  price: string;
  avgPrice: string;
  origQty: string;
  executedQty: string;
  cumQty: string;
  cumQuote: string;
  timeInForce: string;
  type: string;
  reduceOnly: boolean;
  closePosition: boolean;
  side: string;
  positionSide: string;
  stopPrice: string;
  workingType: string;
  priceProtect: boolean;
  origType: string;
  updateTime: number;
}

// Binance position type
export interface BinancePosition {
  symbol: string;
  positionAmount?: string;
  positionAmt?: string;
  entryPrice: string;
  markPrice?: string;
  unRealizedProfit?: string;
  unrealizedProfit?: string;
  liquidationPrice?: string;
  leverage: string | number;
  marginType: string;
  isolatedWallet?: string;
  positionSide: string;
  updateTime?: number;
}

export type BinanceStreamType = '24hrTicker' | 'aggTrade' | 'kline';

export interface BinanceSubscriptionConfig {
  symbols: string[];
  streamType: BinanceStreamType;
  interval?: string; // For kline streams
}

// Interface for authentication
export interface BinanceAuthCredentials {
  apiKey: string;
  apiSecret?: string;
}

// WebSocket subscription message generator
export const createSubscriptionMessage = (config: BinanceSubscriptionConfig): any => {
  const { symbols, streamType, interval } = config;

  // Generate individual stream names
  const streams = symbols.map(symbol => {
    const lowerSymbol = symbol.toLowerCase();
    switch (streamType) {
      case 'kline':
        return `${lowerSymbol}@kline_${interval || '1m'}`;
      case 'aggTrade':
        return `${lowerSymbol}@aggTrade`;
      case '24hrTicker':
      default:
        return `${lowerSymbol}@ticker`;
    }
  });

  // Create subscription message
  return {
    method: "SUBSCRIBE",
    params: streams,
    id: Date.now()
  };
};

// WebSocket unsubscription message generator
export const createUnsubscriptionMessage = (config: BinanceSubscriptionConfig): any => {
  const { symbols, streamType, interval } = config;

  // Generate individual stream names
  const streams = symbols.map(symbol => {
    const lowerSymbol = symbol.toLowerCase();
    switch (streamType) {
      case 'kline':
        return `${lowerSymbol}@kline_${interval || '1m'}`;
      case 'aggTrade':
        return `${lowerSymbol}@aggTrade`;
      case '24hrTicker':
      default:
        return `${lowerSymbol}@ticker`;
    }
  });

  // Create unsubscription message
  return {
    method: "UNSUBSCRIBE",
    params: streams,
    id: Date.now()
  };
};

// Generate HMAC SHA256 signature for authenticated requests - SERVER-SIDE VERSION
export const generateSignature = async (secret: string, queryString: string): Promise<string> => {
  console.log('🔐 Using SERVER-SIDE signature generation through ************:3001...');

  try {
    // Use your server for signature generation to avoid Web Crypto API issues
    const proxyUrl = `${getApiProxyBase()}/api/binance/proxy`;

    const signatureRequest = {
      endpoint: 'SIGNATURE_ONLY', // Special endpoint for signature generation
      method: 'POST',
      headers: {},
      params: {
        queryString: queryString,
        // apiSecret: secret // API Secret artık proxy'ye gönderilmeyecek
      }
    };

    const response = await fetch(proxyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(signatureRequest)
    });

    if (response.ok) {
      const data = await response.json();
      if (data.signature) {
        console.log('✅ Server-side signature generated successfully');
        return data.signature;
      }
    }

    // Fallback to Web Crypto API if server signature fails
    console.log('⚠️ Server signature failed, using Web Crypto API fallback...');
    return await generateSignatureWebCrypto(secret, queryString);

  } catch (error) {
    console.warn('⚠️ Server signature error, using Web Crypto API fallback:', error);
    return await generateSignatureWebCrypto(secret, queryString);
  }
};

// Web Crypto API fallback (original method)
export const generateSignatureWebCrypto = async (secret: string, queryString: string): Promise<string> => {
  try {
    // Check if Web Crypto API is available
    if (typeof crypto === 'undefined' || !crypto.subtle) {
      console.error('❌ Web Crypto API is not available in this environment');
      throw new Error('Web Crypto API not supported');
    }

    const encoder = new TextEncoder();
    const key = encoder.encode(secret);
    const message = encoder.encode(queryString);

    // Using the Web Crypto API to create the signature
    const cryptoKey = await crypto.subtle.importKey(
      "raw",
      key,
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    );

    const signature = await crypto.subtle.sign("HMAC", cryptoKey, message);

    // Convert the signature to a hex string (lowercase)
    return Array.from(new Uint8Array(signature))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  } catch (error) {
    console.error("Error generating signature with Web Crypto API:", error);
    throw new Error("Signature generation failed");
  }
};

// Process 24hr ticker data from WebSocket
export const process24hrTickerData = (message: any): CoinData | null => {
  if (!message || !message.s) return null;

  try {
    return {
      symbol: message.s, // Symbol
      price: parseFloat(message.c), // Current price
      priceChangePercent: parseFloat(message.P), // Price change percent
      volume: parseFloat(message.v), // Volume
      high24h: parseFloat(message.h), // 24h high
      low24h: parseFloat(message.l), // 24h low
    };
  } catch (error) {
    console.error('Error processing ticker data:', error);
    return null;
  }
};

// Create authenticated request for WebSocket API
export const createAuthenticatedRequest = async (
  method: string,
  params: Record<string, any>,
  credentials: BinanceAuthCredentials
): Promise<any> => {
  const timestamp = Date.now();

  const requestParams = {
    ...params,
    apiKey: credentials.apiKey,
    timestamp,
  };

  // Create query string for signature
  const queryString = Object.entries(requestParams)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  // Generate signature
  const signature = await generateSignature(credentials.apiSecret || '', queryString);

  return {
    id: Date.now().toString(),
    method,
    params: {
      ...requestParams,
      signature
    }
  };
};

// Create a session logon request
export const createSessionLogonRequest = async (credentials: BinanceAuthCredentials): Promise<any> => {
  return createAuthenticatedRequest("session.logon", {}, credentials);
};

// Create an order placement request
export const createOrderPlacementRequest = async (
  credentials: BinanceAuthCredentials,
  orderParams: {
    symbol: string;
    side: 'BUY' | 'SELL';
    type: string;
    quantity: string;
    price?: string;
    timeInForce?: string;
    stopPrice?: string;
    recvWindow?: number;
  }
): Promise<any> => {
  return createAuthenticatedRequest("order.place", orderParams, credentials);
};

// Create leverage set request
export const createLeverageSetRequest = async (
  credentials: BinanceAuthCredentials,
  symbol: string,
  leverage: number
): Promise<any> => {
  return createAuthenticatedRequest("leverage.set", { symbol, leverage }, credentials);
};

// Create margin type set request
export const createMarginTypeSetRequest = async (
  credentials: BinanceAuthCredentials,
  symbol: string,
  marginType: 'ISOLATED' | 'CROSSED'
): Promise<any> => {
  return createAuthenticatedRequest("marginType.set", { symbol, marginType }, credentials);
};

// Create position mode set request
export const createPositionModeSetRequest = async (
  credentials: BinanceAuthCredentials,
  dualSidePosition: boolean
): Promise<any> => {
  return createAuthenticatedRequest("positionMode.set", { dualSidePosition }, credentials);
};

// Create account status request
export const createAccountStatusRequest = async (
  credentials: BinanceAuthCredentials,
  recvWindow: number = 5000
): Promise<any> => {
  return createAuthenticatedRequest("account.status", { recvWindow }, credentials);
};

// Create account balance request
export const createAccountBalanceRequest = async (
  credentials: BinanceAuthCredentials,
  recvWindow: number = 5000
): Promise<any> => {
  return createAuthenticatedRequest("account.balance", { recvWindow }, credentials);
};

// Create positions request
export const createPositionsRequest = async (
  credentials: BinanceAuthCredentials,
  symbol?: string,
  recvWindow: number = 5000
): Promise<any> => {
  const params: Record<string, any> = { recvWindow };
  if (symbol) params.symbol = symbol;

  return createAuthenticatedRequest("positions", params, credentials);
};

// Create user trade request
export const createUserTradeHistoryRequest = async (
  credentials: BinanceAuthCredentials,
  symbol: string,
  startTime?: number,
  endTime?: number,
  limit: number = 100,
  recvWindow: number = 5000
): Promise<any> => {
  const params: Record<string, any> = {
    symbol,
    recvWindow,
    limit
  };

  if (startTime) params.startTime = startTime;
  if (endTime) params.endTime = endTime;

  return createAuthenticatedRequest("userTrades", params, credentials);
};

// Create order cancel request
export const createOrderCancelRequest = async (
  credentials: BinanceAuthCredentials,
  symbol: string,
  orderId?: number,
  origClientOrderId?: string,
  recvWindow: number = 5000
): Promise<any> => {
  const params: Record<string, any> = {
    symbol,
    recvWindow
  };

  if (orderId) params.orderId = orderId;
  if (origClientOrderId) params.origClientOrderId = origClientOrderId;

  return createAuthenticatedRequest("order.cancel", params, credentials);
};

// Create cancel all open orders request
export const createCancelAllOrdersRequest = async (
  credentials: BinanceAuthCredentials,
  symbol: string,
  recvWindow: number = 5000
): Promise<any> => {
  return createAuthenticatedRequest("allOpenOrders.cancel", { symbol, recvWindow }, credentials);
};

// Get the API proxy base URL - Use public domain for proxy requests
const getApiProxyBase = () => {
  // Use your public URL (through Apache reverse proxy)
  return 'https://parabot.fun';
};

// Server-side request helper - Updated to use your proxy server
const serverSideRequest = async (endpoint: string, options: RequestInit = {}): Promise<Response> => {
  const proxyUrl = `${getApiProxyBase()}/api/binance/proxy`;

  console.log('🌐 Making request through your server:', {
    proxyUrl,
    endpoint,
    serverIP: '************'
  });

  const proxyBody = {
    endpoint,
    method: options.method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      'X-MBX-APIKEY': '', // Will be set per request
      ...options.headers as Record<string, string>
    },
    body: options.body,
    params: null // Will be set per request for signed endpoints
  };

  const proxyOptions: RequestInit = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(proxyBody)
  };

  try {
    const response = await fetch(proxyUrl, proxyOptions);

    if (!response.ok) {
      console.error('❌ Proxy request failed:', {
        status: response.status,
        statusText: response.statusText,
        proxyUrl,
        endpoint
      });
    }

    return response;
  } catch (error) {
    console.error('❌ Proxy request error:', error);
    throw error;
  }
};

// Get WebSocket endpoint based on isTestnet flag
export const getBinanceWebSocketEndpoint = (isUserDataStream: boolean, isTestnet: boolean, listenKey?: string): string => {
  if (isUserDataStream && listenKey) {
    return isTestnet
      ? `${BINANCE_FUTURES_API_TESTNET_WS_URL}/ws/${listenKey}`
      : `${BINANCE_FUTURES_API_WS_URL}/ws/${listenKey}`;
  }
  return isTestnet ? BINANCE_FUTURES_TESTNET_WS_URL : BINANCE_FUTURES_WS_URL;
};

export default {
  BINANCE_FUTURES_WS_URL,
  BINANCE_FUTURES_API_WS_URL,
  BINANCE_FUTURES_TESTNET_WS_URL,
  BINANCE_FUTURES_API_TESTNET_WS_URL,
  BINANCE_FUTURES_REST_URL,
  BINANCE_TESTNET_FUTURES_REST_URL,
  STREAM_NAME_24HR,
  STREAM_NAME_TRADE,
  STREAM_NAME_KLINE,
  createSubscriptionMessage,
  createUnsubscriptionMessage,
  process24hrTickerData,
  createAuthenticatedRequest,
  createSessionLogonRequest,
  createOrderPlacementRequest,
  createLeverageSetRequest,
  createMarginTypeSetRequest,
  createPositionModeSetRequest,
  createAccountStatusRequest,
  createAccountBalanceRequest,
  createPositionsRequest,
  createUserTradeHistoryRequest,
  createOrderCancelRequest,
  createCancelAllOrdersRequest,
  getBinanceWebSocketEndpoint
};
