import { BinanceAuthCredentials } from './binanceService';

export class BinanceWebSocketManager {
  public ws: WebSocket | null = null;
  private pingInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private listenKey: string | null = null;
  private isReconnecting: boolean = false;
  private isConnected: boolean = false;
  private networkState: 'online' | 'offline' = 'online';
  private serverUrl: string = 'https://parabot.fun:3001'; // HTTPS olarak güncellendi

  constructor(
    private credentials: BinanceAuthCredentials,
    private onMessage: (data: any) => void,
    private onError: (error: any) => void,
    private testnet: boolean = false
  ) {
    // .cursorrules dosyasına göre, sunucu IP'si ************ ve Binance'de tanımlı
    // Force production mode regardless of testnet flag (unless explicitly testing)
    if (!testnet) {
      this.testnet = false;
    }
    // Initialize network state listeners if in browser environment
    if (typeof window !== 'undefined') {
      this.networkState = navigator.onLine ? 'online' : 'offline';
      window.addEventListener('online', this.handleNetworkChange);
      window.addEventListener('offline', this.handleNetworkChange);
    }
  }

  private handleNetworkChange = () => {
    const previousState = this.networkState;
    const currentState = navigator.onLine ? 'online' : 'offline';
    this.networkState = currentState;

    console.log(`Network state changed from ${previousState} to ${currentState}`);

    if (previousState === 'offline' && currentState === 'online') {
      console.log('Network connection restored, attempting to reconnect WebSocket...');
      this.reconnect(0); // Immediate reconnect attempt when network is restored
    }
  };

  public async connect() {
    if (this.isConnected || this.isReconnecting) {
      console.log('Already connected or reconnecting, ignoring connect request');
      return;
    }

    try {
      this.isReconnecting = true;
      console.log('Connecting to Binance WebSocket via proxy server...');

      this.listenKey = await this.getListenKey();
      if (!this.listenKey) {
        throw new Error('Failed to get listen key');
      }

      this.setupWebSocket();
      this.startPingPong();
      this.startListenKeyRenewal();

      this.isReconnecting = false;
      return true;
    } catch (error) {
      this.isReconnecting = false;
      this.onError(error);
      return false;
    }
  }

  private async getListenKey(): Promise<string> {
    try {
      // .cursorrules dosyasına göre API proxy kullanıyoruz
      const response = await fetch(`${this.serverUrl}/api/binance/proxy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: this.testnet
            ? 'https://testnet.binancefuture.com/fapi/v1/listenKey'
            : 'https://fapi.binance.com/fapi/v1/listenKey',
          method: 'POST',
          headers: {
            'X-MBX-APIKEY': this.credentials.apiKey
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get listen key: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      if (!data.result || !data.result.listenKey) {
        throw new Error('Listen key not found in response: ' + JSON.stringify(data));
      }

      return data.result.listenKey;
    } catch (error) {
      console.error('Error getting listen key:', error);
      throw error;
    }
  }

  private async renewListenKey() {
    if (!this.listenKey) return;

    try {
      // .cursorrules dosyasına göre API proxy kullanıyoruz
      const response = await fetch(`${this.serverUrl}/api/binance/proxy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: this.testnet
            ? `https://testnet.binancefuture.com/fapi/v1/listenKey?listenKey=${this.listenKey}`
            : `https://fapi.binance.com/fapi/v1/listenKey?listenKey=${this.listenKey}`,
          method: 'PUT',
          headers: {
            'X-MBX-APIKEY': this.credentials.apiKey
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to renew listen key: ${response.status} ${response.statusText} - ${errorText}`);
      }

      console.log('Listen key renewed successfully');
    } catch (error) {
      console.error('Error renewing listen key:', error);
      this.onError(error);

      // If we can't renew, try to get a new one and reconnect
      if (this.isConnected) {
        this.reconnect();
      }
    }
  }

  private setupWebSocket() {
    if (!this.listenKey) return;

    // Use WebSocket proxy to connect through our server
    // .cursorrules dosyasına göre ws proxy kullanıyoruz
    const baseUrl = this.testnet
      ? 'wss://stream.binancefuture.com'
      : 'wss://fstream.binance.com';

    const wsUrl = `${baseUrl}/ws/${this.listenKey}`;

    console.log(`Setting up WebSocket connection to ${wsUrl}`);

    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('WebSocket connection opened');
      this.isConnected = true;
      this.subscribe();
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.onMessage(data);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.onError(error);
    };

    this.ws.onclose = (event) => {
      console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);
      this.isConnected = false;

      if (this.networkState === 'online') {
        this.reconnect();
      }
    };
  }

  private subscribe() {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return;

    console.log('Subscribing to Binance channels');

    // Subscribe to public channels with faster price updates
    const subscribeMsg = {
      method: "SUBSCRIBE",
      params: [
        "btcusdt@aggTrade",
        "btcusdt@depth",
        "btcusdt@kline_1m",
        "btcusdt@ticker", // Add ticker for more frequent price updates
        "ethusdt@ticker",  // Add ETH ticker
        "!ticker@arr"     // Add all tickers array (for dashboard)
      ],
      id: 1
    };

    this.ws.send(JSON.stringify(subscribeMsg));
  }

  // Public method to subscribe to specific symbols with higher update frequency
  public subscribeToSymbol(symbol: string) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return false;

    const lowerSymbol = symbol.toLowerCase();
    const subscribeMsg = {
      method: "SUBSCRIBE",
      params: [
        `${lowerSymbol}@aggTrade`,
        `${lowerSymbol}@ticker`
      ],
      id: Date.now()
    };

    console.log(`Subscribing to ${symbol} with higher frequency updates`);
    this.ws.send(JSON.stringify(subscribeMsg));
    return true;
  }

  private startPingPong() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }

    this.pingInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ method: "ping" }));
      }
    }, 30000); // Send ping every 30 seconds
  }

  private startListenKeyRenewal() {
    // Renew listenKey every 45 minutes (before 60 minute expiration)
    setInterval(() => {
      if (this.isConnected) {
        this.renewListenKey();
      }
    }, 45 * 60 * 1000);
  }

  private reconnect(delay = 5000) {
    if (this.isReconnecting) {
      console.log('Already attempting to reconnect, ignoring duplicate request');
      return;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.isReconnecting = true;

    this.reconnectTimeout = setTimeout(async () => {
      console.log(`Attempting to reconnect WebSocket...`);

      try {
        // Close existing connection if any
        if (this.ws) {
          try {
            this.ws.close();
            this.ws = null;
          } catch (err) {
            console.error('Error closing WebSocket during reconnect:', err);
          }
        }

        // Get a new listen key
        const newListenKey = await this.getListenKey();
        if (newListenKey) {
          this.listenKey = newListenKey;
          this.setupWebSocket();
          this.isReconnecting = false;
        } else {
          throw new Error('Failed to get a new listen key for reconnection');
        }
      } catch (err) {
        console.error('WebSocket reconnection failed:', err);
        this.isReconnecting = false;

        // Try again with increased delay
        const nextDelay = Math.min(delay * 1.5, 60000); // Cap at 1 minute
        this.reconnect(nextDelay);
      }
    }, delay);
  }

  public disconnect() {
    console.log('Disconnecting WebSocket...');

    window.removeEventListener('online', this.handleNetworkChange);
    window.removeEventListener('offline', this.handleNetworkChange);

    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.ws) {
      try {
        this.ws.close();
        this.ws = null;
      } catch (err) {
        console.error('Error closing WebSocket:', err);
      }
    }

    this.isConnected = false;
    this.isReconnecting = false;
  }

  public async fetchExchangeInfo() {
    try {
      // .cursorrules dosyasına göre API proxy kullanıyoruz
      const response = await fetch(`${this.serverUrl}/api/binance/proxy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: this.testnet
            ? 'https://testnet.binancefuture.com/fapi/v1/exchangeInfo'
            : 'https://fapi.binance.com/fapi/v1/exchangeInfo',
          method: 'GET'
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch exchange info: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data.result;
    } catch (error) {
      console.error('Error fetching exchange info:', error);
      throw error;
    }
  }
}
