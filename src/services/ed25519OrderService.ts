import { get<PERSON><PERSON><PERSON><PERSON>, getApi<PERSON><PERSON><PERSON>, getEd25519<PERSON>pi<PERSON><PERSON>, getPrivateKeyHex } from '../utils/configLoader';

// Constants
const BINANCE_FUTURES_API_URL = 'https://fapi.binance.com';

// Ed25519 imzalama için Buffer sınıfını kullan
const toHex = (buffer: ArrayBuffer): string => {
    return Array.from(new Uint8Array(buffer))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
};

/**
 * Ed25519 ile imza oluşturur
 * @param message İmzalanacak mesaj
 * @param privateKeyHex Özel anahtar (hex formatında)
 * @returns Imza (hex formatında)
 */
export async function createEd25519Signature(message: string, privateKeyHex: string): Promise<string> {
    try {
        // Hex formatındaki private key'i ArrayBuffer'a çevir
        const privateKeyBytes = new Uint8Array(privateKeyHex.match(/.{1,2}/g)!.map(byte => parseInt(byte, 16)));

        // Web Crypto API ile Ed25519 özel anahtarı oluştur
        const privateKey = await window.crypto.subtle.importKey(
            'raw',
            privateKeyBytes,
            { name: 'Ed25519' },
            false,
            ['sign']
        );

        // Mesajı imzala
        const messageBuffer = new TextEncoder().encode(message);
        const signature = await window.crypto.subtle.sign(
            'Ed25519',
            privateKey,
            messageBuffer
        );

        // İmzayı hex formatına çevir
        return toHex(signature);
    } catch (error) {
        console.error('Ed25519 imzalama hatası:', error);
        throw new Error(`İmza oluşturulamadı: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`);
    }
}

/**
 * SHORT pozisyon açar (MARKET emri)
 * @param symbol İşlem sembolü (örn. BTCUSDT)
 * @param quantity İşlem miktarı
 * @param leverage Kaldıraç (örn. 5)
 * @returns İşlem sonucu
 */
export async function placeShortOrder(
    symbol: string,
    quantity: number,
    leverage: number
): Promise<any> {
    try {
        // API bilgilerini yükle - şimdi Ed25519 API anahtarını kullanıyoruz
        const apiKey = getEd25519ApiKey();
        const privateKeyHex = getPrivateKeyHex();

        if (!apiKey || !privateKeyHex) {
            throw new Error('Ed25519 API kimlik bilgileri bulunamadı. Lütfen src/config/binance-credentials.json dosyasını kontrol edin.');
        }

        console.log(`🔴 SHORT pozisyon açılıyor: ${symbol}, miktar: ${quantity}, kaldıraç: ${leverage}x`);

        // 1. Kaldıracı ayarla
        await setLeverage(symbol, leverage, apiKey, privateKeyHex);

        // 2. MARKET SELL emri ver
        const timestamp = Date.now().toString();
        const params = new URLSearchParams();

        // Zorunlu parametreler
        params.append('symbol', symbol);
        params.append('side', 'SELL');  // SHORT için SELL
        params.append('type', 'MARKET');
        params.append('quantity', quantity.toString());
        params.append('timestamp', timestamp);

        // Opsiyonel parametreler
        params.append('reduceOnly', 'false');  // Yeni pozisyon açıyoruz
        params.append('newOrderRespType', 'RESULT'); // Tam sonuç iste

        // Sorgu parametrelerini oluştur
        const queryString = params.toString();

        // Ed25519 imzası oluştur
        const signature = await createEd25519Signature(queryString, privateKeyHex);

        // İmzayı parametrelere ekle
        params.append('signature', signature);

        // API isteğini gönder
        const response = await fetch(`${BINANCE_FUTURES_API_URL}/fapi/v1/order`, {
            method: 'POST',
            headers: {
                'X-MBX-APIKEY': apiKey,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: params.toString()
        });

        // Yanıt kontrolü
        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ SHORT emir hatası:', errorText);
            throw new Error(`SHORT emir hatası: ${errorText}`);
        }

        // Yanıtı işle
        const data = await response.json();
        console.log('✅ SHORT pozisyon başarıyla açıldı:', data);

        return data;
    } catch (error) {
        console.error('❌ SHORT pozisyon açma hatası:', error);
        throw error;
    }
}

/**
 * Kaldıraç seviyesini ayarlar
 * @param symbol İşlem sembolü
 * @param leverage Kaldıraç seviyesi (1-125 arası)
 * @param apiKey API Key
 * @param privateKeyHex Ed25519 Private Key (hex formatında)
 */
async function setLeverage(
    symbol: string,
    leverage: number,
    apiKey: string,
    privateKeyHex: string
): Promise<any> {
    try {
        console.log(`⚙️ Kaldıraç ayarlanıyor: ${symbol}, ${leverage}x`);

        const timestamp = Date.now().toString();
        const params = new URLSearchParams();

        // Parametreleri oluştur
        params.append('symbol', symbol);
        params.append('leverage', Math.min(125, Math.max(1, Math.floor(leverage))).toString()); // 1-125 arası
        params.append('timestamp', timestamp);

        // Sorgu parametrelerini oluştur
        const queryString = params.toString();

        // Ed25519 imzası oluştur
        const signature = await createEd25519Signature(queryString, privateKeyHex);
        params.append('signature', signature);

        // API isteğini gönder
        const response = await fetch(`${BINANCE_FUTURES_API_URL}/fapi/v1/leverage`, {
            method: 'POST',
            headers: {
                'X-MBX-APIKEY': apiKey,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: params.toString()
        });

        // Yanıt kontrolü
        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Kaldıraç ayarlama hatası:', errorText);
            throw new Error(`Kaldıraç ayarlama hatası: ${errorText}`);
        }

        // Yanıtı işle
        const data = await response.json();
        console.log('✅ Kaldıraç başarıyla ayarlandı:', data);

        return data;
    } catch (error) {
        console.error('❌ Kaldıraç ayarlama hatası:', error);
        throw error;
    }
}

/**
 * Kar Al (Take Profit) ve Zarar Durdur (Stop Loss) emirleri yerleştirir
 * @param symbol İşlem sembolü
 * @param entryPrice Giriş fiyatı
 * @param quantity İşlem miktarı
 * @param tpPercent Kar alma yüzdesi (0.8 = %0.8)
 * @param slPercent Zarar durdurma yüzdesi (1.0 = %1.0)
 */
export async function placeTP_SL(
    symbol: string,
    entryPrice: number,
    quantity: number,
    tpPercent: number = 0.8,
    slPercent: number = 1.0
): Promise<{
    tpOrder: any;
    slOrder: any;
}> {
    try {
        // API bilgilerini yükle - şimdi Ed25519 API anahtarını kullanıyoruz
        const apiKey = getEd25519ApiKey();
        const privateKeyHex = getPrivateKeyHex();

        if (!apiKey || !privateKeyHex) {
            throw new Error('Ed25519 API kimlik bilgileri bulunamadı. Lütfen src/config/binance-credentials.json dosyasını kontrol edin.');
        }

        console.log(`🎯 TP/SL emirleri yerleştiriliyor: ${symbol}, entryPrice: ${entryPrice}, quantity: ${quantity}`);

        // SHORT pozisyon için fiyatları hesapla
        // SHORT için TP fiyatı giriş fiyatından DÜŞÜK olmalı
        const tpPrice = entryPrice * (1 - tpPercent / 100);
        // SHORT için SL fiyatı giriş fiyatından YÜKSEK olmalı
        const slPrice = entryPrice * (1 + slPercent / 100);

        console.log(`📊 Hesaplanan fiyatlar (SHORT pozisyon için):`);
        console.log(`- Giriş Fiyatı: ${entryPrice}`);
        console.log(`- TP Fiyatı (%${tpPercent}): ${tpPrice}`);
        console.log(`- SL Fiyatı (%${slPercent}): ${slPrice}`);

        // 1. Take Profit (TP) emri - SHORT için BUY
        const tpOrder = await placeTPOrder(symbol, 'BUY', quantity, tpPrice, apiKey, privateKeyHex);

        // 2. Stop Loss (SL) emri - SHORT için BUY
        const slOrder = await placeSLOrder(symbol, 'BUY', quantity, slPrice, apiKey, privateKeyHex);

        return {
            tpOrder,
            slOrder
        };
    } catch (error) {
        console.error('❌ TP/SL yerleştirme hatası:', error);
        throw error;
    }
}

/**
 * Take Profit (TP) emri yerleştirir
 */
async function placeTPOrder(
    symbol: string,
    side: 'BUY' | 'SELL',
    quantity: number,
    price: number,
    apiKey: string,
    privateKeyHex: string
): Promise<any> {
    try {
        const timestamp = Date.now().toString();
        const params = new URLSearchParams();

        // Parametreleri oluştur
        params.append('symbol', symbol);
        params.append('side', side); // SHORT kapatmak için BUY
        params.append('type', 'LIMIT');
        params.append('timeInForce', 'GTC'); // Good Till Cancel
        params.append('quantity', quantity.toString());
        params.append('price', price.toString());
        params.append('reduceOnly', 'true'); // Pozisyonu kapat
        params.append('timestamp', timestamp);

        // Sorgu parametrelerini oluştur
        const queryString = params.toString();

        // Ed25519 imzası oluştur
        const signature = await createEd25519Signature(queryString, privateKeyHex);
        params.append('signature', signature);

        // API isteğini gönder
        const response = await fetch(`${BINANCE_FUTURES_API_URL}/fapi/v1/order`, {
            method: 'POST',
            headers: {
                'X-MBX-APIKEY': apiKey,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: params.toString()
        });

        // Yanıt kontrolü
        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ TP emri hatası:', errorText);
            throw new Error(`TP emri hatası: ${errorText}`);
        }

        // Yanıtı işle
        const data = await response.json();
        console.log('✅ TP emri başarıyla yerleştirildi:', data);

        return data;
    } catch (error) {
        console.error('❌ TP emri yerleştirme hatası:', error);
        throw error;
    }
}

/**
 * Stop Loss (SL) emri yerleştirir
 */
async function placeSLOrder(
    symbol: string,
    side: 'BUY' | 'SELL',
    quantity: number,
    stopPrice: number,
    apiKey: string,
    privateKeyHex: string
): Promise<any> {
    try {
        const timestamp = Date.now().toString();
        const params = new URLSearchParams();

        // Parametreleri oluştur
        params.append('symbol', symbol);
        params.append('side', side); // SHORT kapatmak için BUY
        params.append('type', 'STOP_MARKET');
        params.append('quantity', quantity.toString());
        params.append('stopPrice', stopPrice.toString());
        params.append('reduceOnly', 'true'); // Pozisyonu kapat
        params.append('timestamp', timestamp);

        // Sorgu parametrelerini oluştur
        const queryString = params.toString();

        // Ed25519 imzası oluştur
        const signature = await createEd25519Signature(queryString, privateKeyHex);
        params.append('signature', signature);

        // API isteğini gönder
        const response = await fetch(`${BINANCE_FUTURES_API_URL}/fapi/v1/order`, {
            method: 'POST',
            headers: {
                'X-MBX-APIKEY': apiKey,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: params.toString()
        });

        // Yanıt kontrolü
        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ SL emri hatası:', errorText);
            throw new Error(`SL emri hatası: ${errorText}`);
        }

        // Yanıtı işle
        const data = await response.json();
        console.log('✅ SL emri başarıyla yerleştirildi:', data);

        return data;
    } catch (error) {
        console.error('❌ SL emri yerleştirme hatası:', error);
        throw error;
    }
} 