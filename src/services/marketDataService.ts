import { onWsEvent, subscribeToStream } from './websocket';

// Debug bilgisi
console.log('📊 Market Data Service yükleniyor...');

// Piyasa verisi için callback tipini tanımla
export type MarketDataCallback = (data: any) => void;

// Tiker verisi
export interface TickerData {
    symbol: string;
    price: number;
    bid: number;
    ask: number;
    volume: number;
    timestamp: number;
}

// Son ticker verilerini sakla
const lastTickerData = new Map<string, TickerData>();

// Ticker verisi callback fonksiyonları
const tickerCallbacks = new Map<string, Set<(data: TickerData) => void>>();

// Sembol için ticker verisi al
export function getTickerData(symbol: string): TickerData | null {
    return lastTickerData.get(symbol) || null;
}

// Ticker verisi dinle
export function subscribeToTickerData(
    symbol: string,
    callback: (data: TickerData) => void
): () => void {
    try {
        console.log(`📊 ${symbol} için ticker verisi aboneliği başlatılıyor...`);

        // Sembol için callback listesi al veya oluştur
        const callbacks = tickerCallbacks.get(symbol) || new Set();
        callbacks.add(callback);
        tickerCallbacks.set(symbol, callbacks);

        // WebSocket bookTicker stream'ine abone ol
        const unsubscribe = subscribeToStream(symbol, 'bookTicker');

        // bookTicker olayını dinle
        const wsListener = onWsEvent(`bookTicker_${symbol.toLowerCase()}`, (data) => {
            // BookTicker verisini işle
            const tickerData: TickerData = {
                symbol: data.s,
                price: parseFloat(data.c || data.a), // Kapanış fiyatı yoksa ask kullan
                bid: parseFloat(data.b),
                ask: parseFloat(data.a),
                volume: parseFloat(data.v || '0'),
                timestamp: Date.now()
            };

            // Son veriyi güncelle
            lastTickerData.set(symbol, tickerData);

            // Callback'leri çağır
            const callbacks = tickerCallbacks.get(symbol);
            if (callbacks) {
                callbacks.forEach(cb => cb(tickerData));
            }
        });

        // Aboneliği kaldır fonksiyonu döndür
        return () => {
            unsubscribe();

            // WebSocket dinleyicisini kaldır
            wsListener();

            // Callback'i kaldır
            const callbacks = tickerCallbacks.get(symbol);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    tickerCallbacks.delete(symbol);
                }
            }

            console.log(`📊 ${symbol} için ticker verisi aboneliği durduruldu`);
        };
    } catch (error) {
        console.error(`❌ ${symbol} için ticker verisi aboneliği başlatılırken hata:`, error);
        return () => { }; // Boş fonksiyon döndür
    }
}

// Mum (kline) verisi dinle
export function subscribeToKlineData(
    symbol: string,
    interval: string,
    callback: MarketDataCallback
): () => void {
    try {
        console.log(`📊 ${symbol} için ${interval} kline verisi aboneliği başlatılıyor...`);

        // WebSocket kline stream'ine abone ol
        const unsubscribe = subscribeToStream(symbol, `kline_${interval}`);

        // kline olayını dinle
        const wsListener = onWsEvent(`kline_${symbol.toLowerCase()}_${interval}`, (data) => {
            callback(data);
        });

        // Aboneliği kaldır fonksiyonu döndür
        return () => {
            unsubscribe();
            wsListener();
            console.log(`📊 ${symbol} için ${interval} kline verisi aboneliği durduruldu`);
        };
    } catch (error) {
        console.error(`❌ ${symbol} için kline verisi aboneliği başlatılırken hata:`, error);
        return () => { }; // Boş fonksiyon döndür
    }
}

// Derinlik verisi dinle
export function subscribeToDepth(
    symbol: string,
    callback: MarketDataCallback
): () => void {
    try {
        console.log(`📊 ${symbol} için derinlik verisi aboneliği başlatılıyor...`);

        // WebSocket depth stream'ine abone ol
        const unsubscribe = subscribeToStream(symbol, 'depth');

        // depth olayını dinle
        const wsListener = onWsEvent(`depth_${symbol.toLowerCase()}`, (data) => {
            callback(data);
        });

        // Aboneliği kaldır fonksiyonu döndür
        return () => {
            unsubscribe();
            wsListener();
            console.log(`📊 ${symbol} için derinlik verisi aboneliği durduruldu`);
        };
    } catch (error) {
        console.error(`❌ ${symbol} için derinlik verisi aboneliği başlatılırken hata:`, error);
        return () => { }; // Boş fonksiyon döndür
    }
}

// İşlem verilerine abone ol
export function subscribeToTrades(
    symbol: string,
    callback: MarketDataCallback
): () => void {
    try {
        console.log(`📊 ${symbol} için işlem verisi aboneliği başlatılıyor...`);

        // WebSocket trade stream'ine abone ol
        const unsubscribe = subscribeToStream(symbol, 'trade');

        // trade olayını dinle
        const wsListener = onWsEvent(`trade_${symbol.toLowerCase()}`, (data) => {
            callback(data);
        });

        // Aboneliği kaldır fonksiyonu döndür
        return () => {
            unsubscribe();
            wsListener();
            console.log(`📊 ${symbol} için işlem verisi aboneliği durduruldu`);
        };
    } catch (error) {
        console.error(`❌ ${symbol} için işlem verisi aboneliği başlatılırken hata:`, error);
        return () => { }; // Boş fonksiyon döndür
    }
} 
