import { HMAC_API_KEY, HMAC_SECRET_KEY } from '../config/api';
import { createSignature } from '../utils/signature';
import { getMode, isRealMode } from '../store/modeStore';
import { onWsEvent } from './websocket';

// Debug bilgisi
console.log('📊 Order Service yükleniyor...');

// Sipariş güncelleme callback tipini tanımla
export type OrderUpdateCallback = (data: any) => void;

/**
 * Yeni bir emir oluştur
 * @param symbol İşlem sembolü (örn. BTCUSDT)
 * @param side İşlem yönü (BUY veya SELL)
 * @param type Emir tipi (MARKET, LIMIT, vb.)
 * @param quantity İşlem miktarı
 * @param price Fiyat (LIMIT emirler için)
 * @param options Diğer seçenekler
 * @returns Emir oluşturma sonucu
 */
export async function placeOrder(
    symbol: string,
    side: 'BUY' | 'SELL',
    type: 'MARKET' | 'LIMIT' | 'STOP' | 'TAKE_PROFIT',
    quantity: number,
    price?: number,
    options?: {
        timeInForce?: 'GTC' | 'IOC' | 'FOK' | 'GTX';
        stopPrice?: number;
        reduceOnly?: boolean;
        closePosition?: boolean;
        positionSide?: 'BOTH' | 'LONG' | 'SHORT';
    }
): Promise<any> {
    try {
        // Real mod kontrolü
        if (!isRealMode()) {
            console.log(`🔷 [${getMode()}] Emir oluşturuluyor: ${side} ${quantity} ${symbol} @ ${price || 'MARKET'}`);
            return {
                symbol,
                orderId: Math.floor(Math.random() * 1000000),
                status: 'NEW',
                clientOrderId: `manual_${Date.now()}`,
                price: price?.toString() || '',
                avgPrice: '0',
                origQty: quantity.toString(),
                executedQty: '0',
                cumQty: '0',
                cumQuote: '0',
                timeInForce: options?.timeInForce || 'GTC',
                type,
                reduceOnly: options?.reduceOnly || false,
                closePosition: options?.closePosition || false,
                side,
                positionSide: options?.positionSide || 'BOTH',
                stopPrice: options?.stopPrice?.toString() || '',
                workingType: 'CONTRACT_PRICE',
                priceProtect: false,
                origType: type,
                updateTime: Date.now(),
            };
        }

        // Zaman damgası oluştur
        const timestamp = Date.now();

        // URLSearchParams ile parametreleri oluştur
        const params = new URLSearchParams();
        params.append('symbol', symbol);
        params.append('side', side);
        params.append('type', type);

        if (type === 'MARKET') {
            params.append('quantity', quantity.toString());
        } else {
            if (price) {
                params.append('price', price.toString());
            }
            params.append('quantity', quantity.toString());
        }

        // Opsiyonel parametreler
        if (options) {
            if (options.timeInForce && type !== 'MARKET') {
                params.append('timeInForce', options.timeInForce);
            }

            if (options.stopPrice) {
                params.append('stopPrice', options.stopPrice.toString());
            }

            if (options.reduceOnly !== undefined) {
                params.append('reduceOnly', options.reduceOnly.toString());
            }

            if (options.closePosition !== undefined) {
                params.append('closePosition', options.closePosition.toString());
            }

            if (options.positionSide) {
                params.append('positionSide', options.positionSide);
            }
        }

        // Zaman damgası ekle
        params.append('timestamp', timestamp.toString());

        // İmza oluştur
        const signature = createSignature(params.toString(), HMAC_SECRET_KEY);
        params.append('signature', signature);

        // Siparişi oluştur
        console.log(`🚀 Emir gönderiliyor: ${side} ${quantity} ${symbol} @ ${price || 'MARKET'}`);

        const response = await fetch('https://fapi.binance.com/fapi/v1/order', {
            method: 'POST',
            headers: {
                'X-MBX-APIKEY': HMAC_API_KEY,
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString(),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Emir oluşturulamadı:', errorText);
            throw new Error(`Emir oluşturma hatası: ${errorText}`);
        }

        const responseData = await response.json();
        console.log('✅ Emir başarıyla oluşturuldu:', responseData);
        return responseData;
    } catch (error) {
        console.error('❌ Emir oluşturulurken hata:', error);
        throw error;
    }
}

/**
 * Emri iptal et
 * @param symbol İşlem sembolü
 * @param orderId Emir ID'si
 * @returns İptal sonucu
 */
export async function cancelOrder(
    symbol: string,
    orderId: number
): Promise<any> {
    try {
        // Real mod kontrolü
        if (!isRealMode()) {
            console.log(`🔷 [${getMode()}] Emir iptal ediliyor: ${orderId} (${symbol})`);
            return {
                symbol,
                orderId,
                status: 'CANCELED',
                clientOrderId: `manual_${Date.now()}`,
            };
        }

        // Zaman damgası oluştur
        const timestamp = Date.now();

        // URLSearchParams ile parametreleri oluştur
        const params = new URLSearchParams();
        params.append('symbol', symbol);
        params.append('orderId', orderId.toString());
        params.append('timestamp', timestamp.toString());

        // İmza oluştur
        const signature = createSignature(params.toString(), HMAC_SECRET_KEY);
        params.append('signature', signature);

        // Siparişi iptal et
        console.log(`🚫 Emir iptal ediliyor: ${orderId} (${symbol})`);

        const response = await fetch('https://fapi.binance.com/fapi/v1/order', {
            method: 'DELETE',
            headers: {
                'X-MBX-APIKEY': HMAC_API_KEY,
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString(),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Emir iptal edilemedi:', errorText);
            throw new Error(`Emir iptal hatası: ${errorText}`);
        }

        const responseData = await response.json();
        console.log('✅ Emir başarıyla iptal edildi:', responseData);
        return responseData;
    } catch (error) {
        console.error('❌ Emir iptal edilirken hata:', error);
        throw error;
    }
}

/**
 * Açık emirleri al
 * @param symbol İşlem sembolü
 * @returns Açık emirler listesi
 */
export async function getOpenOrders(symbol?: string): Promise<any[]> {
    try {
        // Real mod kontrolü
        if (!isRealMode()) {
            console.log(`🔷 [${getMode()}] Açık emirler alınıyor ${symbol ? `(${symbol})` : ''}`);
            return [];
        }

        // Zaman damgası oluştur
        const timestamp = Date.now();

        // URLSearchParams ile parametreleri oluştur
        const params = new URLSearchParams();

        if (symbol) {
            params.append('symbol', symbol);
        }

        params.append('timestamp', timestamp.toString());

        // İmza oluştur
        const signature = createSignature(params.toString(), HMAC_SECRET_KEY);
        params.append('signature', signature);

        // Açık emirleri al
        console.log(`📋 Açık emirler alınıyor ${symbol ? `(${symbol})` : ''}`);

        const response = await fetch(`https://fapi.binance.com/fapi/v1/openOrders?${params.toString()}`, {
            method: 'GET',
            headers: {
                'X-MBX-APIKEY': HMAC_API_KEY,
            },
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Açık emirler alınamadı:', errorText);
            throw new Error(`Açık emirler alma hatası: ${errorText}`);
        }

        const responseData = await response.json();
        console.log('✅ Açık emirler başarıyla alındı:', responseData);
        return responseData;
    } catch (error) {
        console.error('❌ Açık emirler alınırken hata:', error);
        return [];
    }
}

/**
 * Tüm açık pozisyonları kapat
 * @param symbol İşlem sembolü
 * @returns Kapama sonucu
 */
export async function closeAllPositions(symbol: string): Promise<any> {
    try {
        // Real mod kontrolü
        if (!isRealMode()) {
            console.log(`🔷 [${getMode()}] Tüm pozisyonlar kapatılıyor: ${symbol}`);
            return { success: true, symbol };
        }

        // Zaman damgası oluştur
        const timestamp = Date.now();

        // URLSearchParams ile parametreleri oluştur
        const params = new URLSearchParams();
        params.append('symbol', symbol);
        params.append('timestamp', timestamp.toString());

        // İmza oluştur
        const signature = createSignature(params.toString(), HMAC_SECRET_KEY);
        params.append('signature', signature);

        // Tüm pozisyonları kapat
        console.log(`🔴 Tüm pozisyonlar kapatılıyor: ${symbol}`);

        const response = await fetch('https://fapi.binance.com/fapi/v1/allOpenOrders', {
            method: 'DELETE',
            headers: {
                'X-MBX-APIKEY': HMAC_API_KEY,
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params.toString(),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Pozisyonlar kapatılamadı:', errorText);
            throw new Error(`Pozisyon kapatma hatası: ${errorText}`);
        }

        const responseData = await response.json();
        console.log('✅ Tüm pozisyonlar başarıyla kapatıldı:', responseData);
        return responseData;
    } catch (error) {
        console.error('❌ Pozisyonlar kapatılırken hata:', error);
        throw error;
    }
}

/**
 * Sipariş güncellemelerini dinle
 */
export function listenToOrderUpdates(callback: OrderUpdateCallback): () => void {
    return onWsEvent('orderUpdate', callback);
} 
