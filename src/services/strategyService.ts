import { placeOrder } from './orderService';
import { getMode } from '../store/modeStore';

// Debug bilgisi
console.log('🧠 Strategy Service yükleniyor...');

// Strateji türleri
export type StrategyType = 'TREND_FOLLOW' | 'MEAN_REVERSION' | 'BREAKOUT';

// Strateji durumu
export interface StrategyState {
    symbol: string;
    strategyType: StrategyType;
    isActive: boolean;
    params: Record<string, any>;
    lastUpdated: number;
}

// Aktif stratejiler
const activeStrategies = new Map<string, StrategyState>();

// Strateji başlat
export function startStrategy(
    symbol: string,
    strategyType: StrategyType,
    params: Record<string, any>
): boolean {
    try {
        console.log(`🚀 ${symbol} için ${strategyType} stratejisi başlatılıyor`, params);

        // Strateji durumunu kaydet
        activeStrategies.set(symbol, {
            symbol,
            strategyType,
            isActive: true,
            params,
            lastUpdated: Date.now()
        });

        return true;
    } catch (error) {
        console.error('❌ Strateji başlatılırken hata:', error);
        return false;
    }
}

// Strateji durdur
export function stopStrategy(symbol: string): boolean {
    try {
        // Eğer strateji aktifse durdur
        const strategy = activeStrategies.get(symbol);

        if (strategy) {
            console.log(`🛑 ${symbol} için ${strategy.strategyType} stratejisi durduruluyor`);

            strategy.isActive = false;
            strategy.lastUpdated = Date.now();
            activeStrategies.set(symbol, strategy);

            return true;
        }

        return false;
    } catch (error) {
        console.error('❌ Strateji durdurulurken hata:', error);
        return false;
    }
}

// Uzun pozisyon aç
export async function openLongPosition(
    symbol: string,
    quantity: number,
    reason: string = 'MANUAL'
): Promise<any> {
    try {
        console.log(`📈 ${symbol} için LONG pozisyon açılıyor (${quantity})...`);

        const result = await placeOrder(symbol, 'BUY', 'MARKET', quantity);

        console.log(`✅ LONG pozisyon açıldı: ${symbol}, ${quantity}, Neden: ${reason}`);
        return result;
    } catch (error) {
        console.error('❌ LONG pozisyon açılırken hata:', error);
        throw error;
    }
}

// Kısa pozisyon aç
export async function openShortPosition(
    symbol: string,
    quantity: number,
    reason: string = 'MANUAL'
): Promise<any> {
    try {
        console.log(`📉 ${symbol} için SHORT pozisyon açılıyor (${quantity})...`);

        const result = await placeOrder(symbol, 'SELL', 'MARKET', quantity);

        console.log(`✅ SHORT pozisyon açıldı: ${symbol}, ${quantity}, Neden: ${reason}`);
        return result;
    } catch (error) {
        console.error('❌ SHORT pozisyon açılırken hata:', error);
        throw error;
    }
}

// Strateji durumunu al
export function getStrategyState(symbol: string): StrategyState | null {
    return activeStrategies.get(symbol) || null;
}

// Trend takip stratejisi
export async function trendFollowStrategy(
    symbol: string,
    price: number,
    trend: 'up' | 'down',
    quantity: number
): Promise<any> {
    try {
        console.log(`📈 Trend takip stratejisi çalışıyor: ${symbol}, ${trend}, ${quantity}`);

        // İşlem yönünü belirle
        const side = trend === 'up' ? 'BUY' : 'SELL';

        // Market emri oluştur
        console.log(`📊 ${getMode()} modunda ${side} emri gönderiliyor: ${quantity} ${symbol} @ ${price}`);
        return await placeOrder(symbol, side, 'MARKET', quantity);
    } catch (error) {
        console.error('❌ Trend takip stratejisi hatası:', error);
        throw error;
    }
}

// Ortalama dönüş stratejisi
export async function meanReversionStrategy(
    symbol: string,
    price: number,
    averagePrice: number,
    deviation: number,
    quantity: number
): Promise<any> {
    try {
        console.log(`📉 Ortalama dönüş stratejisi çalışıyor: ${symbol}, ${price}, ${averagePrice}, ${deviation}`);

        // Sapma yüzdesini hesapla
        const priceDifference = price - averagePrice;
        const percentDeviation = (priceDifference / averagePrice) * 100;

        // Sapma eşiği kontrolü
        if (Math.abs(percentDeviation) >= deviation) {
            // Fiyat ortalamanın üzerindeyse sat, altındaysa al
            const side = price > averagePrice ? 'SELL' : 'BUY';

            console.log(`📊 Ortalama dönüş sinyali: ${side} (Sapma: ${percentDeviation.toFixed(2)}%)`);
            return await placeOrder(symbol, side, 'MARKET', quantity);
        } else {
            console.log(`ℹ️ Sapma yetersiz (${percentDeviation.toFixed(2)}%), işlem yapılmadı`);
            return { success: false, message: 'Sapma yetersiz, işlem yapılmadı' };
        }
    } catch (error) {
        console.error('❌ Ortalama dönüş stratejisi hatası:', error);
        throw error;
    }
}

// Fiyat kırılma stratejisi
export async function breakoutStrategy(
    symbol: string,
    price: number,
    resistance: number,
    support: number,
    quantity: number
): Promise<any> {
    try {
        console.log(`🚀 Kırılma stratejisi çalışıyor: ${symbol}, ${price}, R:${resistance}, S:${support}`);

        // Direnç veya destek kırılımı kontrolü
        if (price > resistance) {
            // Direnç kırıldı, al
            console.log(`📊 Direnç kırıldı (${resistance}), alış sinyali`);
            return await placeOrder(symbol, 'BUY', 'MARKET', quantity);
        } else if (price < support) {
            // Destek kırıldı, sat
            console.log(`📊 Destek kırıldı (${support}), satış sinyali`);
            return await placeOrder(symbol, 'SELL', 'MARKET', quantity);
        } else {
            console.log(`ℹ️ Fiyat (${price}) aralıkta, işlem yapılmadı`);
            return { success: false, message: 'Fiyat aralıkta, işlem yapılmadı' };
        }
    } catch (error) {
        console.error('❌ Kırılma stratejisi hatası:', error);
        throw error;
    }
} 
