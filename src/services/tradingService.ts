import { initUserDataStream, closeWebSocket } from './websocket';
import { StrategyType, startStrategy, stopStrategy } from './strategyService';
import { getMode } from '../store/modeStore';

// Debug bilgisi
console.log('💼 Trading Service yükleniyor...');

// Trading servisi durumu
export interface TradingServiceState {
    isInitialized: boolean;
    isRunning: boolean;
    activeSymbols: string[];
    activeStrategies: string[];
}

// Servis durumu
let serviceState: TradingServiceState = {
    isInitialized: false,
    isRunning: false,
    activeSymbols: [],
    activeStrategies: []
};

// Trading servisini başlat
export async function initializeTradingService(): Promise<boolean> {
    try {
        if (serviceState.isInitialized) {
            console.warn('⚠️ Trading servisi zaten başlatılmış!');
            return true;
        }

        console.log('🚀 Trading servisi başlatılıyor...');

        // WebSocket servisini başlat
        const wsInitialized = await initUserDataStream();

        if (!wsInitialized) {
            console.error('❌ WebSocket servisi başlatılamadı');
            return false;
        }

        // Servis durumunu güncelle
        serviceState.isInitialized = true;
        console.log('✅ Trading servisi başlatıldı');

        return true;
    } catch (error) {
        console.error('❌ Trading servisi başlatılırken hata:', error);
        // Hata durumunda servisleri durdur
        shutdownTradingService();
        return false;
    }
}

// Trading servisini durdur
export function shutdownTradingService(): void {
    try {
        if (!serviceState.isInitialized) {
            console.warn('⚠️ Trading servisi zaten durdurulmuş!');
            return;
        }

        console.log('🛑 Trading servisi durduruluyor...');

        // Aktif stratejileri durdur
        stopAllTradingStrategies();

        // WebSocket bağlantısını kapat
        closeWebSocket();

        // Servis durumunu güncelle
        serviceState = {
            isInitialized: false,
            isRunning: false,
            activeSymbols: [],
            activeStrategies: []
        };

        console.log('✅ Trading servisi durduruldu');
    } catch (error) {
        console.error('❌ Trading servisi durdurulurken hata:', error);
    }
}

// Strateji başlat
export function startTradingStrategy(
    symbol: string,
    strategyType: StrategyType,
    params: Record<string, any>
): boolean {
    try {
        if (!serviceState.isInitialized) {
            console.error('❌ Trading servisi başlatılmamış!');
            return false;
        }

        console.log(`📈 ${symbol} için ${strategyType} stratejisi başlatılıyor...`);

        // Stratejiyi başlat
        const success = startStrategy(symbol, strategyType, params);

        if (!success) {
            console.error(`❌ ${symbol} için strateji başlatılamadı`);
            return false;
        }

        // Aktif sembolleri güncelle
        if (!serviceState.activeSymbols.includes(symbol)) {
            serviceState.activeSymbols.push(symbol);
        }

        // Aktif stratejileri güncelle
        if (!serviceState.activeStrategies.includes(strategyType)) {
            serviceState.activeStrategies.push(strategyType);
        }

        // Servisi çalışıyor olarak işaretle
        serviceState.isRunning = true;

        console.log(`✅ ${symbol} için ${strategyType} stratejisi başlatıldı (${getMode()} modunda)`);
        return true;
    } catch (error) {
        console.error('❌ Trading stratejisi başlatılırken hata:', error);
        return false;
    }
}

// Sembol için stratejiyi durdur
export function stopTradingStrategy(symbol: string): boolean {
    try {
        if (!serviceState.isInitialized) {
            console.error('❌ Trading servisi başlatılmamış!');
            return false;
        }

        // Stratejiyi durdur
        const success = stopStrategy(symbol);

        if (!success) {
            console.error(`❌ ${symbol} için strateji durdurulamadı`);
            return false;
        }

        // Aktif sembolleri güncelle
        serviceState.activeSymbols = serviceState.activeSymbols.filter(s => s !== symbol);

        // Hiç aktif sembol kalmadıysa servisi durdur
        if (serviceState.activeSymbols.length === 0) {
            serviceState.isRunning = false;
            serviceState.activeStrategies = [];
        }

        console.log(`✅ ${symbol} için trading stratejisi durduruldu`);
        return true;
    } catch (error) {
        console.error('❌ Trading stratejisi durdurulurken hata:', error);
        return false;
    }
}

// Tüm stratejileri durdur
export function stopAllTradingStrategies(): void {
    try {
        if (!serviceState.isInitialized) {
            console.error('❌ Trading servisi başlatılmamış!');
            return;
        }

        // Tüm sembollerin stratejilerini durdur
        for (const symbol of serviceState.activeSymbols) {
            stopStrategy(symbol);
        }

        // Servis durumunu güncelle
        serviceState.activeSymbols = [];
        serviceState.activeStrategies = [];
        serviceState.isRunning = false;

        console.log('✅ Tüm trading stratejileri durduruldu');
    } catch (error) {
        console.error('❌ Tüm trading stratejileri durdurulurken hata:', error);
    }
}

// Servis durumunu al
export function getTradingServiceState(): TradingServiceState {
    return { ...serviceState };
}

// Trend takip stratejisi için varsayılan parametreler
export function createTrendFollowParams(
    symbol: string,
    quantity: number = 0.001
): Record<string, any> {
    return {
        symbol,
        quantity,
        period: 14,
        oversold: 30,
        overbought: 70,
        interval: '15m'
    };
}

// Ortalama dönüş stratejisi için varsayılan parametreler
export function createMeanReversionParams(
    symbol: string,
    quantity: number = 0.001
): Record<string, any> {
    return {
        symbol,
        quantity,
        lookbackPeriod: 20,
        deviationThreshold: 2.0, // standart sapma
        interval: '15m'
    };
}

// Kırılma stratejisi için varsayılan parametreler
export function createBreakoutParams(
    symbol: string,
    quantity: number = 0.001
): Record<string, any> {
    return {
        symbol,
        quantity,
        lookbackPeriod: 24, // son 24 bar
        resistanceThreshold: 0.5, // %0.5
        supportThreshold: 0.5, // %0.5
        interval: '15m'
    };
} 
