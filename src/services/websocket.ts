import { EventEmitter } from 'eventemitter3';
import { HMAC_API_KEY } from '../config/api';
import { createSignature } from '../utils/signature';
import { getAccountInfo } from './accountService';
import { setMode } from '../store/modeStore';
import { AccountInfo, UserAccountStream } from '../types/accountTypes';

// EventEmitter örneği oluştur
const wsEmitter = new EventEmitter();

// WebSocket bağlantısı
let accountWs: WebSocket | null = null;
let marketWs: WebSocket | null = null;  // Market verileri için ayrı WS bağlantısı
let listenKey: string | null = null;
let lastPingTime = 0;
let reconnectAttempts = 0;
let reconnectTimeout: NodeJS.Timeout | null = null;
let hasAccountInfo = false;
let hasRealAccountData = false;

// WebSocket durumu
export const wsStatus = {
    isConnected: false,
    hasAccountInfo: false,
    hasRealAccountData: false
};

// Debug bilgisi
console.log('🔄 WebSocket modülü yükleniyor...');

// WebSocket olaylarını dinle
export function onWsEvent(event: string, listener: (...args: any[]) => void) {
    wsEmitter.on(event, listener);
    return () => wsEmitter.off(event, listener);
}

// Hesap bilgilerini alındığında işle
export function handleAccountInfo(accountInfo: AccountInfo | null, error?: any) {
    if (accountInfo) {
        console.log('✅ Hesap bilgileri başarıyla alındı');
        wsStatus.hasAccountInfo = true;
        wsStatus.hasRealAccountData = true;
        wsEmitter.emit('accountInfo', accountInfo);
    } else {
        console.error('❌ Hesap bilgileri alınamadı:', error);
        wsStatus.hasAccountInfo = false;
    }
    console.log('🔮 Mode Determination Logic:');
    console.log(wsStatus);
}

// Hesap bilgilerini manual olarak set et (demo modu için)
export function setManualAccountInfo(accountInfo: AccountInfo) {
    console.log('👨‍💼 Manual hesap bilgileri ayarlandı');
    wsEmitter.emit('accountInfo', accountInfo);
    setMode('MANUAL');
}

// ListenKey al ve WebSocket bağlantısını başlat
export async function initUserDataStream(): Promise<boolean> {
    try {
        if (accountWs) {
            console.log('🔄 Mevcut WebSocket bağlantısı kapatılıyor...');
            accountWs.close();
            accountWs = null;
        }

        // ListenKey al - örnek dosyadaki gibi basit yap
        console.log('🔑 ListenKey alınıyor...');
        const response = await fetch('https://fapi.binance.com/fapi/v1/listenKey', {
            method: 'POST',
            headers: { 'X-MBX-APIKEY': HMAC_API_KEY }
        });

        const data = await response.json();

        if (!data.listenKey) {
            console.error('❌ ListenKey alınamadı:', data);
            return false;
        }

        listenKey = data.listenKey;
        console.log(`🔑 ListenKey alındı: ${listenKey.substring(0, 10)}...`);

        // WebSocket bağlantısını başlat
        startUserDataStream(listenKey);

        // Market verileri için WebSocket bağlantısını başlat
        startMarketDataStream();

        // Hesap bilgilerini REST API ile al
        getAccountInfo(handleAccountInfo);

        return true;
    } catch (error) {
        console.error('❌ WebSocket başlatılırken hata:', error);
        return false;
    }
}

// Kullanıcı verisi WebSocket bağlantısını kur
function startUserDataStream(listenKey: string) {
    try {
        const wsUrl = `wss://fstream.binance.com/ws/${listenKey}`;
        console.log(`🔌 WebSocket bağlantısı başlatılıyor: ${wsUrl.substring(0, 30)}...`);

        accountWs = new WebSocket(wsUrl);

        // Bağlantı açıldığında
        accountWs.onopen = () => {
            console.log('✅ WebSocket bağlantısı kuruldu');
            wsStatus.isConnected = true;
            reconnectAttempts = 0;
            lastPingTime = Date.now();

            // 30 dakikada bir listenKey'i yenile
            startListenKeyKeepAlive();

            // Hesap bilgilerini REST API ile al
            getAccountInfo(handleAccountInfo);

            wsEmitter.emit('connected');
        };

        // Mesaj geldiğinde
        accountWs.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            } catch (error) {
                console.error('❌ WebSocket mesajı işlenirken hata:', error);
            }
        };

        // Hata olduğunda
        accountWs.onerror = (error) => {
            console.error('❌ WebSocket hatası:', error);
            wsEmitter.emit('error', error);
        };

        // Bağlantı kapandığında
        accountWs.onclose = () => {
            console.log('🔌 WebSocket bağlantısı kapandı');
            wsStatus.isConnected = false;
            wsEmitter.emit('disconnected');

            // Yeniden bağlanmayı dene
            if (!reconnectTimeout) {
                const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
                console.log(`🔄 ${delay}ms sonra yeniden bağlanılacak (Deneme: ${reconnectAttempts + 1})`);

                reconnectTimeout = setTimeout(() => {
                    reconnectTimeout = null;
                    reconnectAttempts++;
                    initUserDataStream();
                }, delay);
            }
        };
    } catch (error) {
        console.error('❌ WebSocket oluşturulurken hata:', error);
    }
}

// Market verileri için WebSocket bağlantısını kur
function startMarketDataStream() {
    try {
        const wsUrl = `wss://fstream.binance.com/ws`;
        console.log(`🔌 Market WebSocket bağlantısı başlatılıyor: ${wsUrl}`);

        marketWs = new WebSocket(wsUrl);

        // Bağlantı açıldığında
        marketWs.onopen = () => {
            console.log('✅ Market WebSocket bağlantısı kuruldu');

            // Varsayılan abonelikler
            subscribeToMarketStream('BTCUSDT@bookTicker');
            subscribeToMarketStream('BTCUSDT@kline_1m');
        };

        // Mesaj geldiğinde
        marketWs.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                handleMarketStreamMessage(data);
            } catch (error) {
                console.error('❌ Market WebSocket mesajı işlenirken hata:', error);
            }
        };

        // Hata olduğunda
        marketWs.onerror = (error) => {
            console.error('❌ Market WebSocket hatası:', error);
        };

        // Bağlantı kapandığında
        marketWs.onclose = () => {
            console.log('🔌 Market WebSocket bağlantısı kapandı');

            // 5 saniye sonra yeniden bağlan
            setTimeout(() => {
                startMarketDataStream();
            }, 5000);
        };
    } catch (error) {
        console.error('❌ Market WebSocket oluşturulurken hata:', error);
    }
}

// Market stream'e abone ol
function subscribeToMarketStream(stream: string | string[]) {
    if (!marketWs || marketWs.readyState !== WebSocket.OPEN) {
        console.error('❌ Market WebSocket bağlantısı açık değil!');
        return;
    }

    const streams = Array.isArray(stream) ? stream : [stream];
    const id = Date.now();

    const subscribeMsg = {
        method: 'SUBSCRIBE',
        params: streams,
        id
    };

    console.log(`📊 Market stream aboneliği: ${streams.join(', ')}`);
    marketWs.send(JSON.stringify(subscribeMsg));
}

// Market stream aboneliğini kaldır
function unsubscribeFromMarketStream(stream: string | string[]) {
    if (!marketWs || marketWs.readyState !== WebSocket.OPEN) {
        console.error('❌ Market WebSocket bağlantısı açık değil!');
        return;
    }

    const streams = Array.isArray(stream) ? stream : [stream];
    const id = Date.now();

    const unsubscribeMsg = {
        method: 'UNSUBSCRIBE',
        params: streams,
        id
    };

    console.log(`📊 Market stream aboneliği iptal: ${streams.join(', ')}`);
    marketWs.send(JSON.stringify(unsubscribeMsg));
}

// Market stream'inden gelen mesajları işle
function handleMarketStreamMessage(data: any) {
    // Abonelik yanıtı
    if (data.id) {
        console.log(`📊 Market stream abonelik yanıtı: ${JSON.stringify(data)}`);
        return;
    }

    // BookTicker mesajı
    if (data.e === 'bookTicker') {
        wsEmitter.emit(`bookTicker_${data.s.toLowerCase()}`, data);
        return;
    }

    // Kline mesajı
    if (data.e === 'kline') {
        wsEmitter.emit(`kline_${data.s.toLowerCase()}_${data.k.i}`, data);
        return;
    }

    // Kullanıcı işlemleri
    if (data.e === 'ORDER_TRADE_UPDATE') {
        wsEmitter.emit('userTrades', data);
        return;
    }

    // Diğer mesajlar
    console.log('📨 Bilinmeyen market stream mesajı:', data);
}

// WebSocket mesajını işle
function handleWebSocketMessage(data: any) {
    // Mesaj türüne göre işle
    if (data.e === 'ACCOUNT_UPDATE') {
        console.log('💰 Hesap güncellemesi alındı');
        wsEmitter.emit('accountUpdate', data);
    } else if (data.e === 'ORDER_TRADE_UPDATE') {
        console.log('📝 Sipariş güncellemesi alındı');
        wsEmitter.emit('orderUpdate', data);
    } else if (data.e === 'MARGIN_CALL') {
        console.log('⚠️ Marjin çağrısı alındı');
        wsEmitter.emit('marginCall', data);
    } else {
        console.log('📨 Diğer WebSocket mesajı alındı:', data);
    }
}

// ListenKey'i aktif tut
function startListenKeyKeepAlive() {
    // 30 dakikada bir ping gönder
    setInterval(async () => {
        if (!listenKey) return;

        try {
            console.log('🔄 ListenKey yenileniyor...');
            const response = await fetch('https://fapi.binance.com/fapi/v1/listenKey', {
                method: 'PUT',
                headers: { 'X-MBX-APIKEY': HMAC_API_KEY }
            });

            if (response.ok) {
                console.log('✅ ListenKey yenilendi');
                lastPingTime = Date.now();
            } else {
                console.error('❌ ListenKey yenilenemedi');
            }
        } catch (error) {
            console.error('❌ ListenKey yenilenirken hata:', error);
        }
    }, 30 * 60 * 1000); // 30 dakika
}

// WebSocket bağlantısını kapat
export function closeWebSocket() {
    if (accountWs) {
        console.log('🔌 WebSocket bağlantısı kapatılıyor...');
        accountWs.close();
        accountWs = null;
    }

    if (marketWs) {
        console.log('🔌 Market WebSocket bağlantısı kapatılıyor...');
        marketWs.close();
        marketWs = null;
    }

    // Zamanlayıcıyı iptal et
    if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
        reconnectTimeout = null;
    }

    wsStatus.isConnected = false;
    wsStatus.hasAccountInfo = false;

    console.log('✅ WebSocket bağlantıları kapatıldı');
}

// Market verilerine abone ol
export function subscribeToStream(symbol: string, streamType: string) {
    const stream = `${symbol.toLowerCase()}@${streamType}`;
    subscribeToMarketStream(stream);
    return () => unsubscribeFromMarketStream(stream);
}
