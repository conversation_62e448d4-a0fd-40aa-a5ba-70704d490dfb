import { EventEmitter } from 'eventemitter3';

// İşlem modu tipleri
export type TradingMode = 'REAL' | 'MANUAL' | 'SIMULATION';

// Event emitter
const modeEmitter = new EventEmitter();

// Aktif işlem modu
let currentMode: TradingMode = 'MANUAL';

// Debug bilgisi
console.log('🔄 Mode Store yükleniyor...');

/**
 * İşlem modunu ayarla
 * @param mode Yeni işlem modu
 */
export function setMode(mode: TradingMode): void {
    const oldMode = currentMode;
    currentMode = mode;

    // Mode değiştiğinde event yayınla
    if (oldMode !== mode) {
        console.log(`🔮 İşlem modu değişti: ${oldMode} => ${mode}`);
        modeEmitter.emit('modeChanged', mode, oldMode);
    }

    // Mod bilgisini konsola yaz
    console.log(`🧠 REAL MODE ${mode === 'REAL' ? 'ACTIVE' : 'INACTIVE'}: Using fetched API data`);

    if (mode === 'MANUAL') {
        console.log(`📝 REAL MODE (MANUAL): Final balance = $ 0.00`);
    }
}

/**
 * Aktif işlem modunu al
 * @returns Aktif işlem modu
 */
export function getMode(): TradingMode {
    return currentMode;
}

/**
 * İşlem modu değiştiğinde çalışacak fonksiyon ekle
 * @param listener Değişiklik olduğunda çalışacak fonksiyon
 * @returns Dinleyiciyi kaldırmak için fonksiyon
 */
export function onModeChanged(listener: (mode: TradingMode, oldMode: TradingMode) => void): () => void {
    modeEmitter.on('modeChanged', listener);
    return () => modeEmitter.off('modeChanged', listener);
}

/**
 * İşlem gerçek modda mı çalışıyor kontrol et
 * @returns Gerçek modda çalışıyorsa true
 */
export function isRealMode(): boolean {
    return currentMode === 'REAL';
}

/**
 * İşlem manuel modda mı çalışıyor kontrol et
 * @returns Manuel modda çalışıyorsa true
 */
export function isManualMode(): boolean {
    return currentMode === 'MANUAL';
}

/**
 * İşlem simülasyon modunda mı çalışıyor kontrol et
 * @returns Simülasyon modunda çalışıyorsa true
 */
export function isSimulationMode(): boolean {
    return currentMode === 'SIMULATION';
} 
