// Bakiye bilgileri
export interface Balance {
    asset: string;
    walletBalance: number;
    unrealizedProfit: number;
    marginBalance: number;
    maintMargin: number;
    initialMargin: number;
    positionInitialMargin: number;
    openOrderInitialMargin: number;
    crossWalletBalance: number;
    crossUnPnl: number;
    availableBalance: number;
    maxWithdrawAmount: number;
    marginAvailable: boolean;
    updateTime: number;
}

// Pozisyon bilgileri
export interface Position {
    symbol: string;
    positionSide: string;
    positionAmt: number;
    entryPrice: number;
    breakEvenPrice: number;
    markPrice: number;
    unrealizedProfit: number;
    liquidationPrice: number;
    isolatedMargin: number;
    notional: number;
    marginAsset: string;
    isolatedWallet: number;
    initialMargin: number;
    maintMargin: number;
    positionInitialMargin: number;
    openOrderInitialMargin: number;
    adl: number;
    bidNotional: number;
    askNotional: number;
    updateTime: number;
}

// Hesap bilgileri
export interface AccountInfo {
    feeTier: number;
    canTrade: boolean;
    canDeposit: boolean;
    canWithdraw: boolean;
    updateTime: number;
    totalInitialMargin: string;
    totalMaintMargin: string;
    totalWalletBalance: string;
    totalUnrealizedProfit: string;
    totalMarginBalance: string;
    totalPositionInitialMargin: string;
    totalOpenOrderInitialMargin: string;
    totalCrossWalletBalance: string;
    totalCrossUnPnl: string;
    availableBalance: string;
    maxWithdrawAmount: string;
    assets: AssetInfo[];
    positions: PositionInfo[];
}

// Varlık bilgisi
export interface AssetInfo {
    asset: string;
    walletBalance: string;
    unrealizedProfit: string;
    marginBalance: string;
    maintMargin: string;
    initialMargin: string;
    positionInitialMargin: string;
    openOrderInitialMargin: string;
    crossWalletBalance: string;
    crossUnPnl: string;
    availableBalance: string;
    maxWithdrawAmount: string;
    marginAvailable: boolean;
    updateTime: number;
}

// Pozisyon bilgisi
export interface PositionInfo {
    symbol: string;
    initialMargin: string;
    maintMargin: string;
    unrealizedProfit: string;
    positionInitialMargin: string;
    openOrderInitialMargin: string;
    leverage: string;
    isolated: boolean;
    entryPrice: string;
    maxNotional: string;
    positionSide: string;
    positionAmt: string;
    notional: string;
    isolatedWallet: string;
    updateTime: number;
    bidNotional: string;
    askNotional: string;
}

// Hesap stream verisi
export interface UserAccountStream {
    e: string; // Olay tipi
    E: number; // Olay zamanı
    a: AccountUpdate; // Hesap güncellemesi
}

// Hesap güncellemesi
export interface AccountUpdate {
    m: string; // Değişiklik sebebi
    B: BalanceUpdate[]; // Bakiye güncellemeleri
    P: PositionUpdate[]; // Pozisyon güncellemeleri
}

// Bakiye güncellemesi
export interface BalanceUpdate {
    a: string; // Varlık
    wb: string; // Cüzdan bakiyesi
    cw: string; // Cross cüzdan bakiyesi
}

// Pozisyon güncellemesi
export interface PositionUpdate {
    s: string; // Sembol
    pa: string; // Pozisyon miktarı
    ep: string; // Giriş fiyatı
    cr: string; // Realize edilmiş kar
    up: string; // Realize edilmemiş kar
    mt: string; // Pozisyon marjin tipi
    iw: string; // İzole edilmiş cüzdan (sadece izole pozisyonlar için)
    ps: string; // Pozisyon tarafı
}

// Callback türü
export type AccountInfoCallback = (accountInfo: AccountInfo | null, error?: any) => void; 
