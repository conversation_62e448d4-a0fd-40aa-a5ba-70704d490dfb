export interface Trade {
    id: string;
    symbol: string;
    direction: 'long' | 'short';
    entryPrice: number;
    exitPrice?: number;
    quantity: number;
    leverage: number;
    status: 'open' | 'closed';
    openTime: Date;
    closeTime?: Date;
    profitTarget: number;
    stopLoss: number;
    actualProfitAmount?: number;
    profitPercentage?: number;
    fees: number;

    // Açık pozisyonlar için ek alanlar
    currentPrice?: number;
    unrealizedProfit?: number;
    takeProfitPrice?: number;
    stopLossPrice?: number;
} 