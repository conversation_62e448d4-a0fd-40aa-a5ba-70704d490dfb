/**
 * Config loader utility for loading API credentials from config files
 */

// Import doğrudan ES modül syntax'i ile yapılmalı
import credentials from '../config/binance-credentials.json';

// API anahtarları
let apiKey: string = '';
let apiSecret: string = '';
let ed25519ApiKey: string = '';
let privateKeyHex: string = '';

// Credentials yükleme - runtime check ile
const loadCredentials = (): void => {
    try {
        // Credentials nesnesini doğrudan kullan
        if (credentials) {
            apiKey = credentials.apiKey || '';
            apiSecret = credentials.apiSecret || '';
            ed25519ApiKey = credentials.ed25519ApiKey || '';
            privateKeyHex = credentials.privateKeyHex || '';

            console.log('✅ API kimlik bilgileri başarıyla yüklendi');

            if (!apiKey || !apiSecret) {
                console.warn('⚠️ API kimlik bilgileri eksik veya boş');
            }
        } else {
            console.warn('⚠️ Binance credentials dosyası yüklenemedi, var<PERSON><PERSON><PERSON> değerler kullanılacak');
        }
    } catch (error) {
        console.error('❌ API kimlik bilgileri yüklenirken hata oluştu:', error);
    }
};

// Başlangıçta yükle
loadCredentials();

// Getter fonksiyonları - senkron
export const getApiKey = (): string => apiKey;
export const getApiSecret = (): string => apiSecret;
export const getEd25519ApiKey = (): string => ed25519ApiKey || apiKey; // Ed25519 yoksa normal API key kullan
export const getPrivateKeyHex = (): string => privateKeyHex;

// Credentials güncelleyici
export const updateCredentials = (newCredentials: {
    apiKey?: string;
    apiSecret?: string;
    ed25519ApiKey?: string;
    privateKeyHex?: string;
}): void => {
    if (newCredentials.apiKey) apiKey = newCredentials.apiKey;
    if (newCredentials.apiSecret) apiSecret = newCredentials.apiSecret;
    if (newCredentials.ed25519ApiKey) ed25519ApiKey = newCredentials.ed25519ApiKey;
    if (newCredentials.privateKeyHex) privateKeyHex = newCredentials.privateKeyHex;

    console.log('✅ API kimlik bilgileri başarıyla güncellendi');
};

/**
 * Tüm API kimlik bilgilerini temizler
 */
export const clearCredentials = (): void => {
    apiKey = '';
    apiSecret = '';
    ed25519ApiKey = '';
    privateKeyHex = '';

    console.log('🧹 API kimlik bilgileri temizlendi');
};

// Export default
export default {
    getApiKey,
    getApiSecret,
    getEd25519ApiKey,
    getPrivateKeyHex,
    updateCredentials,
    clearCredentials
}; 