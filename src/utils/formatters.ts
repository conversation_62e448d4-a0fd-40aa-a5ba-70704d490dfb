/**
 * Para birimlerini formatlayan yardımcı fonksiyon
 * @param {number} value - Formatlanacak sayısal de<PERSON>
 * @param {number} decimals - Gösterilecek ondalık basamak sayısı
 * @param {string} currency - Para birimi sembolü
 * @returns {string} Formatlanmış para değeri
 */
export const formatCurrency = (value, decimals = 2, currency = '$') => {
    if (value === undefined || value === null) return `${currency}0.00`;

    // Sıfır değilse formatla
    if (Math.abs(value) < 0.01 && value !== 0) {
        return `${currency}${value.toFixed(6)}`;
    }

    // Normal format
    return `${currency}${value.toFixed(decimals)}`;
};

/**
 * Yüzde değerlerini formatlayan yardımcı fonksiyon
 * @param {number} value - Formatlanacak yüzde değeri (0.01 = %1)
 * @param {number} decimals - Gösterilecek ondalık basamak sayısı
 * @returns {string} Formatlanmış yüzde değeri
 */
export const formatPercent = (value, decimals = 2) => {
    if (value === undefined || value === null) return '0.00%';

    // Yüzde olarak göster (0.01 -> %1.00)
    const percentValue = value * 100;

    if (Math.abs(percentValue) < 0.01 && percentValue !== 0) {
        return `${percentValue.toFixed(4)}%`;
    }

    return `${percentValue.toFixed(decimals)}%`;
};

/**
 * Sayıları kısaltarak formatlayan yardımcı fonksiyon (1000 -> 1K, 1000000 -> 1M)
 * @param {number} value - Formatlanacak sayı
 * @param {number} decimals - Gösterilecek ondalık basamak sayısı
 * @returns {string} Kısaltılmış ve formatlanmış sayı
 */
export const formatCompactNumber = (value, decimals = 2) => {
    if (value === undefined || value === null) return '0';

    const absValue = Math.abs(value);
    const sign = value < 0 ? '-' : '';

    if (absValue >= 1000000000) {
        return `${sign}${(value / 1000000000).toFixed(decimals)}B`;
    }
    if (absValue >= 1000000) {
        return `${sign}${(value / 1000000).toFixed(decimals)}M`;
    }
    if (absValue >= 1000) {
        return `${sign}${(value / 1000).toFixed(decimals)}K`;
    }

    return `${sign}${value.toFixed(decimals)}`;
};

/**
 * Zamanı formatlayan yardımcı fonksiyon
 * @param {Date|number} date - Date nesnesi veya timestamp 
 * @param {boolean} includeSeconds - Saniyeleri dahil et
 * @returns {string} Formatlanmış zaman
 */
export const formatTime = (date, includeSeconds = true) => {
    if (!date) return '';

    const d = date instanceof Date ? date : new Date(date);

    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return includeSeconds
        ? `${hours}:${minutes}:${seconds}`
        : `${hours}:${minutes}`;
};

/**
 * Tarihi formatlayan yardımcı fonksiyon
 * @param {Date|number} date - Date nesnesi veya timestamp
 * @returns {string} Formatlanmış tarih
 */
export const formatDate = (date) => {
    if (!date) return '';

    const d = date instanceof Date ? date : new Date(date);

    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();

    return `${day}.${month}.${year}`;
}; 