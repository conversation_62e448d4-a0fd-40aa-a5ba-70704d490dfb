import crypto from 'crypto';

// Debug bilgisi
console.log('🔐 Signature modülü yükleniyor...');

// API Anahtarları
const API_SECRET = process.env.REACT_APP_BINANCE_API_SECRET || '';

/**
 * HMAC SHA256 imzası oluşturur
 * @param queryString İmzalanacak sorgu metni
 * @param secretKey API gizli anahtarı
 * @returns İmza (hex formatında)
 */
export function createSignature(queryString: string, secretKey: string): string {
    const signature = crypto
        .createHmac('sha256', secretKey)
        .update(queryString)
        .digest('hex');

    return signature;
}

// Bu dosya artık kullanılmadığı için boştur.
// Referans olması için tutuyoruz, gelecekte imza oluşturma algoritmaları değişirse burada implemente edilebilir. 
