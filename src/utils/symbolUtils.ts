/**
 * Sembolün perpetual kontrat olup olmadığını kontrol et
 */
export function isPerpetualSymbol(symbol: string): boolean {
    // Future sembollerinde genellikle USDT, BUSD, USDC gibi ifadeler bulunur
    // Bu örnekte USDT future'ları için kontrol yapıyoruz
    const perpetualSuffixes = ['USDT', 'BUSD', 'USDC', 'USD'];

    // Sembol içinde bu ifadelerden biri varsa ve perp değilse kontrol et
    if (perpetualSuffixes.some(suffix => symbol.endsWith(suffix))) {
        // Aşağıdaki semboller bilerek test için hariç tutuluyor
        const excludedSymbols = ['BTCUSDT_230630', 'ETHUSDT_230630'];

        // Hariç tutulan semboller listesinde değilse perpetual kabul et
        return !excludedSymbols.includes(symbol);
    }

    return false;
}

// Sembol bilgisinden lotSize hesapla
export function calculateLotSize(symbol: string, price: number): number {
    // Varsayılan lot büyüklüğü 0.001 BTC veya eşdeğeri
    let lotSize = 0.001;

    if (symbol.startsWith('BTC')) {
        lotSize = 0.001;
    } else if (symbol.startsWith('ETH')) {
        lotSize = 0.01;
    } else if (symbol.startsWith('BNB')) {
        lotSize = 0.01;
    } else if (symbol.includes('1000')) {
        // 1000x token'lar için daha yüksek lot
        lotSize = 1;
    } else if (price < 1) {
        // Düşük fiyatlı token'lar için daha yüksek lot
        lotSize = 10;
    } else if (price < 10) {
        lotSize = 1;
    } else if (price < 100) {
        lotSize = 0.1;
    } else if (price < 1000) {
        lotSize = 0.01;
    } else {
        lotSize = 0.001;
    }

    return lotSize;
}

// Sembol adını standartlaştır
export function standardizeSymbol(symbol: string): string {
    // Boşlukları ve özel karakterleri temizle
    let standardized = symbol.trim().replace(/[^a-zA-Z0-9]/g, '');

    // Hepsini büyük harfe dönüştür
    standardized = standardized.toUpperCase();

    // USDT ekle (eğer yoksa)
    if (!standardized.endsWith('USDT') &&
        !standardized.endsWith('BUSD') &&
        !standardized.endsWith('USDC') &&
        !standardized.endsWith('USD')) {
        standardized += 'USDT';
    }

    return standardized;
}

// Fiyat formatla
export function formatPrice(price: number, symbol: string): string {
    let decimals = 2;

    if (symbol.startsWith('BTC')) {
        decimals = 1;
    } else if (price < 0.1) {
        decimals = 6;
    } else if (price < 1) {
        decimals = 5;
    } else if (price < 10) {
        decimals = 4;
    } else if (price < 100) {
        decimals = 3;
    } else if (price < 1000) {
        decimals = 2;
    } else {
        decimals = 1;
    }

    return price.toFixed(decimals);
}

// Debug bilgisi
console.log('🔄 Symbol Utils yükleniyor...'); 
