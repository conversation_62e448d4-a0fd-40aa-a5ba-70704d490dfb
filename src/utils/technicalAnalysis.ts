// Teknik analiz için yardımcı fonksiyonlar

// Hareketli ortalama türleri
export enum MAType {
    SMA = 'SMA', // Simple Moving Average
    EMA = 'EMA', // Exponential Moving Average
    WMA = 'WMA'  // Weighted Moving Average
}

// Hareketli ortalama hesapla
export function calculateMA(
    prices: number[],
    period: number,
    type: MAType = MAType.SMA
): number[] {
    if (prices.length < period) {
        return [];
    }

    const result: number[] = [];

    switch (type) {
        case MAType.SMA:
            return calculateSMA(prices, period);
        case MAType.EMA:
            return calculateEMA(prices, period);
        case MAType.WMA:
            return calculateWMA(prices, period);
        default:
            return calculateSMA(prices, period);
    }

    return result;
}

// Basit Hareketli Ortalama (SMA) hesapla
export function calculateSMA(prices: number[], period: number): number[] {
    const result: number[] = [];

    for (let i = period - 1; i < prices.length; i++) {
        let sum = 0;
        for (let j = 0; j < period; j++) {
            sum += prices[i - j];
        }
        result.push(sum / period);
    }

    return result;
}

// Üstel Hareketli Ortalama (EMA) hesapla
export function calculateEMA(prices: number[], period: number): number[] {
    const result: number[] = [];
    const multiplier = 2 / (period + 1);

    // İlk EMA değeri olarak SMA kullan
    let ema = prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;
    result.push(ema);

    // Diğer EMA değerlerini hesapla
    for (let i = period; i < prices.length; i++) {
        ema = (prices[i] - ema) * multiplier + ema;
        result.push(ema);
    }

    return result;
}

// Ağırlıklı Hareketli Ortalama (WMA) hesapla
export function calculateWMA(prices: number[], period: number): number[] {
    const result: number[] = [];
    const weights: number[] = [];

    // Ağırlıkları hesapla
    let weightSum = 0;
    for (let i = 1; i <= period; i++) {
        weights.push(i);
        weightSum += i;
    }

    // Her dönem için WMA hesapla
    for (let i = period - 1; i < prices.length; i++) {
        let sum = 0;
        for (let j = 0; j < period; j++) {
            sum += prices[i - j] * weights[period - j - 1];
        }
        result.push(sum / weightSum);
    }

    return result;
}

// RSI (Relative Strength Index) hesapla
export function calculateRSI(prices: number[], period: number = 14): number[] {
    if (prices.length < period + 1) {
        return [];
    }

    const result: number[] = [];
    const changes: number[] = [];

    // Fiyat değişimlerini hesapla
    for (let i = 1; i < prices.length; i++) {
        changes.push(prices[i] - prices[i - 1]);
    }

    // İlk RS değerini hesapla
    let gainSum = 0;
    let lossSum = 0;

    for (let i = 0; i < period; i++) {
        if (changes[i] >= 0) {
            gainSum += changes[i];
        } else {
            lossSum += Math.abs(changes[i]);
        }
    }

    let avgGain = gainSum / period;
    let avgLoss = lossSum / period;
    let rs = avgGain / (avgLoss === 0 ? 0.001 : avgLoss); // Sıfıra bölümü engellemek için
    let rsi = 100 - (100 / (1 + rs));
    result.push(rsi);

    // Diğer RSI değerlerini hesapla
    for (let i = period; i < changes.length; i++) {
        const change = changes[i];
        const gain = change >= 0 ? change : 0;
        const loss = change < 0 ? Math.abs(change) : 0;

        // Wilder's smoothing yöntemi
        avgGain = ((avgGain * (period - 1)) + gain) / period;
        avgLoss = ((avgLoss * (period - 1)) + loss) / period;

        rs = avgGain / (avgLoss === 0 ? 0.001 : avgLoss);
        rsi = 100 - (100 / (1 + rs));
        result.push(rsi);
    }

    return result;
}

// MACD (Moving Average Convergence Divergence) hesapla
export function calculateMACD(
    prices: number[],
    fastPeriod: number = 12,
    slowPeriod: number = 26,
    signalPeriod: number = 9
): { macd: number[], signal: number[], histogram: number[] } {
    // Hızlı ve yavaş EMA'ları hesapla
    const fastEMA = calculateEMA(prices, fastPeriod);
    const slowEMA = calculateEMA(prices, slowPeriod);

    // MACD çizgisini hesapla (hızlı EMA - yavaş EMA)
    const macd: number[] = [];
    // slowEMA daha az elemana sahip olduğu için, slowEMA'nın uzunluğuna göre hesapla
    const startIndex = fastEMA.length - slowEMA.length;
    for (let i = 0; i < slowEMA.length; i++) {
        macd.push(fastEMA[i + startIndex] - slowEMA[i]);
    }

    // Sinyal çizgisini hesapla (MACD'nin EMA'sı)
    const signal = calculateEMA(macd, signalPeriod);

    // Histogram hesapla (MACD - sinyal)
    const histogram: number[] = [];
    const signalStartIndex = macd.length - signal.length;
    for (let i = 0; i < signal.length; i++) {
        histogram.push(macd[i + signalStartIndex] - signal[i]);
    }

    return { macd, signal, histogram };
}

// Bollinger Bantları hesapla
export function calculateBollingerBands(
    prices: number[],
    period: number = 20,
    multiplier: number = 2
): { middle: number[], upper: number[], lower: number[] } {
    const middle = calculateSMA(prices, period);
    const upper: number[] = [];
    const lower: number[] = [];

    for (let i = period - 1; i < prices.length; i++) {
        // Standart sapmayı hesapla
        let sum = 0;
        for (let j = 0; j < period; j++) {
            sum += Math.pow(prices[i - j] - middle[i - (period - 1)], 2);
        }
        const stdDev = Math.sqrt(sum / period);

        // Üst ve alt bantları hesapla
        upper.push(middle[i - (period - 1)] + (multiplier * stdDev));
        lower.push(middle[i - (period - 1)] - (multiplier * stdDev));
    }

    return { middle, upper, lower };
}

// MA kesişimlerini tespit et
export function detectMACrossover(
    fastMA: number[],
    slowMA: number[]
): { crossovers: { index: number, type: 'bullish' | 'bearish' }[] } {
    if (fastMA.length !== slowMA.length) {
        throw new Error('MA dizilerinin uzunlukları eşit olmalıdır');
    }

    const crossovers: { index: number, type: 'bullish' | 'bearish' }[] = [];

    for (let i = 1; i < fastMA.length; i++) {
        // Bullish crossover: Hızlı MA, yavaş MA'nın üzerine çıktı
        if (fastMA[i - 1] <= slowMA[i - 1] && fastMA[i] > slowMA[i]) {
            crossovers.push({ index: i, type: 'bullish' });
        }
        // Bearish crossover: Hızlı MA, yavaş MA'nın altına düştü
        else if (fastMA[i - 1] >= slowMA[i - 1] && fastMA[i] < slowMA[i]) {
            crossovers.push({ index: i, type: 'bearish' });
        }
    }

    return { crossovers };
}

// Belirli bir yüzde değişimle kesişimleri tespit et
export function detectMACrossoverWithThreshold(
    fastMA: number[],
    slowMA: number[],
    threshold: number = 0.0 // Örneğin: 0.01 = %1
): { crossovers: { index: number, type: 'bullish' | 'bearish', percentage: number }[] } {
    if (fastMA.length !== slowMA.length) {
        throw new Error('MA dizilerinin uzunlukları eşit olmalıdır');
    }

    const crossovers: { index: number, type: 'bullish' | 'bearish', percentage: number }[] = [];

    for (let i = 1; i < fastMA.length; i++) {
        // Yüzde değişimi hesapla
        const percentage = (fastMA[i] - slowMA[i]) / slowMA[i];

        // Bullish crossover with threshold
        if (fastMA[i - 1] <= slowMA[i - 1] && fastMA[i] > slowMA[i] && Math.abs(percentage) >= threshold) {
            crossovers.push({ index: i, type: 'bullish', percentage });
        }
        // Bearish crossover with threshold
        else if (fastMA[i - 1] >= slowMA[i - 1] && fastMA[i] < slowMA[i] && Math.abs(percentage) >= threshold) {
            crossovers.push({ index: i, type: 'bearish', percentage });
        }
    }

    return { crossovers };
}

// Fiyat verilerinden kapanış fiyatlarını çıkart
export function extractClosePrices(klineData: any[]): number[] {
    return klineData.map(kline => parseFloat(kline.closePrice || kline.c || kline));
}

// MACD kesişimlerini tespit et
export function detectMACDSignalCrossover(
    macd: number[],
    signal: number[]
): { crossovers: { index: number, type: 'bullish' | 'bearish' }[] } {
    if (macd.length !== signal.length) {
        throw new Error('MACD ve Sinyal dizilerinin uzunlukları eşit olmalıdır');
    }

    const crossovers: { index: number, type: 'bullish' | 'bearish' }[] = [];

    for (let i = 1; i < macd.length; i++) {
        // Bullish crossover: MACD, Sinyal'ın üzerine çıktı
        if (macd[i - 1] <= signal[i - 1] && macd[i] > signal[i]) {
            crossovers.push({ index: i, type: 'bullish' });
        }
        // Bearish crossover: MACD, Sinyal'ın altına düştü
        else if (macd[i - 1] >= signal[i - 1] && macd[i] < signal[i]) {
            crossovers.push({ index: i, type: 'bearish' });
        }
    }

    return { crossovers };
}

// RSI aşırı alım/satım seviyelerini tespit et
export function detectRSIExtremes(
    rsi: number[],
    overbought: number = 70,
    oversold: number = 30
): { extremes: { index: number, type: 'overbought' | 'oversold' }[] } {
    const extremes: { index: number, type: 'overbought' | 'oversold' }[] = [];

    for (let i = 1; i < rsi.length; i++) {
        // Aşırı alım bölgesinden çıkış
        if (rsi[i - 1] >= overbought && rsi[i] < overbought) {
            extremes.push({ index: i, type: 'overbought' });
        }
        // Aşırı satım bölgesinden çıkış
        else if (rsi[i - 1] <= oversold && rsi[i] > oversold) {
            extremes.push({ index: i, type: 'oversold' });
        }
    }

    return { extremes };
}

// MA kesişimlerini threshold ile tespit et (daha basit ve doğrudan)
export function detectMAIntersection(
    prevShortMA: number,
    currentShortMA: number,
    prevLongMA: number,
    currentLongMA: number,
    threshold: number = 0.0
): 'up' | 'down' | null {
    // Kesişim yüzdesi hesapla
    const percentage = (currentShortMA - currentLongMA) / currentLongMA;

    // Yukarı kesişim (kısa MA, uzun MA'nın üzerine çıktı)
    if (prevShortMA <= prevLongMA && currentShortMA > currentLongMA && Math.abs(percentage) >= threshold) {
        return 'up';
    }
    // Aşağı kesişim (kısa MA, uzun MA'nın altına düştü)
    else if (prevShortMA >= prevLongMA && currentShortMA < currentLongMA && Math.abs(percentage) >= threshold) {
        return 'down';
    }

    return null;
}

// YENİ: Gelişmiş momentum analizi
export function analyzeMomentum(
    prices: number[],
    period: number = 5
): {
    direction: 'bullish' | 'bearish' | 'neutral';
    strength: number; // 0-10 arası
    velocity: number; // Değişim hızı
} {
    if (prices.length < period) {
        return { direction: 'neutral', strength: 0, velocity: 0 };
    }

    const recentPrices = prices.slice(-period);
    const first = recentPrices[0];
    const last = recentPrices[recentPrices.length - 1];

    // Genel trend yönü
    const totalChange = (last - first) / first;

    // Velocity hesaplama (ortalama değişim)
    let totalVelocity = 0;
    for (let i = 1; i < recentPrices.length; i++) {
        const change = (recentPrices[i] - recentPrices[i - 1]) / recentPrices[i - 1];
        totalVelocity += Math.abs(change);
    }
    const avgVelocity = totalVelocity / (recentPrices.length - 1);

    // Güç hesaplama
    const strength = Math.min(Math.abs(totalChange) * 1000, 10); // 0-10 arası normalize et

    return {
        direction: totalChange > 0.001 ? 'bullish' : totalChange < -0.001 ? 'bearish' : 'neutral',
        strength,
        velocity: avgVelocity * 1000 // Daha okunabilir hale getir
    };
}

// YENİ: Basit RSI hesaplama
export function calculateSimpleRSI(
    prices: number[],
    period: number = 14
): number {
    if (prices.length < period + 1) return 50; // Neutral RSI

    const changes = [];
    for (let i = 1; i < prices.length; i++) {
        changes.push(prices[i] - prices[i - 1]);
    }

    const recentChanges = changes.slice(-period);
    const gains = recentChanges.filter(change => change > 0);
    const losses = recentChanges.filter(change => change < 0).map(loss => Math.abs(loss));

    const avgGain = gains.length > 0 ? gains.reduce((sum, gain) => sum + gain, 0) / gains.length : 0;
    const avgLoss = losses.length > 0 ? losses.reduce((sum, loss) => sum + loss, 0) / losses.length : 0;

    if (avgLoss === 0) return 100;

    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));

    return rsi;
}

// YENİ: Kombinasyonlu sinyal sistemi
export function detectCombinedSignals(
    prices: number[],
    ma: number,
    options: {
        minMomentumStrength?: number;
        rsiOverbought?: number;
        rsiOversold?: number;
        momentumPeriod?: number;
    } = {}
): {
    signal: 'strong_long' | 'long' | 'weak_long' | 'neutral' | 'weak_short' | 'short' | 'strong_short';
    confidence: number; // 0-100 arası
    reasons: string[];
} {
    if (prices.length < 5) {
        return { signal: 'neutral', confidence: 0, reasons: ['Yetersiz veri'] };
    }

    const currentPrice = prices[prices.length - 1];
    const reasons: string[] = [];
    let signalPoints = 0;
    let confidence = 0;

    // MA ile karşılaştırma
    const priceVsMA = (currentPrice - ma) / ma;
    if (Math.abs(priceVsMA) > 0.002) { // %0.2 eşik
        if (priceVsMA > 0) {
            signalPoints += 2;
            reasons.push(`Fiyat MA üzerinde (+${(priceVsMA * 100).toFixed(2)}%)`);
        } else {
            signalPoints -= 2;
            reasons.push(`Fiyat MA altında (${(priceVsMA * 100).toFixed(2)}%)`);
        }
        confidence += 40;
    }

    // Fiyat değişimi kontrolü
    const recentChange = (currentPrice - prices[prices.length - 5]) / prices[prices.length - 5];
    if (Math.abs(recentChange) > 0.005) { // %0.5 eşik
        if (recentChange > 0) {
            signalPoints += 1;
            reasons.push(`Fiyat yükseliyor (+${(recentChange * 100).toFixed(2)}%)`);
        } else {
            signalPoints -= 1;
            reasons.push(`Fiyat düşüyor (${(recentChange * 100).toFixed(2)}%)`);
        }
        confidence += 20;
    }

    // Sinyal belirleme
    let signal: 'strong_long' | 'long' | 'weak_long' | 'neutral' | 'weak_short' | 'short' | 'strong_short';

    if (signalPoints >= 3) {
        signal = 'strong_long';
    } else if (signalPoints >= 2) {
        signal = 'long';
    } else if (signalPoints >= 1) {
        signal = 'weak_long';
    } else if (signalPoints <= -3) {
        signal = 'strong_short';
    } else if (signalPoints <= -2) {
        signal = 'short';
    } else if (signalPoints <= -1) {
        signal = 'weak_short';
    } else {
        signal = 'neutral';
    }

    return {
        signal,
        confidence: Math.min(confidence, 100),
        reasons
    };
} 
