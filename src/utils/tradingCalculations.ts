// Trading calculations utility

// Binance futures trading fee rates
const STANDARD_TAKER_FEE_RATE = 0.0002; // 0.02% standard taker fee
const VIP_TAKER_FEE_RATE = 0.00018; // 0.018% VIP level taker fee (-10%)
const STANDARD_MAKER_FEE_RATE = 0.00015; // 0.015% standard maker fee
const VIP_MAKER_FEE_RATE = 0.000135; // 0.0135% VIP level maker fee (-10%)

export const calculateCommissionFee = (
  tradeAmount: number,
  isVIP: boolean = false,
  isMaker: boolean = false
): number => {
  if (!tradeAmount || tradeAmount <= 0) {
    return 0;
  }

  let feeRate;
  if (isMaker) {
    feeRate = isVIP ? VIP_MAKER_FEE_RATE : STANDARD_MAKER_FEE_RATE;
  } else {
    feeRate = isVIP ? VIP_TAKER_FEE_RATE : STANDARD_TAKER_FEE_RATE;
  }

  const fee = tradeAmount * feeRate;
  console.log(`Commission calculation: Amount $${tradeAmount.toFixed(6)}, Rate ${(feeRate * 100).toFixed(4)}%, Fee $${fee.toFixed(6)}`);

  return fee;
};

export const calculateTotalFees = (
  entryAmount: number,
  exitAmount: number,
  isVIP: boolean = false,
  fundingFee: number = 0,
  entryIsMaker: boolean = false,
  exitIsMaker: boolean = false
): {
  entryCommission: number,
  exitCommission: number,
  totalCommission: number,
  totalFees: number
} => {
  if (!entryAmount || entryAmount <= 0) {
    entryAmount = 0;
  }

  if (!exitAmount || exitAmount <= 0) {
    exitAmount = 0;
  }

  console.log(`Hesaplama: Giriş tutarı: ${entryAmount.toFixed(6)}, Çıkış tutarı: ${exitAmount.toFixed(6)}`);

  const entryCommission = calculateCommissionFee(entryAmount, isVIP, entryIsMaker);
  const exitCommission = calculateCommissionFee(exitAmount, isVIP, exitIsMaker);
  const totalCommission = entryCommission + exitCommission;
  const totalFees = totalCommission + (fundingFee || 0);

  console.log(`Komisyon hesaplandı: Giriş: ${entryCommission.toFixed(6)}, Çıkış: ${exitCommission.toFixed(6)}, Toplam: ${totalCommission.toFixed(6)}, Tüm Ücretler: ${totalFees.toFixed(6)}`);

  return {
    entryCommission,
    exitCommission,
    totalCommission,
    totalFees
  };
};

// Check if a coin is newly listed (less than N days old)
export const isNewlyListedCoin = (
  symbol: string,
  listedCoinsMap: Record<string, Date>,
  thresholdDays: number = 30
): boolean => {
  const listingDate = listedCoinsMap[symbol];

  // If we don't have listing date info, assume it's not new
  if (!listingDate) return false;

  const now = new Date();
  const diffTime = now.getTime() - listingDate.getTime();
  const diffDays = diffTime / (1000 * 60 * 60 * 24);

  return diffDays < thresholdDays;
};

// Check if total losses exceed threshold
export const exceedsTotalLossThreshold = (
  totalProfit: number,
  initialBalance: number,
  maxLossPercentage: number = 20
): boolean => {
  if (totalProfit >= 0) return false;

  const lossPercentage = (Math.abs(totalProfit) / initialBalance) * 100;
  return lossPercentage >= maxLossPercentage;
};

// Check if trade can be opened
export function canOpenTrade(
  symbol: string,
  direction: 'long' | 'short',
  configSettings: any,
  restrictions: any,
  lastTradeTime?: Date,
  fundingRates?: any
): { allowed: boolean; reason: string } {
  // 1. Time-based rate limiting
  if (lastTradeTime) {
    const now = new Date();
    const timeSinceLastTrade = (now.getTime() - lastTradeTime.getTime()) / 1000; // seconds
    if (timeSinceLastTrade < restrictions.minTradeInterval) {
      return {
        allowed: false,
        reason: `Rate limit: Son işlemden beri ${Math.floor(timeSinceLastTrade)}s geçti, min. ${restrictions.minTradeInterval}s gerekli`
      };
    }
  }

  // 2. Hacim kontrolü (minVolumeForTrade kontrolü daha genel bir şekilde yapılıyor)
  // Artık restrictLowVolumeCoins kullanılmıyor - hacim kontrolü diğer yerlerde yapılıyor

  // 3. MA200 ve hacim/fiyat uyumsuzluğu kontrolleri kaldırıldı

  // 4. Funding rate checks for short positions
  if (direction === 'short' &&
    restrictions.avoidNegativeFundingForShort &&
    fundingRates &&
    fundingRates[symbol]) {

    const fundingRate = fundingRates[symbol].fundingRate;
    const nextFundingTime = fundingRates[symbol].nextFundingTime;

    // Check if we're approaching funding time and rate is negative
    if (fundingRate < 0) {
      const now = new Date();
      const minutesToFunding = (nextFundingTime.getTime() - now.getTime()) / (1000 * 60);

      if (minutesToFunding <= restrictions.fundingTimeThresholdMinutes) {
        return {
          allowed: false,
          reason: `Funding zamanına ${Math.floor(minutesToFunding)} dk kaldı, negatif oran (${fundingRate.toFixed(4)}%)`
        };
      }
    }
  }

  // All checks passed
  return { allowed: true, reason: 'OK' };
}

// Check time-based rate limit
export function checkRateLimit(
  lastTradeTime: Date,
  minInterval: number // in seconds
): { allowed: boolean; reason: string } {
  const now = new Date();
  const timeSinceLastTrade = (now.getTime() - lastTradeTime.getTime()) / 1000; // seconds

  if (timeSinceLastTrade < minInterval) {
    return {
      allowed: false,
      reason: `Rate limit: Son işlemden beri ${Math.floor(timeSinceLastTrade)}s geçti, min. ${minInterval}s gerekli`
    };
  }

  return { allowed: true, reason: 'OK' };
}

// DÜZENLEME: Günlük işlem limiti tamamen kaldırıldı
// export const checkDailyTradeLimit = (...) => { ... } // REMOVED

// DÜZENLEME: Stop-loss hesaplaması düzeltildi - Kaldıraç etkisi doğru uygulandı
export const calculateStopLossPrice = (
  entryPrice: number,
  direction: 'long' | 'short',
  stopLossPercent: number,
  leverage: number = 1
): number => {
  console.log(`🎯 Stop-loss hesaplanıyor:`);
  console.log(`   Giriş: $${entryPrice}`);
  console.log(`   Yön: ${direction}`);
  console.log(`   Stop %: ${stopLossPercent}%`);
  console.log(`   Kaldıraç: ${leverage}x`);

  let stopPrice = 0;

  // DÜZELTME: Kaldıraç stop-loss oranını azaltır, çünkü aynı fiyat hareketinde daha büyük kayıp var
  const adjustedStopPercent = stopLossPercent / leverage;

  if (direction === 'long') {
    // Long pozisyon: giriş fiyatının altında stop-loss
    stopPrice = entryPrice * (1 - adjustedStopPercent / 100);
  } else {
    // Short pozisyon: giriş fiyatının üstünde stop-loss
    stopPrice = entryPrice * (1 + adjustedStopPercent / 100);
  }

  console.log(`   Düzeltilmiş Stop %: ${adjustedStopPercent.toFixed(4)}%`);
  console.log(`   Hesaplanan Stop: $${stopPrice}`);
  return stopPrice;
};

// Take profit fiyatını hesapla - kaldıraç etkisini doğru şekilde uygulama
export const calculateTakeProfitPrice = (
  entryPrice: number,
  direction: 'long' | 'short',
  takeProfitPercent: number,
  leverage: number = 1
): number => {
  console.log(`🎯 Take-profit hesaplanıyor:`);
  console.log(`   Giriş: $${entryPrice}`);
  console.log(`   Yön: ${direction}`);
  console.log(`   TP %: ${takeProfitPercent}%`);
  console.log(`   Kaldıraç: ${leverage}x`);

  let takeProfitPrice = 0;

  // DÜZELTME: Kaldıraç take-profit oranını azaltır
  const adjustedTPPercent = takeProfitPercent / leverage;

  if (direction === 'long') {
    // Long pozisyon: giriş fiyatının üstünde take-profit
    takeProfitPrice = entryPrice * (1 + adjustedTPPercent / 100);
  } else {
    // Short pozisyon: giriş fiyatının altında take-profit
    takeProfitPrice = entryPrice * (1 - adjustedTPPercent / 100);
  }

  console.log(`   Düzeltilmiş TP %: ${adjustedTPPercent.toFixed(4)}%`);
  console.log(`   Hesaplanan TP: $${takeProfitPrice}`);
  return takeProfitPrice;
};

// DÜZENLEME: Slippage kontrolü iyileştirildi
export const checkSlippage = (
  expectedPrice: number,
  actualPrice: number,
  maxSlippagePercent: number = 1.0
): { acceptable: boolean, slippagePercent: number, reason?: string } => {
  const slippagePercent = Math.abs((actualPrice - expectedPrice) / expectedPrice) * 100;

  console.log(`🔍 Slippage kontrolü: Beklenen $${expectedPrice}, Gerçek $${actualPrice}, Slippage %${slippagePercent.toFixed(3)}`);

  if (slippagePercent > maxSlippagePercent) {
    const reason = `Slippage çok yüksek: %${slippagePercent.toFixed(2)} (maksimum: %${maxSlippagePercent})`;
    console.warn(`⚠️ ${reason}`);
    return { acceptable: false, slippagePercent, reason };
  }

  console.log(`✅ Slippage kabul edilebilir: %${slippagePercent.toFixed(3)}`);
  return { acceptable: true, slippagePercent };
};

// DÜZENLEME: Acil durum stop loss kontrolü - kaldıraç etkisi doğru hesaplanıyor
export const checkEmergencyStopLoss = (
  currentPrice: number,
  entryPrice: number,
  direction: 'long' | 'short',
  emergencyStopLossPercent: number,
  leverage: number = 1
): { shouldTrigger: boolean, lossPercent: number } => {
  let priceChangePercent = 0;

  // Fiyat değişim yüzdesini hesapla
  if (direction === 'long') {
    // Long pozisyonda: fiyat düştüğünde zarar
    priceChangePercent = ((entryPrice - currentPrice) / entryPrice) * 100;
  } else {
    // Short pozisyonda: fiyat yükseldiğinde zarar
    priceChangePercent = ((currentPrice - entryPrice) / entryPrice) * 100;
  }

  // Kaldıraçlı zarar hesapla
  const lossPercent = Math.max(0, priceChangePercent * leverage);

  // Acil durum stop loss tetiklenmeli mi?
  const shouldTrigger = lossPercent >= emergencyStopLossPercent;

  if (shouldTrigger) {
    console.error(`🚨 ACİL DURUM STOP LOSS TETİKLENDİ:`);
    console.error(`   Sembol: ${direction} pozisyon`);
    console.error(`   Giriş: $${entryPrice}`);
    console.error(`   Güncel: $${currentPrice}`);
    console.error(`   Fiyat Değişimi: %${priceChangePercent.toFixed(2)}`);
    console.error(`   Kaldıraçlı Zarar: %${lossPercent.toFixed(2)}`);
    console.error(`   Limit: %${emergencyStopLossPercent}`);
    console.error(`   Kaldıraç: ${leverage}x`);
  } else if (lossPercent > emergencyStopLossPercent * 0.8) {
    // Limite yaklaştığında uyarı
    console.warn(`⚠️ Acil stop-loss limitine yaklaşılıyor: %${lossPercent.toFixed(2)}/%${emergencyStopLossPercent}`);
  }

  return { shouldTrigger, lossPercent };
};

// Stop-loss tetiklenme kontrolü
export const shouldTriggerStopLoss = (
  currentPrice: number,
  stopLossPrice: number,
  direction: 'long' | 'short'
): boolean => {
  if (direction === 'long') {
    // Long pozisyonda: fiyat stop-loss seviyesinin altına düştüğünde tetikle
    return currentPrice <= stopLossPrice;
  } else {
    // Short pozisyonda: fiyat stop-loss seviyesinin üstüne çıktığında tetikle
    return currentPrice >= stopLossPrice;
  }
};

// DÜZENLEME: Hacim kontrolü iyileştirildi
export const checkVolumeRequirement = (
  symbol: string,
  volume24h: number,
  minVolume: number
): { meetRequirement: boolean, reason?: string } => {
  const minVolumeUSDT = minVolume * 1000000; // Convert to USDT

  if (volume24h < minVolumeUSDT) {
    return {
      meetRequirement: false,
      reason: `${symbol} 24s hacmi çok düşük: $${(volume24h / 1000000).toFixed(1)}M (minimum: $${minVolume}M)`
    };
  }

  return { meetRequirement: true };
};

// Calculate coin quantity from USDT amount and price
export const calculateCoinQuantity = (
  usdtAmount: number,
  coinPrice: number,
  leverage: number = 1
): number => {
  // With leverage, we can buy more coins for the same USDT amount
  const totalValue = usdtAmount * leverage;
  return totalValue / coinPrice;
};

export const calculateUsdtValue = (
  coinQuantity: number,
  coinPrice: number
): number => {
  return coinQuantity * coinPrice;
};

export const formatCoinQuantity = (quantity: number, symbol: string): string => {
  // Different symbols have different precision requirements
  if (symbol.includes('BTC')) return quantity.toFixed(6);
  if (symbol.includes('ETH')) return quantity.toFixed(4);
  if (quantity < 0.01) return quantity.toFixed(8);
  if (quantity < 1) return quantity.toFixed(6);
  if (quantity < 10) return quantity.toFixed(4);
  return quantity.toFixed(2);
};

export const getCoinSymbol = (tradingSymbol: string): string => {
  return tradingSymbol.replace('USDT', '');
};

export const calculateTradeDetails = (
  investmentAmount: number,
  leverage: number,
  coinPrice: number,
  symbol: string
) => {
  const totalTradeValue = investmentAmount * leverage;
  const coinQuantity = totalTradeValue / coinPrice;
  const coinSymbol = getCoinSymbol(symbol);

  return {
    totalTradeValue,
    coinQuantity,
    coinSymbol,
    formattedQuantity: formatCoinQuantity(coinQuantity, symbol)
  };
};

export const formatPrice = (price: number, pricePrecision?: number): string => {
  if (pricePrecision !== undefined) {
    return price.toFixed(pricePrecision);
  }

  // Otomatik hassasiyet tayini
  if (price < 0.0001) return price.toFixed(8);
  if (price < 0.01) return price.toFixed(6);
  if (price < 1) return price.toFixed(5);
  if (price < 10) return price.toFixed(4);
  if (price < 1000) return price.toFixed(2);
  return price.toLocaleString();
};

export const formatQuantity = (quantity: number, quantityPrecision?: number): string => {
  if (quantityPrecision !== undefined) {
    return quantity.toFixed(quantityPrecision);
  }
  return quantity.toFixed(3); // Varsayılan olarak 3 basamak hassasiyet
};

export const normalizePriceForSymbol = (price: number, pricePrecision: number): number => {
  const multiplier = Math.pow(10, pricePrecision);
  return Math.floor(price * multiplier) / multiplier;
};

export const normalizeQuantityForSymbol = (quantity: number, quantityPrecision: number): number => {
  const multiplier = Math.pow(10, quantityPrecision);
  return Math.floor(quantity * multiplier) / multiplier;
};

// İşlem hesaplamaları için yardımcı fonksiyonlar
import { Trade } from "../types/trading";

// İşlem için kâr/zarar hesapla
export function calculateTradeProfit(
  trade: Trade,
  currentPrice: number,
  leverage: number = 1
): {
  profitLoss: number;
  profitLossPercent: number;
  calculatedAmount: number;
} {
  if (!trade || !currentPrice) {
    return { profitLoss: 0, profitLossPercent: 0, calculatedAmount: 0 };
  }

  const { direction, entryPrice, quantity } = trade;

  // Yönlendirmeye göre kâr/zarar hesabı
  const priceDifference = direction === 'long'
    ? currentPrice - entryPrice
    : entryPrice - currentPrice;

  // Yüzde olarak kâr/zarar
  const profitLossPercent = (priceDifference / entryPrice) * 100 * leverage;

  // Miktar olarak kâr/zarar (kaldıraç dahil)
  const calculatedAmount = (priceDifference * quantity) * leverage;

  return {
    profitLoss: profitLossPercent,
    profitLossPercent,
    calculatedAmount
  };
}

// İşlem fiyatını takip et ve durdurma koşullarını kontrol et
export function monitorTradePrice(
  trade: Trade,
  currentPrice: number,
  stopLossPercent: number,
  takeProfitPercent: number,
  leverage: number = 20,
): {
  shouldClose: boolean;
  reason: 'take_profit' | 'stop_loss' | 'emergency_stop_loss' | null;
  profitLossPercent: number;
  stopLossPrice?: number;
  takeProfitPrice?: number;
} {
  if (!trade || !currentPrice) {
    return { shouldClose: false, reason: null, profitLossPercent: 0 };
  }

  const { direction, entryPrice } = trade;

  // Stop loss ve take profit fiyatlarını hesaplayalım
  const stopLossPrice = calculateStopLossPrice(entryPrice, direction, stopLossPercent, leverage);
  const takeProfitPrice = calculateTakeProfitPrice(entryPrice, direction, takeProfitPercent, leverage);

  // Yönlendirmeye göre kâr/zarar hesabı
  const priceDifference = direction === 'long'
    ? currentPrice - entryPrice
    : entryPrice - currentPrice;

  // Yüzde olarak kâr/zarar (kaldıraç dahil)
  const profitLossPercent = (priceDifference / entryPrice) * 100 * leverage;

  // Stop loss kontrolü - hesaplanan fiyatlarla kontrol edelim
  let stopLossTriggered = false;
  if (direction === 'long') {
    stopLossTriggered = currentPrice <= stopLossPrice;
  } else {
    stopLossTriggered = currentPrice >= stopLossPrice;
  }

  // Acil stop loss (2x normal stop loss) kontrolü
  const emergencyStopLoss = stopLossPercent * 2; // Acil durum stop loss değeri
  const emergencyStopTriggered = profitLossPercent <= -emergencyStopLoss;

  // Take profit kontrolü - hesaplanan fiyatlarla kontrol edelim
  let takeProfitTriggered = false;
  if (direction === 'long') {
    takeProfitTriggered = currentPrice >= takeProfitPrice;
  } else {
    takeProfitTriggered = currentPrice <= takeProfitPrice;
  }

  // Trailing stop kaldırıldı - sadece basit take profit ve stop loss kullanılıyor

  if (emergencyStopTriggered) {
    return {
      shouldClose: true,
      reason: 'emergency_stop_loss',
      profitLossPercent,
      stopLossPrice,
      takeProfitPrice
    };
  } else if (stopLossTriggered) {
    return {
      shouldClose: true,
      reason: 'stop_loss',
      profitLossPercent,
      stopLossPrice,
      takeProfitPrice
    };
  } else if (takeProfitTriggered) {
    return {
      shouldClose: true,
      reason: 'take_profit',
      profitLossPercent,
      stopLossPrice,
      takeProfitPrice
    };
  }

  return {
    shouldClose: false,
    reason: null,
    profitLossPercent,
    stopLossPrice,
    takeProfitPrice
  };
}

// Fiyat değişiminin hızını hesapla (son N güncelleme için)
export function calculatePriceVelocity(
  priceHistory: number[],
  timeIntervalMs: number
): number {
  if (priceHistory.length < 2) return 0;

  const oldestPrice = priceHistory[0];
  const latestPrice = priceHistory[priceHistory.length - 1];
  const priceDiff = latestPrice - oldestPrice;
  const timeDiffSeconds = (timeIntervalMs / 1000) * (priceHistory.length - 1);

  // Saniye başına yüzde değişim hızı
  return (priceDiff / oldestPrice) * 100 / timeDiffSeconds;
}

// İşlem için gerekli miktarı hesapla
export function calculateTradeQuantity(
  symbol: string,
  currentPrice: number,
  investmentAmount: number,
  leverage: number = 1
): number {
  if (!currentPrice || currentPrice <= 0 || !investmentAmount || investmentAmount <= 0) {
    return 0;
  }

  // Kaldıraçlı toplam değer
  const totalValue = investmentAmount * leverage;

  // Teorik miktar (tam miktar)
  const theoreticalQuantity = totalValue / currentPrice;

  // Symbol bazında doğru lotlara yuvarla
  let lotSize = 0.001; // Varsayılan minimum lot boyutu

  // BTC için özel lot boyutu
  if (symbol.includes('BTC')) {
    lotSize = 0.001;
  }
  // ETH için özel lot boyutu
  else if (symbol.includes('ETH')) {
    lotSize = 0.01;
  }
  // Küçük semboller için daha büyük lot boyutu 
  else if (['XRP', 'ADA', 'DOGE', 'MATIC'].some(s => symbol.includes(s))) {
    lotSize = 1;
  }

  // Lot boyutuna göre yuvarla (aşağı)
  const roundedQuantity = Math.floor(theoreticalQuantity / lotSize) * lotSize;

  // Kontrol: yeterli miktar var mı?
  if (roundedQuantity * currentPrice < 5) {
    console.warn(`⚠️ Calculated quantity for ${symbol} is too small: ${roundedQuantity}. Min notional value should be >= 5 USDT`);
    return 0;
  }

  return parseFloat(roundedQuantity.toFixed(8));
}

// İşlem komisyonunu hesapla
export function calculateTradingFee(
  amount: number,
  feeRate: number = 0.0004 // Varsayılan %0.04 taker fee
): number {
  return amount * feeRate;
}
